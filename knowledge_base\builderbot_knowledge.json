[{"url": "https://www.builderbot.app/en", "title": "BuilderBot.app Create a WhatsApp Chatbot, Without Limit", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/</>)\n  * [Contribute](https://www.builderbot.app/</contribute>)\n  * [Course](https://www.builderbot.app/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/</>)\n  * [Documentation](https://www.builderbot.app/</en#>)\n  * [Support](https://www.builderbot.app/</en#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/</>)\n    * [Quickstart](https://www.builderbot.app/</quickstart>)\n    * [Concepts](https://www.builderbot.app/</concepts>)\n    * [Examples](https://www.builderbot.app/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/</add-functions>)\n    * [Context](https://www.builderbot.app/</context>)\n    * [Methods](https://www.builderbot.app/</methods>)\n    * [Events](https://www.builderbot.app/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/</deploy>)\n    * [Railway](https://www.builderbot.app/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/</contribute>)\n    * [Core](https://www.builderbot.app/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/</en#>)\n\n\n# Get started with BuilderBot\nThis is a **free** and open source framework with an intuitive and extensible way to create chatbot and smart apps that connect to different communication channels like **[Whatsapp](https://www.builderbot.app/</plugins/telegram>)** , **[Telegram](https://www.builderbot.app/</plugins/telegram>)** and others. We have made an intuitive framework so you can have your first chatbot in minutes. **[Winner of the first prize at OpenExpo 2024](https://www.builderbot.app/<https:/a.cstmapp.com/voteme/974264/712365893>)** 🏆\n## [Quick Start](https://www.builderbot.app/</en#quick-start>)\nTo create quickly with the following command\npnpmnpm\n```\npnpmcreatebuilderbot@latest\n\n```\nCopyCopied!\n[Installation and requirements](https://www.builderbot.app/</quickstart#install>)\n## [⚡ Building an AI bot](https://www.builderbot.app/</en#building-an-ai-bot>)\nIn this few minutes tutorial you can have your own chatbot with whatsapp and artificial intelligence to talk about your business.\n### Learn how to create a bot with the new open ai assistants\n[Code repository](https://www.builderbot.app/<https:/github.com/leifermendez/builderbot-openai-assistants>)\n## Quick Example\nIn this example we can see the basis of a simple bot which responds to the keywords sent by a user, the words are: `info, hello, hi`. You can see how to create the bot and implement the [flows](https://www.builderbot.app/</concepts#flow>).\nmain.tsmain.js\n```\nimport { createBot, createProvider, createFlow, addKeyword, MemoryDB } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\nconstwelcomeFlow=addKeyword<BaileysProvider,MemoryDB>(['hello','hi'])\n.addAnswer('Ey! welcome')\n.addAnswer(`Send image from URL`, { media:'https://i.imgur.com/0HpzsEm.png' })\nconstmain=async () => {\nconstadapterDB=newMemoryDB()\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(BaileysProvider)\nconst { handleCtx,httpServer } =awaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\nhttpServer(3000)\nadapterProvider.server.post('/v1/messages',handleCtx(async (bot, req, res) => {\nconst { number,message } =req.body\nawaitbot.sendMessage(number, message, {})\nreturnres.end('send')\n  }))\n}\nmain()\n\n```\nCopyCopied!\n## [Guides](https://www.builderbot.app/</en#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/</plugins>)\n## [Resources](https://www.builderbot.app/</en#resources>)\n### [Modularize](https://www.builderbot.app/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/<https:/link.codigoencasa.com/DISCORD>)\n", "chunks": [{"content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/</>)\n  * [Contribute](https://www.builderbot.app/</contribute>)\n  * [Course](https://www.builderbot.app/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)", "metadata": {"title": "BuilderBot.app Create a WhatsApp Chatbot, Without Limit", "url": "https://www.builderbot.app/en", "section": "Sin título"}, "embedding": null}, {"content": "[Get started](https://www.builderbot.app/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/</>)\n  * [Documentation](https://www.builderbot.app/</en#>)\n  * [Support](https://www.builderbot.app/</en#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/</>)\n    * [Quickstart](https://www.builderbot.app/</quickstart>)\n    * [Concepts](https://www.builderbot.app/</concepts>)\n    * [Examples](https://www.builderbot.app/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/</add-functions>)\n    * [Context](https://www.builderbot.app/</context>)\n    * [Methods](https://www.builderbot.app/</methods>)\n    * [Events](https://www.builderbot.app/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/</deploy>)\n    * [Railway](https://www.builderbot.app/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/</contribute>)\n    * [Core](https://www.builderbot.app/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/</en#>)", "metadata": {"title": "BuilderBot.app Create a WhatsApp Chatbot, Without Limit", "url": "https://www.builderbot.app/en", "section": "Sin título"}, "embedding": null}, {"content": "# Get started with BuilderBot\nThis is a **free** and open source framework with an intuitive and extensible way to create chatbot and smart apps that connect to different communication channels like **[Whatsapp](https://www.builderbot.app/</plugins/telegram>)** , **[Telegram](https://www.builderbot.app/</plugins/telegram>)** and others. We have made an intuitive framework so you can have your first chatbot in minutes. **[Winner of the first prize at OpenExpo 2024](https://www.builderbot.app/<https:/a.cstmapp.com/voteme/974264/712365893>)** 🏆\n", "metadata": {"title": "BuilderBot.app Create a WhatsApp Chatbot, Without Limit", "url": "https://www.builderbot.app/en", "section": "Get started with BuilderBot"}, "embedding": null}, {"content": "## [Quick Start](https://www.builderbot.app/</en#quick-start>)\nTo create quickly with the following command\npnpmnpm\n```\npnpmcreatebuilderbot@latest\n\n```\nCopyCopied!\n[Installation and requirements](https://www.builderbot.app/</quickstart#install>)\n", "metadata": {"title": "BuilderBot.app Create a WhatsApp Chatbot, Without Limit", "url": "https://www.builderbot.app/en", "section": "[Quick Start](https://www.builderbot.app/</en#quick-start>)"}, "embedding": null}, {"content": "## [⚡ Building an AI bot](https://www.builderbot.app/</en#building-an-ai-bot>)\nIn this few minutes tutorial you can have your own chatbot with whatsapp and artificial intelligence to talk about your business.\n### Learn how to create a bot with the new open ai assistants\n[Code repository](https://www.builderbot.app/<https:/github.com/leifermendez/builderbot-openai-assistants>)\n", "metadata": {"title": "BuilderBot.app Create a WhatsApp Chatbot, Without Limit", "url": "https://www.builderbot.app/en", "section": "[⚡ Building an AI bot](https://www.builderbot.app/</en#building-an-ai-bot>)"}, "embedding": null}, {"content": "## Quick Example\nIn this example we can see the basis of a simple bot which responds to the keywords sent by a user, the words are: `info, hello, hi`. You can see how to create the bot and implement the [flows](https://www.builderbot.app/</concepts#flow>).\nmain.tsmain.js\n```\nimport { createBot, createProvider, createFlow, addKeyword, MemoryDB } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\nconstwelcomeFlow=addKeyword<BaileysProvider,MemoryDB>(['hello','hi'])\n.addAnswer('Ey! welcome')\n.addAnswer(`Send image from URL`, { media:'https://i.imgur.com/0HpzsEm.png' })\nconstmain=async () => {\nconstadapterDB=newMemoryDB()\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(BaileysProvider)\nconst { handleCtx,httpServer } =awaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\nhttpServer(3000)\nadapterProvider.server.post('/v1/messages',handleCtx(async (bot, req, res) => {\nconst { number,message } =req.body\nawaitbot.sendMessage(number, message, {})\nreturnres.end('send')\n  }))\n}\nmain()", "metadata": {"title": "BuilderBot.app Create a WhatsApp Chatbot, Without Limit", "url": "https://www.builderbot.app/en", "section": "Quick Example"}, "embedding": null}, {"content": "```\nCopyCopied!\n", "metadata": {"title": "BuilderBot.app Create a WhatsApp Chatbot, Without Limit", "url": "https://www.builderbot.app/en", "section": "Quick Example"}, "embedding": null}, {"content": "## [Guides](https://www.builderbot.app/</en#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/</plugins>)\n", "metadata": {"title": "BuilderBot.app Create a WhatsApp Chatbot, Without Limit", "url": "https://www.builderbot.app/en", "section": "[Guides](https://www.builderbot.app/</en#guides>)"}, "embedding": null}, {"content": "## [Resources](https://www.builderbot.app/</en#resources>)\n### [Modularize](https://www.builderbot.app/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/<https:/link.codigoencasa.com/DISCORD>)\n", "metadata": {"title": "BuilderBot.app Create a WhatsApp Chatbot, Without Limit", "url": "https://www.builderbot.app/en", "section": "[Resources](https://www.builderbot.app/</en#resources>)"}, "embedding": null}], "headers": [{"text": "BuilderBot", "level": 4, "id": ""}, {"text": "BuilderBot", "level": 4, "id": ""}, {"text": "Start here", "level": 2, "id": ""}, {"text": "Basics", "level": 2, "id": ""}, {"text": "Built-in", "level": 2, "id": ""}, {"text": "Providers", "level": 2, "id": ""}, {"text": "Deploy", "level": 2, "id": ""}, {"text": "Recipes", "level": 2, "id": ""}, {"text": "Tutorials", "level": 2, "id": ""}, {"text": "Community Contribute", "level": 2, "id": ""}, {"text": "Plugins", "level": 2, "id": ""}, {"text": "Get started with BuilderBot", "level": 1, "id": ""}, {"text": "Quick Start", "level": 2, "id": "quick-start"}, {"text": "⚡ Building an AI bot", "level": 2, "id": "building-an-ai-bot"}, {"text": "Learn how to create a bot with the new open ai assistants", "level": 3, "id": ""}, {"text": "Quick Example", "level": 2, "id": ""}, {"text": "Guides", "level": 2, "id": "guides"}, {"text": "My first chatbot", "level": 3, "id": ""}, {"text": "Concepts", "level": 3, "id": ""}, {"text": "Add Functions", "level": 3, "id": ""}, {"text": "Plugins", "level": 3, "id": ""}, {"text": "Resources", "level": 2, "id": "resources"}, {"text": "Modularize", "level": 3, "id": ""}, {"text": "Send Message", "level": 3, "id": ""}, {"text": "Dockerizer", "level": 3, "id": ""}, {"text": "Events", "level": 3, "id": ""}], "code_blocks": [{"language": "language-bash", "code": "pnpm create builderbot@latest\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, create<PERSON>low, add<PERSON>eyword, MemoryDB } from '@builderbot/bot'\nimport { BaileysProvider } from '@builderbot/provider-baileys'\n\nconst welcomeFlow = addKeyword<BaileysProvider, MemoryDB>(['hello', 'hi'])\n    .addAnswer('Ey! welcome')\n    .addAnswer(`Send image from URL`, { media: 'https://i.imgur.com/0HpzsEm.png' })\n\nconst main = async () => {\n\n    const adapterDB = new MemoryDB()\n    const adapterFlow = createFlow([welcomeFlow])\n    const adapterProvider = createProvider(BaileysProvider)\n\n    const { handleCtx, httpServer } = await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n\n    httpServer(3000)\n\n    adapterProvider.server.post('/v1/messages', handleCtx(async (bot, req, res) => {\n        const { number, message } = req.body\n        await bot.sendMessage(number, message, {})\n        return res.end('send')\n    }))\n}\n\nmain()\n"}], "success": true, "error_message": ""}, {"url": "https://www.builderbot.app/en/contribute", "title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/contribute#>)\n  * [Support](https://www.builderbot.app/en/</en/contribute#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n      * [Quick View](https://www.builderbot.app/en/</en/contribute#quick-view>)\n      * [Why Contribute?](https://www.builderbot.app/en/</en/contribute#why-contribute>)\n      * [How to Contribute](https://www.builderbot.app/en/</en/contribute#how-to-contribute>)\n      * [GitHub Workflow](https://www.builderbot.app/en/</en/contribute#git-hub-workflow>)\n      * [Writing MDX](https://www.builderbot.app/en/</en/contribute#writing-mdx>)\n      * [VSCode](https://www.builderbot.app/en/</en/contribute#vs-code>)\n      * [Extensions](https://www.builderbot.app/en/</en/contribute#extensions>)\n      * [Review Process](https://www.builderbot.app/en/</en/contribute#review-process>)\n      * [File Structure](https://www.builderbot.app/en/</en/contribute#file-structure>)\n      * [Required Fields](https://www.builderbot.app/en/</en/contribute#required-fields>)\n      * [Code Blocks](https://www.builderbot.app/en/</en/contribute#code-blocks>)\n      * [Language and Filename](https://www.builderbot.app/en/</en/contribute#language-and-filename>)\n      * [Grouped code blocks](https://www.builderbot.app/en/</en/contribute#grouped-code-blocks>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/contribute#>)\n\n\n# Contribute\nWelcome to the **BuilderBot** Contribution Guide We're glad to have you here.\nThis page provides instructions on how to edit BuilderBot documentation. Our goal is to ensure that everyone in the community feels empowered to contribute and improve our documentation.\n## [Quick View](https://www.builderbot.app/en/</en/contribute#quick-view>)\n  * Make a fork of the [project](https://www.builderbot.app/en/<https:/github.com/codigoencasa/documentation/fork>)\n  * Clone the project `git clone https://github.com/USERNAME/documentation`\n  * Install dependencies `npm install`\n  * Make your changes\n  * Send your contributions (PullRequest)\n\n\n## [Why Contribute?](https://www.builderbot.app/en/</en/contribute#why-contribute>)\nOpen source work never ends, and neither does documentation. Contributing to the documentation is a great way for beginners to get involved in open source and for experienced developers to clarify more complex issues while sharing their knowledge with the community.\nBy contributing to BuilderBot documentation, you help us create a more robust learning resource for all developers. If you've found a typo, a confusing section, or noticed that a particular topic is missing, your contributions are welcome and appreciated.\n## [How to Contribute](https://www.builderbot.app/en/</en/contribute#how-to-contribute>)\nThe content of the documentation is located in the [BuilderBot repository](https://www.builderbot.app/en/<https:/github.com/codigoencasa/documentation>). To contribute, you can edit the files directly on GitHub or clone the repository and edit the files locally.\n## [GitHub Workflow](https://www.builderbot.app/en/</en/contribute#git-hub-workflow>)\nIf you're new to GitHub, we recommend you read the GitHub Open Source Guide to learn how to fork a repository, create a branch, and send a pull request.\nThe code in the underlying documents lives in a private codebase that syncs with the public BuilderBot repository. This means that you cannot preview the docs locally. However, you will see your changes in builderbot.app after merging a pull request.\n## [Writing MDX](https://www.builderbot.app/en/</en/contribute#writing-mdx>)\nThe docs are written in [MDX](https://www.builderbot.app/en/<https:/mdxjs.com/>), a markdown format that supports JSX syntax. This allows us to embed React components in the docs. See the [GitHub Markdown Guide](https://www.builderbot.app/en/<https:/docs.github.com/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax>) for a quick overview of markdown syntax.\n## [VSCode](https://www.builderbot.app/en/</en/contribute#vs-code>)\n### Previewing Changes Locally\nVSCode has a built-in markdown previewer that you can use to see your edits locally. To enable the previewer for MDX files, you'll need to add a configuration option to your user settings.\nOpen the command palette (`⌘ + ⇧ + P` on Mac or `Ctrl + Shift + P` on Windows) and search from `Preferences: Open User Settings (JSON)`.\nThen, add the following line to your `settings.json` file:\n```\n{\n\"files.associations\": {\n\"*.mdx\":\"markdown\"\n }\n}\n\n```\nCopyCopied!\nNext, open the command palette again, and search for `Markdown: Preview File` or `Markdown: Open Preview to the Side`. This will open a preview window where you can see your formatted changes.\n## [Extensions](https://www.builderbot.app/en/</en/contribute#extensions>)\nWe also recommend the following extensions for VSCode users:\n  * [MDX](https://www.builderbot.app/en/<https:/marketplace.visualstudio.com/items?itemName=unifiedjs.vscode-mdx>): Intellisense and syntax highlighting for MDX.\n  * [Grammarly](https://www.builderbot.app/en/<https:/marketplace.visualstudio.com/items?itemName=znck.grammarly>): Grammar and spell checker.\n  * [Prettier](https://www.builderbot.app/en/<https:/marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode>): Format MDX files on save.\n\n\n## [Review Process](https://www.builderbot.app/en/</en/contribute#review-process>)\nOnce you have submitted your contribution, a **Core Team** member will review your changes, provide feedback and merge the pull request when ready.\nPlease let us know if you have any questions or need further assistance in the comments of your PR. Thank you for contributing to the BuilderBot docs and for being part of our community.\n## [File Structure](https://www.builderbot.app/en/</en/contribute#file-structure>)\nDocuments use file system routing. Each folder and file within `/pages`[](https://www.builderbot.app/en/<https:/github.com/codigoencasa/documentation/tree/master/src/pages>) represents a path segment. These segments are used to generate URL paths, navigation and breadcrumbs.\n```\nen\n├── showcases\n│  └── api-use.mdx\n└── ...\n\n```\nCopyCopied!\nEach folder prefix `en`, `es`, `pt` represents the language in which the content is represented.\n```\nen\n├── showcases\n│  └── api-use.mdx\n└── ...\nes\n├── showcases\n│  └── api-use.mdx\n└── ...\npt\n├── showcases\n│  └── api-use.mdx\n└── ...\n\n```\nCopyCopied!\n## [Required Fields](https://www.builderbot.app/en/</en/contribute#required-fields>)\nThe following fields are **required** :\nField| Description  \n---|---  \n`description`| The page's description, used in the `<meta name=\"description\">` tag for SEO.  \n`title`| The page's `<h1>` title, used for SEO and OG Images.  \n```\nexportconstdescription='In this guide, we will talk ...'\n# Community\n\n```\nCopyCopied!\n## [Code Blocks](https://www.builderbot.app/en/</en/contribute#code-blocks>)\nThe code blocks must contain a minimal working example that can be copied and pasted. This means that the code must be able to run without any additional configuration.\nFor example if we want to print TS or JS code\n### example.ts\n```\nconstflow=addKeyword('hello')\n.addAnswer(`What is your name?`, { capture:true },async (ctx, { state }) => {\nawaitstate.update({ name:ctx.body })\n  })\n.addAction(async (ctx, { state, flowDynamic }) => {\nconstname=state.get('name')\nawaitflowDynamic(`Your name is: ${name}`)\n  })\n}\n\n```\nCopyCopied!\nAlways run examples locally before committing them. This will ensure that the code is up-to-date and working.\n## [Language and Filename](https://www.builderbot.app/en/</en/contribute#language-and-filename>)\nCode blocks should have a header that includes the language and the `filename`. Add a `filename` prop to render a special Terminal icon that helps orientate users where to input the command. For example:\n```\n```ts {{ title: 'example.ts' }}\nconstflow=addKeyword('hello')\n.addAnswer(`What is your name?`, { capture:true },async (ctx, { state }) => {\nawaitstate.update({ name:ctx.body })\n  })\n.addAction(async (ctx, { state, flowDynamic }) => {\nconstname=state.get('name')\nawaitflowDynamic(`Your name is: ${name}`)\n  })\n}\n```\n\n```\nCopyCopied!\nMost examples in the docs are written in `tsx` and `jsx`, and a few in `bash`. However, you can use any supported language, here's the [full list](https://www.builderbot.app/en/<https:/github.com/shikijs/shiki/blob/main/docs/languages.md#all-languages>).\nWhen writing JavaScript code blocks, we use the following language and extension combinations.\nLanguage| Extension  \n---|---  \nJavaScript files| ```js| .js  \nTypeScript files| ```ts| .ts  \n## [Grouped code blocks](https://www.builderbot.app/en/</en/contribute#grouped-code-blocks>)\nSometimes we will need to represent a group of blocks of code grouped together even with different file names and in multiple languages we can do it in the following way\napp.tsprovider/index.tsdatabase/index.tsflow/index.tsflow/welcome.flow.tsservices/ai.ts\n```\nimport { createBot } from'@builderbot/bot';\nimport { flow } from\"./flow\";\nimport { database } from\"./database\";\nimport { provider } from\"./provider\";\nimport { ai } from\"./services/ai\";\nconstmain=async () => {\nawaitcreateBot({\n     flow,\n     provider,\n     database,\n   },\n     extensions: {\n     ai // Dependency AI \n   })\nprovider.initHttpServer(3000)\n}\nmain()\n\n```\nCopyCopied!\nThe template already provides internally a `<CodeGroup>` component that has the ability to interpret code blocks.\n```\n<CodeGroup>\n```ts {{ title: 'app.ts' }}\nimport { createBot } from'@builderbot/bot';\nimport { flow } from\"./flow\";\nimport { database } from\"./database\";\nimport { provider } from\"./provider\";\nimport { ai } from\"./services/ai\";\nconstmain=async () => {\nawaitcreateBot({\n     flow,\n     provider,\n     database,\n   },\n     extensions: {\n     ai // Dependency AI \n   })\nprovider.initHttpServer(3000)\n}\nmain()\n```\n```ts {{ title: 'provider/index.ts' }}\nimport { createProvider } from'@builderbot/bot';\nimport { BaileysProvider } from'@builderbot/provider-baileys';\nexportconstprovider=createProvider(BaileysProvider)\n```\n```ts {{ title: 'database/index.ts' }}\nexportconstdatabase=newMemoryDB()\n```\n```ts {{ title: 'flow/index.ts' }}\nimport { createFlow } from'@builderbot/bot';\nimport { flowWelcome } from\"./welcome.flow\";\nimport { byeFlow } from\"./bye.flow\";\nimport { mediaFlow } from\"./media.flow\";\n// other flows....\nexportconstflow=createFlow([flowWelcome, byeFlow, mediaFlow])\n```\n```ts {{ title: 'flow/welcome.flow.ts' }}\nimport { addKeyword, EVENTS } from'@builderbot/bot';\nexportconstflowWelcome=addKeyword(EVENTS.WELCOME)\n.addAction(async (ctx, {flowDynamic, extensions})=> {\nconst { ai } = extensions\nconsttalkWithGPT=ai.chat(ctx.body) // Dependency AI from app.ts\nawaitflowDynamic(talkWithGPT)\n })\n```\n```ts {{ title: 'services/ai.ts' }}\n// ....\nexportconstai=newAiService(process.env.OPEN_AI_KEY);\n```\n</CodeGroup>\n\n```\nCopyCopied!\n## [Guides](https://www.builderbot.app/en/</en/contribute#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/</plugins>)\n## [Resources](https://www.builderbot.app/en/</en/contribute#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "chunks": [{"content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "Sin título"}, "embedding": null}, {"content": "[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/contribute#>)\n  * [Support](https://www.builderbot.app/en/</en/contribute#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n      * [Quick View](https://www.builderbot.app/en/</en/contribute#quick-view>)\n      * [Why Contribute?](https://www.builderbot.app/en/</en/contribute#why-contribute>)\n      * [How to Contribute](https://www.builderbot.app/en/</en/contribute#how-to-contribute>)\n      * [GitHub Workflow](https://www.builderbot.app/en/</en/contribute#git-hub-workflow>)\n      * [Writing MDX](https://www.builderbot.app/en/</en/contribute#writing-mdx>)\n      * [VSCode](https://www.builderbot.app/en/</en/contribute#vs-code>)\n      * [Extensions](https://www.builderbot.app/en/</en/contribute#extensions>)\n      * [Review Process](https://www.builderbot.app/en/</en/contribute#review-process>)\n      * [File Structure](https://www.builderbot.app/en/</en/contribute#file-structure>)\n      * [Required Fields](https://www.builderbot.app/en/</en/contribute#required-fields>)\n      * [Code Blocks](https://www.builderbot.app/en/</en/contribute#code-blocks>)\n      * [Language and Filename](https://www.builderbot.app/en/</en/contribute#language-and-filename>)\n      * [Grouped code blocks](https://www.builderbot.app/en/</en/contribute#grouped-code-blocks>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/contribute#>)", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "Sin título"}, "embedding": null}, {"content": "# Contribute\nWelcome to the **BuilderBot** Contribution Guide We're glad to have you here.\nThis page provides instructions on how to edit BuilderBot documentation. Our goal is to ensure that everyone in the community feels empowered to contribute and improve our documentation.\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "Contribute"}, "embedding": null}, {"content": "## [Quick View](https://www.builderbot.app/en/</en/contribute#quick-view>)\n  * Make a fork of the [project](https://www.builderbot.app/en/<https:/github.com/codigoencasa/documentation/fork>)\n  * Clone the project `git clone https://github.com/USERNAME/documentation`\n  * Install dependencies `npm install`\n  * Make your changes\n  * Send your contributions (PullRequest)\n\n\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[Quick View](https://www.builderbot.app/en/</en/contribute#quick-view>)"}, "embedding": null}, {"content": "## [Why Contribute?](https://www.builderbot.app/en/</en/contribute#why-contribute>)\nOpen source work never ends, and neither does documentation. Contributing to the documentation is a great way for beginners to get involved in open source and for experienced developers to clarify more complex issues while sharing their knowledge with the community.\nBy contributing to BuilderBot documentation, you help us create a more robust learning resource for all developers. If you've found a typo, a confusing section, or noticed that a particular topic is missing, your contributions are welcome and appreciated.\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[Why Contribute?](https://www.builderbot.app/en/</en/contribute#why-contribute>)"}, "embedding": null}, {"content": "## [How to Contribute](https://www.builderbot.app/en/</en/contribute#how-to-contribute>)\nThe content of the documentation is located in the [BuilderBot repository](https://www.builderbot.app/en/<https:/github.com/codigoencasa/documentation>). To contribute, you can edit the files directly on GitHub or clone the repository and edit the files locally.\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[How to Contribute](https://www.builderbot.app/en/</en/contribute#how-to-contribute>)"}, "embedding": null}, {"content": "## [GitHub Workflow](https://www.builderbot.app/en/</en/contribute#git-hub-workflow>)\nIf you're new to GitHub, we recommend you read the GitHub Open Source Guide to learn how to fork a repository, create a branch, and send a pull request.\nThe code in the underlying documents lives in a private codebase that syncs with the public BuilderBot repository. This means that you cannot preview the docs locally. However, you will see your changes in builderbot.app after merging a pull request.\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[GitHub Workflow](https://www.builderbot.app/en/</en/contribute#git-hub-workflow>)"}, "embedding": null}, {"content": "## [Writing MDX](https://www.builderbot.app/en/</en/contribute#writing-mdx>)\nThe docs are written in [MDX](https://www.builderbot.app/en/<https:/mdxjs.com/>), a markdown format that supports JSX syntax. This allows us to embed React components in the docs. See the [GitHub Markdown Guide](https://www.builderbot.app/en/<https:/docs.github.com/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax>) for a quick overview of markdown syntax.\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[Writing MDX](https://www.builderbot.app/en/</en/contribute#writing-mdx>)"}, "embedding": null}, {"content": "## [VSCode](https://www.builderbot.app/en/</en/contribute#vs-code>)\n### Previewing Changes Locally\nVSCode has a built-in markdown previewer that you can use to see your edits locally. To enable the previewer for MDX files, you'll need to add a configuration option to your user settings.\nOpen the command palette (`⌘ + ⇧ + P` on Mac or `Ctrl + Shift + P` on Windows) and search from `Preferences: Open User Settings (JSON)`.\nThen, add the following line to your `settings.json` file:\n```\n{\n\"files.associations\": {\n\"*.mdx\":\"markdown\"\n }\n}\n\n```\nCopyCopied!\nNext, open the command palette again, and search for `Markdown: Preview File` or `Markdown: Open Preview to the Side`. This will open a preview window where you can see your formatted changes.\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[VSCode](https://www.builderbot.app/en/</en/contribute#vs-code>)"}, "embedding": null}, {"content": "## [Extensions](https://www.builderbot.app/en/</en/contribute#extensions>)\nWe also recommend the following extensions for VSCode users:\n  * [MDX](https://www.builderbot.app/en/<https:/marketplace.visualstudio.com/items?itemName=unifiedjs.vscode-mdx>): Intellisense and syntax highlighting for MDX.\n  * [Grammarly](https://www.builderbot.app/en/<https:/marketplace.visualstudio.com/items?itemName=znck.grammarly>): Grammar and spell checker.\n  * [Prettier](https://www.builderbot.app/en/<https:/marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode>): Format MDX files on save.\n\n\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[Extensions](https://www.builderbot.app/en/</en/contribute#extensions>)"}, "embedding": null}, {"content": "## [Review Process](https://www.builderbot.app/en/</en/contribute#review-process>)\nOnce you have submitted your contribution, a **Core Team** member will review your changes, provide feedback and merge the pull request when ready.\nPlease let us know if you have any questions or need further assistance in the comments of your <PERSON>. Thank you for contributing to the BuilderBot docs and for being part of our community.\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[Review Process](https://www.builderbot.app/en/</en/contribute#review-process>)"}, "embedding": null}, {"content": "## [File Structure](https://www.builderbot.app/en/</en/contribute#file-structure>)\nDocuments use file system routing. Each folder and file within `/pages`[](https://www.builderbot.app/en/<https:/github.com/codigoencasa/documentation/tree/master/src/pages>) represents a path segment. These segments are used to generate URL paths, navigation and breadcrumbs.\n```\nen\n├── showcases\n│  └── api-use.mdx\n└── ...\n\n```\nCopyCopied!\nEach folder prefix `en`, `es`, `pt` represents the language in which the content is represented.\n```\nen\n├── showcases\n│  └── api-use.mdx\n└── ...\nes\n├── showcases\n│  └── api-use.mdx\n└── ...\npt\n├── showcases\n│  └── api-use.mdx\n└── ...\n\n```\nCopyCopied!\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[File Structure](https://www.builderbot.app/en/</en/contribute#file-structure>)"}, "embedding": null}, {"content": "## [Required Fields](https://www.builderbot.app/en/</en/contribute#required-fields>)\nThe following fields are **required** :\nField| Description  \n---|---  \n`description`| The page's description, used in the `<meta name=\"description\">` tag for SEO.  \n`title`| The page's `<h1>` title, used for SEO and OG Images.  \n```\nexportconstdescription='In this guide, we will talk ...'\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[Required Fields](https://www.builderbot.app/en/</en/contribute#required-fields>)"}, "embedding": null}, {"content": "# Community\n\n```\nCopyCopied!\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "Community"}, "embedding": null}, {"content": "## [Code Blocks](https://www.builderbot.app/en/</en/contribute#code-blocks>)\nThe code blocks must contain a minimal working example that can be copied and pasted. This means that the code must be able to run without any additional configuration.\nFor example if we want to print TS or JS code\n### example.ts\n```\nconstflow=addKeyword('hello')\n.addAnswer(`What is your name?`, { capture:true },async (ctx, { state }) => {\nawaitstate.update({ name:ctx.body })\n  })\n.addAction(async (ctx, { state, flowDynamic }) => {\nconstname=state.get('name')\nawaitflowDynamic(`Your name is: ${name}`)\n  })\n}\n\n```\nCopyCopied!\nAlways run examples locally before committing them. This will ensure that the code is up-to-date and working.\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[Code Blocks](https://www.builderbot.app/en/</en/contribute#code-blocks>)"}, "embedding": null}, {"content": "## [Language and Filename](https://www.builderbot.app/en/</en/contribute#language-and-filename>)\nCode blocks should have a header that includes the language and the `filename`. Add a `filename` prop to render a special Terminal icon that helps orientate users where to input the command. For example:\n```\n```ts {{ title: 'example.ts' }}\nconstflow=addKeyword('hello')\n.addAnswer(`What is your name?`, { capture:true },async (ctx, { state }) => {\nawaitstate.update({ name:ctx.body })\n  })\n.addAction(async (ctx, { state, flowDynamic }) => {\nconstname=state.get('name')\nawaitflowDynamic(`Your name is: ${name}`)\n  })\n}\n```", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[Language and Filename](https://www.builderbot.app/en/</en/contribute#language-and-filename>)"}, "embedding": null}, {"content": "```\nCopyCopied!\nMost examples in the docs are written in `tsx` and `jsx`, and a few in `bash`. However, you can use any supported language, here's the [full list](https://www.builderbot.app/en/<https:/github.com/shikijs/shiki/blob/main/docs/languages.md#all-languages>).\nWhen writing JavaScript code blocks, we use the following language and extension combinations.\nLanguage| Extension  \n---|---  \nJavaScript files| ```js| .js  \nTypeScript files| ```ts| .ts  \n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[Language and Filename](https://www.builderbot.app/en/</en/contribute#language-and-filename>)"}, "embedding": null}, {"content": "## [Grouped code blocks](https://www.builderbot.app/en/</en/contribute#grouped-code-blocks>)\nSometimes we will need to represent a group of blocks of code grouped together even with different file names and in multiple languages we can do it in the following way\napp.tsprovider/index.tsdatabase/index.tsflow/index.tsflow/welcome.flow.tsservices/ai.ts\n```\nimport { createBot } from'@builderbot/bot';\nimport { flow } from\"./flow\";\nimport { database } from\"./database\";\nimport { provider } from\"./provider\";\nimport { ai } from\"./services/ai\";\nconstmain=async () => {\nawaitcreateBot({\n     flow,\n     provider,\n     database,\n   },\n     extensions: {\n     ai // Dependency AI \n   })\nprovider.initHttpServer(3000)\n}\nmain()", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[Grouped code blocks](https://www.builderbot.app/en/</en/contribute#grouped-code-blocks>)"}, "embedding": null}, {"content": "```\nCopyCopied!\nThe template already provides internally a `<CodeGroup>` component that has the ability to interpret code blocks.\n```\n<CodeGroup>\n```ts {{ title: 'app.ts' }}\nimport { createBot } from'@builderbot/bot';\nimport { flow } from\"./flow\";\nimport { database } from\"./database\";\nimport { provider } from\"./provider\";\nimport { ai } from\"./services/ai\";\nconstmain=async () => {\nawaitcreateBot({\n     flow,\n     provider,\n     database,\n   },\n     extensions: {\n     ai // Dependency AI \n   })\nprovider.initHttpServer(3000)\n}\nmain()\n```\n```ts {{ title: 'provider/index.ts' }}\nimport { createProvider } from'@builderbot/bot';\nimport { BaileysProvider } from'@builderbot/provider-baileys';\nexportconstprovider=createProvider(BaileysProvider)\n```\n```ts {{ title: 'database/index.ts' }}\nexportconstdatabase=newMemoryDB()\n```\n```ts {{ title: 'flow/index.ts' }}\nimport { createFlow } from'@builderbot/bot';\nimport { flowWelcome } from\"./welcome.flow\";\nimport { byeFlow } from\"./bye.flow\";\nimport { mediaFlow } from\"./media.flow\";\n// other flows....\nexportconstflow=createFlow([flowWelcome, byeFlow, mediaFlow])\n```\n```ts {{ title: 'flow/welcome.flow.ts' }}\nimport { addKeyword, EVENTS } from'@builderbot/bot';\nexportconstflowWelcome=addKeyword(EVENTS.WELCOME)\n.addAction(async (ctx, {flowDynamic, extensions})=> {\nconst { ai } = extensions\nconsttalkWithGPT=ai.chat(ctx.body) // Dependency AI from app.ts\nawaitflowDynamic(talkWithGPT)\n })\n```\n```ts {{ title: 'services/ai.ts' }}\n// ....\nexportconstai=newAiService(process.env.OPEN_AI_KEY);\n```\n</CodeGroup>", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[Grouped code blocks](https://www.builderbot.app/en/</en/contribute#grouped-code-blocks>)"}, "embedding": null}, {"content": "```\nCopyCopied!\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[Grouped code blocks](https://www.builderbot.app/en/</en/contribute#grouped-code-blocks>)"}, "embedding": null}, {"content": "## [Guides](https://www.builderbot.app/en/</en/contribute#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/</plugins>)\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[Guides](https://www.builderbot.app/en/</en/contribute#guides>)"}, "embedding": null}, {"content": "## [Resources](https://www.builderbot.app/en/</en/contribute#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "[Resources](https://www.builderbot.app/en/</en/contribute#resources>)"}, "embedding": null}], "headers": [{"text": "BuilderBot", "level": 4, "id": ""}, {"text": "BuilderBot", "level": 4, "id": ""}, {"text": "Start here", "level": 2, "id": ""}, {"text": "Basics", "level": 2, "id": ""}, {"text": "Built-in", "level": 2, "id": ""}, {"text": "Providers", "level": 2, "id": ""}, {"text": "Deploy", "level": 2, "id": ""}, {"text": "Recipes", "level": 2, "id": ""}, {"text": "Tutorials", "level": 2, "id": ""}, {"text": "Community Contribute", "level": 2, "id": ""}, {"text": "Plugins", "level": 2, "id": ""}, {"text": "Contribute", "level": 1, "id": ""}, {"text": "Quick View", "level": 2, "id": "quick-view"}, {"text": "Why Contribute?", "level": 2, "id": "why-contribute"}, {"text": "How to Contribute", "level": 2, "id": "how-to-contribute"}, {"text": "GitHub Workflow", "level": 2, "id": "git-hub-workflow"}, {"text": "Writing MDX", "level": 2, "id": "writing-mdx"}, {"text": "VSCode", "level": 2, "id": "vs-code"}, {"text": "Previewing Changes Locally", "level": 3, "id": ""}, {"text": "Extensions", "level": 2, "id": "extensions"}, {"text": "Review Process", "level": 2, "id": "review-process"}, {"text": "File Structure", "level": 2, "id": "file-structure"}, {"text": "Required <PERSON>", "level": 2, "id": "required-fields"}, {"text": "Code Blocks", "level": 2, "id": "code-blocks"}, {"text": "example.ts", "level": 3, "id": ""}, {"text": "Language and Filename", "level": 2, "id": "language-and-filename"}, {"text": "Grouped code blocks", "level": 2, "id": "grouped-code-blocks"}, {"text": "Guides", "level": 2, "id": "guides"}, {"text": "My first chatbot", "level": 3, "id": ""}, {"text": "Concepts", "level": 3, "id": ""}, {"text": "Add Functions", "level": 3, "id": ""}, {"text": "Plugins", "level": 3, "id": ""}, {"text": "Resources", "level": 2, "id": "resources"}, {"text": "Modularize", "level": 3, "id": ""}, {"text": "Send Message", "level": 3, "id": ""}, {"text": "Dockerizer", "level": 3, "id": ""}, {"text": "Events", "level": 3, "id": ""}], "code_blocks": [{"language": "language-json", "code": "{\n  \"files.associations\": {\n    \"*.mdx\": \"markdown\"\n  }\n}\n"}, {"language": "language-txt", "code": "en\n├── showcases\n│   └── api-use.mdx\n└── ...\n"}, {"language": "language-txt", "code": "en\n├── showcases\n│   └── api-use.mdx\n└── ...\nes\n├── showcases\n│   └── api-use.mdx\n└── ...\npt\n├── showcases\n│   └── api-use.mdx\n└── ...\n"}, {"language": "language-mdx", "code": "export const description = 'In this guide, we will talk ...'\n\n# Community\n"}, {"language": "language-ts", "code": "const flow = addKeyword('hello')\n    .addAnswer(`What is your name?`, { capture: true }, async (ctx, { state }) => {\n        await state.update({ name: ctx.body })\n    })\n    .addAction(async (ctx, { state, flowDynamic }) => {\n        const name = state.get('name')\n        await flowDynamic(`Your name is: ${name}`)\n    })\n}\n"}, {"language": "language-mdx", "code": "```ts {{ title: 'example.ts' }}\nconst flow = addKeyword('hello')\n    .addAnswer(`What is your name?`, { capture: true }, async (ctx, { state }) => {\n        await state.update({ name: ctx.body })\n    })\n    .addAction(async (ctx, { state, flowDynamic }) => {\n        const name = state.get('name')\n        await flowDynamic(`Your name is: ${name}`)\n    })\n}\n```\n"}, {"language": "language-ts", "code": "  import { createBot } from '@builderbot/bot';\n  import { flow } from \"./flow\";\n  import { database } from \"./database\";\n  import { provider } from \"./provider\";\n  import { ai } from \"./services/ai\";\n\n  const main = async () => {\n  await createBot({\n          flow,\n          provider,\n          database,\n      },\n          extensions: {\n          ai // Dependency AI \n      })\n\n  provider.initHttpServer(3000)\n}\nmain()\n"}, {"language": "language-mdx", "code": "<CodeGroup>\n```ts {{ title: 'app.ts' }}\n  import { createBot } from '@builderbot/bot';\n  import { flow } from \"./flow\";\n  import { database } from \"./database\";\n  import { provider } from \"./provider\";\n  import { ai } from \"./services/ai\";\n\n  const main = async () => {\n  await createBot({\n          flow,\n          provider,\n          database,\n      },\n          extensions: {\n          ai // Dependency AI \n      })\n\n  provider.initHttpServer(3000)\n}\nmain()\n```\n```ts {{ title: 'provider/index.ts' }}\n  import { createProvider } from '@builderbot/bot';\n  import { BaileysProvider } from '@builderbot/provider-baileys';\n\n  export const provider = createProvider(BaileysProvider)\n```\n```ts {{ title: 'database/index.ts' }}\n  export const database = new MemoryDB()\n```\n```ts {{ title: 'flow/index.ts' }}\n  import { createFlow } from '@builderbot/bot';\n  import { flowWelcome } from \"./welcome.flow\";\n  import { byeFlow } from \"./bye.flow\";\n  import { mediaFlow } from \"./media.flow\";\n  // other flows....\n\n  export const flow =  createFlow([flowWelcome, byeFlow, mediaFlow])\n```\n```ts {{ title: 'flow/welcome.flow.ts' }}\n  import { addKeyword, EVENTS } from '@builderbot/bot';\n\n  export const flowWelcome = addKeyword(EVENTS.WELCOME)\n  .addAction(async (ctx, {flowDynamic, extensions})=> {\n    const { ai } = extensions\n    const talkWithGPT = ai.chat(ctx.body) // Dependency AI from app.ts\n    await flowDynamic(talkWithGPT)\n  })\n```\n```ts {{ title: 'services/ai.ts' }}\n  // ....\n  export const ai = new AiService(process.env.OPEN_AI_KEY);\n```\n</CodeGroup>\n"}], "success": true, "error_message": ""}, {"url": "https://www.builderbot.app/en/quickstart", "title": "Documentation - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/quickstart#>)\n  * [Support](https://www.builderbot.app/en/</en/quickstart#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n      * [Create](https://www.builderbot.app/en/</en/quickstart#create>)\n      * [Requirements](https://www.builderbot.app/en/</en/quickstart#requirements>)\n      * [Base Example](https://www.builderbot.app/en/</en/quickstart#base-example>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/quickstart#>)\n\n\n## [Create](https://www.builderbot.app/en/</en/quickstart#create>)\nCreating a bot is as simple as running the following command and following the instructions\nPrerequisites to consider before using this tool, [Node](https://www.builderbot.app/en/<https:/nodejs.org/en>) v20 or higher and [Git](https://www.builderbot.app/en/<https:/git-scm.com/download>)\npnpmnpm\n```\npnpmcreatebuilderbot@latest\n\n```\nCopyCopied!\nor you can use the following command to create a bot with the default configuration\npnpmnpm\n```\npnpmcreatebuilderbot@latest--provider=baileys--database=memory--language=ts\n\n```\nCopyCopied!\nUse the space key to select and the enter key to confirm. The CLI performs a preliminary check of the Node and operating system version, informing you if it meets the requirements or providing you with relevant information. In addition to generating a base project for you to simply start up\nIf you have problems with your terminal try running the command with **CMD, PowerShell, GitBash** or another console you have installed.\n## [Requirements](https://www.builderbot.app/en/</en/quickstart#requirements>)\nMake sure you have installed Node version **20 or higher** , below you can see an example to check the version of node you are using.\n### Node Version\n```\nnode-v\nv20.10.0\n\n```\nCopyCopied!\n[Download node from its official website](https://www.builderbot.app/en/<https:/nodejs.org/en>)\nIt is recommended to have GIT installed for proper operation. If you are using Linux or MacOc you probably already have GIT installed by default.\n### Git Version\n```\ngit-v\ngitversionXXXX\n\n```\nCopyCopied!\n[Download GIT from its official website](https://www.builderbot.app/en/<https:/git-scm.com/downloads>)\n## [Base Example](https://www.builderbot.app/en/</en/quickstart#base-example>)\nIn this example we can see the basis of a simple bot which responds to the keywords sent by a user, the words are: `info, hello, hi`. You can see how to create the bot and implement the [flows](https://www.builderbot.app/en/</concepts#flow>).\nmain.tsmain.js\n```\nimport { createBot, createProvider, createFlow, addKeyword, MemoryDB } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\n/** send static messages */\nconstwelcomeFlow=addKeyword<BaileysProvider,MemoryDB>(['hello','hi']).addAnswer('Ey! welcome')\n/** send dynamic message from db or other sources */\nconstinfoFlow=addKeyword<BaileysProvider,MemoryDB>('info')\n.addAction(async (ctx, { flowDynamic }) => {\nawaitflowDynamic(`Welcome ${ctx.name}`)\n  })\n/** send media files */\nconstmediaFlow=addKeyword<BaileysProvider,MemoryDB>('image')\n.addAnswer(`Send Image A`, { media:'https://i.imgur.com/AsvWfUX.png' })\n.addAction(async (ctx, { flowDynamic }) => {\nawaitflowDynamic(`Welcome ${ctx.name}`)\nawaitflowDynamic(\n      [\n        {\n          body:'Send Image B',\n          media:'https://i.imgur.com/w0RtKnN.png'\n        }\n      ]\n    )\n  })\n/** initialization bot */\nconstmain=async () => {\nconstadapterDB=newMemoryDB()\nconstadapterFlow=createFlow([welcomeFlow, infoFlow, mediaFlow])\nconstadapterProvider=createProvider(BaileysProvider)\nadapterProvider.initHttpServer(3000)\nawaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\n}\nmain()\n\n```\nCopyCopied!\n## [Resources](https://www.builderbot.app/en/</en/quickstart#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "chunks": [{"content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)", "metadata": {"title": "Documentation - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/quickstart", "section": "Sin título"}, "embedding": null}, {"content": "[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/quickstart#>)\n  * [Support](https://www.builderbot.app/en/</en/quickstart#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n      * [Create](https://www.builderbot.app/en/</en/quickstart#create>)\n      * [Requirements](https://www.builderbot.app/en/</en/quickstart#requirements>)\n      * [Base Example](https://www.builderbot.app/en/</en/quickstart#base-example>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/quickstart#>)", "metadata": {"title": "Documentation - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/quickstart", "section": "Sin título"}, "embedding": null}, {"content": "## [Create](https://www.builderbot.app/en/</en/quickstart#create>)\nCreating a bot is as simple as running the following command and following the instructions\nPrerequisites to consider before using this tool, [Node](https://www.builderbot.app/en/<https:/nodejs.org/en>) v20 or higher and [Git](https://www.builderbot.app/en/<https:/git-scm.com/download>)\npnpmnpm\n```\npnpmcreatebuilderbot@latest\n\n```\nCopyCopied!\nor you can use the following command to create a bot with the default configuration\npnpmnpm\n```\npnpmcreatebuilderbot@latest--provider=baileys--database=memory--language=ts", "metadata": {"title": "Documentation - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/quickstart", "section": "[Create](https://www.builderbot.app/en/</en/quickstart#create>)"}, "embedding": null}, {"content": "```\nCopyCopied!\nUse the space key to select and the enter key to confirm. The CLI performs a preliminary check of the Node and operating system version, informing you if it meets the requirements or providing you with relevant information. In addition to generating a base project for you to simply start up\nIf you have problems with your terminal try running the command with **CMD, PowerShell, GitBash** or another console you have installed.\n", "metadata": {"title": "Documentation - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/quickstart", "section": "[Create](https://www.builderbot.app/en/</en/quickstart#create>)"}, "embedding": null}, {"content": "## [Requirements](https://www.builderbot.app/en/</en/quickstart#requirements>)\nMake sure you have installed Node version **20 or higher** , below you can see an example to check the version of node you are using.\n### Node Version\n```\nnode-v\nv20.10.0\n\n```\nCopyCopied!\n[Download node from its official website](https://www.builderbot.app/en/<https:/nodejs.org/en>)\nIt is recommended to have GIT installed for proper operation. If you are using Linux or MacOc you probably already have GIT installed by default.\n### Git Version\n```\ngit-v\ngitversionXXXX\n\n```\nCopyCopied!\n[Download GIT from its official website](https://www.builderbot.app/en/<https:/git-scm.com/downloads>)\n", "metadata": {"title": "Documentation - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/quickstart", "section": "[Requirements](https://www.builderbot.app/en/</en/quickstart#requirements>)"}, "embedding": null}, {"content": "## [Base Example](https://www.builderbot.app/en/</en/quickstart#base-example>)\nIn this example we can see the basis of a simple bot which responds to the keywords sent by a user, the words are: `info, hello, hi`. You can see how to create the bot and implement the [flows](https://www.builderbot.app/en/</concepts#flow>).\nmain.tsmain.js\n```\nimport { createBot, createProvider, createFlow, addKeyword, MemoryDB } from'@builderbot/bot'\nimport { <PERSON>s<PERSON>rov<PERSON> } from'@builderbot/provider-baileys'\n/** send static messages */\nconstwelcomeFlow=addKeyword<BaileysProvider,MemoryDB>(['hello','hi']).addAnswer('Ey! welcome')\n/** send dynamic message from db or other sources */\nconstinfoFlow=addKeyword<BaileysProvider,MemoryDB>('info')\n.addAction(async (ctx, { flowDynamic }) => {\nawaitflowDynamic(`Welcome ${ctx.name}`)\n  })\n/** send media files */\nconstmediaFlow=addKeyword<BaileysProvider,MemoryDB>('image')\n.addAnswer(`Send Image A`, { media:'https://i.imgur.com/AsvWfUX.png' })\n.addAction(async (ctx, { flowDynamic }) => {\nawaitflowDynamic(`Welcome ${ctx.name}`)\nawaitflowDynamic(\n      [\n        {\n          body:'Send Image B',\n          media:'https://i.imgur.com/w0RtKnN.png'\n        }\n      ]\n    )\n  })\n/** initialization bot */\nconstmain=async () => {\nconstadapterDB=newMemoryDB()\nconstadapterFlow=createFlow([welcomeFlow, infoFlow, mediaFlow])\nconstadapterProvider=createProvider(BaileysProvider)\nadapterProvider.initHttpServer(3000)\nawaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\n}\nmain()", "metadata": {"title": "Documentation - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/quickstart", "section": "[Base Example](https://www.builderbot.app/en/</en/quickstart#base-example>)"}, "embedding": null}, {"content": "```\nCopyCopied!\n", "metadata": {"title": "Documentation - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/quickstart", "section": "[Base Example](https://www.builderbot.app/en/</en/quickstart#base-example>)"}, "embedding": null}, {"content": "## [Resources](https://www.builderbot.app/en/</en/quickstart#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "metadata": {"title": "Documentation - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/quickstart", "section": "[Resources](https://www.builderbot.app/en/</en/quickstart#resources>)"}, "embedding": null}], "headers": [{"text": "BuilderBot", "level": 4, "id": ""}, {"text": "BuilderBot", "level": 4, "id": ""}, {"text": "Start here", "level": 2, "id": ""}, {"text": "Basics", "level": 2, "id": ""}, {"text": "Built-in", "level": 2, "id": ""}, {"text": "Providers", "level": 2, "id": ""}, {"text": "Deploy", "level": 2, "id": ""}, {"text": "Recipes", "level": 2, "id": ""}, {"text": "Tutorials", "level": 2, "id": ""}, {"text": "Community Contribute", "level": 2, "id": ""}, {"text": "Plugins", "level": 2, "id": ""}, {"text": "Create", "level": 2, "id": "create"}, {"text": "Requirements", "level": 2, "id": "requirements"}, {"text": "Node Version", "level": 3, "id": ""}, {"text": "Git Version", "level": 3, "id": ""}, {"text": "Base Example", "level": 2, "id": "base-example"}, {"text": "Resources", "level": 2, "id": "resources"}, {"text": "Modularize", "level": 3, "id": ""}, {"text": "Send Message", "level": 3, "id": ""}, {"text": "Dockerizer", "level": 3, "id": ""}, {"text": "Events", "level": 3, "id": ""}], "code_blocks": [{"language": "language-bash", "code": "pnpm create builderbot@latest\n"}, {"language": "language-bash", "code": "pnpm create builderbot@latest --provider=baileys --database=memory --language=ts\n"}, {"language": "language-bash", "code": "node -v\nv20.10.0\n"}, {"language": "language-bash", "code": "git -v\ngit version XXXX\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, create<PERSON>low, addKeyword, MemoryDB } from '@builderbot/bot'\nimport { BaileysProvider } from '@builderbot/provider-baileys'\n\n/** send static messages */\nconst welcomeFlow = addKeyword<BaileysProvider, MemoryDB>(['hello', 'hi']).addAnswer('Ey! welcome')\n\n/** send dynamic message from db or other sources */\nconst infoFlow = addKeyword<BaileysProvider, MemoryDB>('info')\n    .addAction(async (ctx, { flowDynamic }) => {\n        await flowDynamic(`Welcome ${ctx.name}`)\n    })\n\n/** send media files */\nconst mediaFlow = addKeyword<BaileysProvider, MemoryDB>('image')\n    .addAnswer(`Send Image A`, { media: 'https://i.imgur.com/AsvWfUX.png' })\n    .addAction(async (ctx, { flowDynamic }) => {\n        await flowDynamic(`Welcome ${ctx.name}`)\n        await flowDynamic(\n            [\n                {\n                    body: 'Send Image B',\n                    media: 'https://i.imgur.com/w0RtKnN.png'\n                }\n            ]\n        )\n    })\n\n/** initialization bot */\nconst main = async () => {\n\n    const adapterDB = new MemoryDB()\n    const adapterFlow = createFlow([welcomeFlow, infoFlow, mediaFlow])\n    const adapterProvider = createProvider(BaileysProvider)\n\n    adapterProvider.initHttpServer(3000)\n\n    await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n}\n\nmain()\n\n"}], "success": true, "error_message": ""}, {"url": "https://www.builderbot.app/en/concepts", "title": "Why BuilderBot? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/concepts#>)\n  * [Support](https://www.builderbot.app/en/</en/concepts#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n      * [Flow](https://www.builderbot.app/en/</en/concepts#flow>)\n      * [Provider](https://www.builderbot.app/en/</en/concepts#provider>)\n      * [Database](https://www.builderbot.app/en/</en/concepts#database>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/concepts#>)\n\n\n# Why BuilderBot?\n**BuilderBot is the framework** for the creation of ChatBots focused on low-frequency communication channels, whatsapp, telegram, etc. We implement an architecture focused on improving the developer experience and the reuse of logic at all times, if you need to create chatbots for whatsapp quickly, without limits and easy connection between different providers then BuilderBot is for you.\nThe library is based on three key components for its correct functioning: the Flow, in charge of building the context of the conversation and offering a friendly interface to the developer; the Provider, which acts as a connector allowing to easily switch between WhatsApp providers without the risk of affecting other parts of the bot; and the Database, which in line with this connector philosophy, facilitates changing the data persistence layer without the need to rewrite the workflow.\n## [Flow](https://www.builderbot.app/en/</en/concepts#flow>)\nRefers to creating structured sequences of interactions, as in building conversation flows. Two key methods are addKeyword and addAnswer, which allow keywords to be associated with specific responses, providing options for customizing the conversation flow.\nKeywords are the words you will use to start the flow, you can use a single word or a list of words. Example \"hello\", \"good morning\".\napp.tsapp.js\n```\nimport { addKeyword } from'@builderbot/bot'\naddKeyword(['hello','hi']).addAnswer('Ey! welcome')\n\n```\nCopyCopied!\nSome examples of how to use the addKeyword in which you can place the **keyword** or a list of **keywords** that will be used to start a conversational flow\n```\n// Example with single keyword\naddKeyword('hello').addAnswer('Ey! welcome')\n// Example with multi keywords\naddKeyword(['hello','hi']).addAnswer('Ey! welcome')\n\n```\nCopyCopied!\nFor a quick understanding of the operation we have prepared a basic example of how to implement\n[View more examples](https://www.builderbot.app/en/</uses-cases>)\n## [Provider](https://www.builderbot.app/en/</en/concepts#provider>)\nIt is a key piece used to deliver the message to the chosen supplier. In a case you are building a bot for whatsapp you should use an adapter like **[Meta](https://www.builderbot.app/en/</providers#meta>)** , **[Twilio](https://www.builderbot.app/en/</providers#twilio>)** , **[Baileys](https://www.builderbot.app/en/</providers#baileys>)** , etc or even if you want to connect to Telegram.\napp.tsprovider.wppconnect.tsprovider.meta.ts\n```\nimport { addKeyword, MemoryDB, createProvider, createFlow } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\n// ...stuff code...\nconstmain=async () => {\nawaitcreateBot({\n    database:newMemoryDB(),\n    provider:createProvider(BaileysProvider),\n    flow:createFlow([flowDemo])\n  })\n}\nmain()\n\n```\nCopyCopied!\n[More information about the providers ](https://www.builderbot.app/en/</providers>)\n## [Database](https://www.builderbot.app/en/</en/concepts#database>)\nJust as providers can be easily exchanged between adapters, we can do the same with the database. Now the important thing to understand is how it works. The main purpose of the database inside the bot is to provide the bot with a record of the different events that have occurred between different conversations.\nIt is ready to implement adapters from [Mongo](https://www.builderbot.app/en/</databases#mongo>), [MySQL](https://www.builderbot.app/en/</databases#my-sql>), [Postgres](https://www.builderbot.app/en/</databases#postgres>), among others.\napp.tsprovider.wppconnect.tsprovider.meta.ts\n```\nimport { addKeyword, MemoryDB, createProvider, createFlow } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\n// ...stuff code...\nconstmain=async () => {\nawaitcreateBot({\n    database:newMemoryDB(),\n    provider:createProvider(BaileysProvider),\n    flow:createFlow([flowDemo])\n  })\n}\nmain()\n\n```\nCopyCopied!\n[More information about the databases ](https://www.builderbot.app/en/</databases>)\n## [Guides](https://www.builderbot.app/en/</en/concepts#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/</plugins>)\n## [Resources](https://www.builderbot.app/en/</en/concepts#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "chunks": [{"content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)", "metadata": {"title": "Why BuilderBot? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/concepts", "section": "Sin título"}, "embedding": null}, {"content": "[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/concepts#>)\n  * [Support](https://www.builderbot.app/en/</en/concepts#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n      * [Flow](https://www.builderbot.app/en/</en/concepts#flow>)\n      * [Provider](https://www.builderbot.app/en/</en/concepts#provider>)\n      * [Database](https://www.builderbot.app/en/</en/concepts#database>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/concepts#>)", "metadata": {"title": "Why BuilderBot? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/concepts", "section": "Sin título"}, "embedding": null}, {"content": "# Why BuilderBot?\n**BuilderBot is the framework** for the creation of ChatBots focused on low-frequency communication channels, whatsapp, telegram, etc. We implement an architecture focused on improving the developer experience and the reuse of logic at all times, if you need to create chatbots for whatsapp quickly, without limits and easy connection between different providers then BuilderBot is for you.\nThe library is based on three key components for its correct functioning: the Flow, in charge of building the context of the conversation and offering a friendly interface to the developer; the Provider, which acts as a connector allowing to easily switch between WhatsApp providers without the risk of affecting other parts of the bot; and the Database, which in line with this connector philosophy, facilitates changing the data persistence layer without the need to rewrite the workflow.\n", "metadata": {"title": "Why BuilderBot? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/concepts", "section": "Why BuilderBot?"}, "embedding": null}, {"content": "## [Flow](https://www.builderbot.app/en/</en/concepts#flow>)\nRefers to creating structured sequences of interactions, as in building conversation flows. Two key methods are addKeyword and addAnswer, which allow keywords to be associated with specific responses, providing options for customizing the conversation flow.\nKeywords are the words you will use to start the flow, you can use a single word or a list of words. Example \"hello\", \"good morning\".\napp.tsapp.js\n```\nimport { addKeyword } from'@builderbot/bot'\naddKeyword(['hello','hi']).addAnswer('Ey! welcome')\n\n```\nCopyCopied!\nSome examples of how to use the addKeyword in which you can place the **keyword** or a list of **keywords** that will be used to start a conversational flow\n```\n// Example with single keyword\naddKeyword('hello').addAnswer('Ey! welcome')\n// Example with multi keywords\naddKeyword(['hello','hi']).addAnswer('Ey! welcome')", "metadata": {"title": "Why BuilderBot? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/concepts", "section": "[Flow](https://www.builderbot.app/en/</en/concepts#flow>)"}, "embedding": null}, {"content": "```\nCopyCopied!\nFor a quick understanding of the operation we have prepared a basic example of how to implement\n[View more examples](https://www.builderbot.app/en/</uses-cases>)\n", "metadata": {"title": "Why BuilderBot? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/concepts", "section": "[Flow](https://www.builderbot.app/en/</en/concepts#flow>)"}, "embedding": null}, {"content": "## [Provider](https://www.builderbot.app/en/</en/concepts#provider>)\nIt is a key piece used to deliver the message to the chosen supplier. In a case you are building a bot for whatsapp you should use an adapter like **[Meta](https://www.builderbot.app/en/</providers#meta>)** , **[Twilio](https://www.builderbot.app/en/</providers#twilio>)** , **[<PERSON><PERSON>](https://www.builderbot.app/en/</providers#baileys>)** , etc or even if you want to connect to Telegram.\napp.tsprovider.wppconnect.tsprovider.meta.ts\n```\nimport { addKeyword, MemoryDB, createProvider, createFlow } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\n// ...stuff code...\nconstmain=async () => {\nawaitcreateBot({\n    database:newMemoryDB(),\n    provider:createProvider(BaileysProvider),\n    flow:createFlow([flowDemo])\n  })\n}\nmain()\n\n```\nCopyCopied!\n[More information about the providers ](https://www.builderbot.app/en/</providers>)\n", "metadata": {"title": "Why BuilderBot? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/concepts", "section": "[Provider](https://www.builderbot.app/en/</en/concepts#provider>)"}, "embedding": null}, {"content": "## [Database](https://www.builderbot.app/en/</en/concepts#database>)\nJust as providers can be easily exchanged between adapters, we can do the same with the database. Now the important thing to understand is how it works. The main purpose of the database inside the bot is to provide the bot with a record of the different events that have occurred between different conversations.\nIt is ready to implement adapters from [Mongo](https://www.builderbot.app/en/</databases#mongo>), [MySQL](https://www.builderbot.app/en/</databases#my-sql>), [Postgres](https://www.builderbot.app/en/</databases#postgres>), among others.\napp.tsprovider.wppconnect.tsprovider.meta.ts\n```\nimport { addKeyword, MemoryDB, createProvider, createFlow } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\n// ...stuff code...\nconstmain=async () => {\nawaitcreateBot({\n    database:newMemoryDB(),\n    provider:createProvider(BaileysProvider),\n    flow:createFlow([flowDemo])\n  })\n}\nmain()", "metadata": {"title": "Why BuilderBot? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/concepts", "section": "[Database](https://www.builderbot.app/en/</en/concepts#database>)"}, "embedding": null}, {"content": "```\nCopyCopied!\n[More information about the databases ](https://www.builderbot.app/en/</databases>)\n", "metadata": {"title": "Why BuilderBot? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/concepts", "section": "[Database](https://www.builderbot.app/en/</en/concepts#database>)"}, "embedding": null}, {"content": "## [Guides](https://www.builderbot.app/en/</en/concepts#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/</plugins>)\n", "metadata": {"title": "Why BuilderBot? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/concepts", "section": "[Guides](https://www.builderbot.app/en/</en/concepts#guides>)"}, "embedding": null}, {"content": "## [Resources](https://www.builderbot.app/en/</en/concepts#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "metadata": {"title": "Why BuilderBot? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/concepts", "section": "[Resources](https://www.builderbot.app/en/</en/concepts#resources>)"}, "embedding": null}], "headers": [{"text": "BuilderBot", "level": 4, "id": ""}, {"text": "BuilderBot", "level": 4, "id": ""}, {"text": "Start here", "level": 2, "id": ""}, {"text": "Basics", "level": 2, "id": ""}, {"text": "Built-in", "level": 2, "id": ""}, {"text": "Providers", "level": 2, "id": ""}, {"text": "Deploy", "level": 2, "id": ""}, {"text": "Recipes", "level": 2, "id": ""}, {"text": "Tutorials", "level": 2, "id": ""}, {"text": "Community Contribute", "level": 2, "id": ""}, {"text": "Plugins", "level": 2, "id": ""}, {"text": "Why BuilderBot?", "level": 1, "id": ""}, {"text": "Flow", "level": 2, "id": "flow"}, {"text": "Provider", "level": 2, "id": "provider"}, {"text": "Database", "level": 2, "id": "database"}, {"text": "Guides", "level": 2, "id": "guides"}, {"text": "My first chatbot", "level": 3, "id": ""}, {"text": "Concepts", "level": 3, "id": ""}, {"text": "Add Functions", "level": 3, "id": ""}, {"text": "Plugins", "level": 3, "id": ""}, {"text": "Resources", "level": 2, "id": "resources"}, {"text": "Modularize", "level": 3, "id": ""}, {"text": "Send Message", "level": 3, "id": ""}, {"text": "Dockerizer", "level": 3, "id": ""}, {"text": "Events", "level": 3, "id": ""}], "code_blocks": [{"language": "language-ts", "code": "import { addKeyword } from '@builderbot/bot'\n\naddKeyword(['hello','hi']).addAnswer('Ey! welcome')\n"}, {"language": "language-ts", "code": "// Example with single keyword\naddKeyword('hello').addAnswer('Ey! welcome')\n\n// Example with multi keywords\naddKeyword(['hello','hi']).addAnswer('Ey! welcome')\n"}, {"language": "language-ts", "code": "import { addKeyword, MemoryDB, createProvider, createFlow } from '@builderbot/bot'\nimport { BaileysProvider } from '@builderbot/provider-baileys'\n\n// ...stuff code...\n\nconst main = async () => {\n    \n    await createBot({\n        database: new MemoryDB(),\n        provider: createProvider(BaileysProvider),\n        flow: createFlow([flowDemo])\n    })\n}\n\nmain()\n"}, {"language": "language-ts", "code": "import { addKeyword, MemoryDB, createProvider, createFlow } from '@builderbot/bot'\nimport { BaileysProvider } from '@builderbot/provider-baileys'\n\n// ...stuff code...\n\nconst main = async () => {\n    \n    await createBot({\n        database: new MemoryDB(),\n        provider: createProvider(BaileysProvider),\n        flow: createFlow([flowDemo])\n    })\n}\n\nmain()\n"}], "success": true, "error_message": ""}, {"url": "https://www.builderbot.app/en/uses-cases", "title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/uses-cases#>)\n  * [Support](https://www.builderbot.app/en/</en/uses-cases#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n      * [How to Update to the Latest Version](https://www.builderbot.app/en/</en/uses-cases#how-to-update-to-the-latest-version>)\n      * [My first bot](https://www.builderbot.app/en/</en/uses-cases#my-first-bot>)\n      * [Conversational historystate](https://www.builderbot.app/en/</en/uses-cases#conversational-history>)\n      * [Dynamic MessagesflowDynamic](https://www.builderbot.app/en/</en/uses-cases#dynamic-messages>)\n      * [Send File](https://www.builderbot.app/en/</en/uses-cases#send-file>)\n      * [Switch to another flowgotoFlow](https://www.builderbot.app/en/</en/uses-cases#switch-to-another-flow>)\n      * [Turn off bot a certain userstate](https://www.builderbot.app/en/</en/uses-cases#turn-off-bot-a-certain-user>)\n      * [Turn off for everyonestate](https://www.builderbot.app/en/</en/uses-cases#turn-off-for-everyone>)\n      * [Bot Self-Interactionstate](https://www.builderbot.app/en/</en/uses-cases#bot-self-interaction>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/uses-cases#>)\n\n\n# Examples\nBelow you will find different examples showing the implementation in different use cases. These examples have been compiled based on the community, feel free to post an example that you like or that you think would be useful for new people.\n## [How to Update to the Latest Version](https://www.builderbot.app/en/</en/uses-cases#how-to-update-to-the-latest-version>)\nTo ensure you're using the most up-to-date features and bug fixes, it's important to keep your BuilderBot installation current. Follow the steps below to update to the latest version. To keep your project up to date, make sure to run the command to update the core and the corresponding provider\n```\npnpminstall@builderbot/bot@latest\npnpminstall@builderbot/provider-baileys@latest\npnpminstall@builderbot/provider-wppconnect@latest\npnpminstall@builderbot/provider-venom@latest\npnpminstall@builderbot/provider-meta@latest\npnpminstall@builderbot/provider-twilio@latest\n\n```\nCopyCopied!\n## [My first bot](https://www.builderbot.app/en/</en/uses-cases#my-first-bot>)\nThe following code represents the quick use of a bot that when you type the word `hi`, greets you with a welcome message and asks you for your name and then returns a funny image\n### app.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword, MemoryDB } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\nconstwelcomeFlow=addKeyword<BaileysProvider,MemoryDB>(['hi'])\n.addAnswer('Ey! welcome')\n.addAnswer('Your name is?', { capture:true },async (ctx, { flowDynamic }) => {\nawaitflowDynamic([`nice! ${ctx.body}`,'I will send you a funny image'])\n  })\n.addAction(async(_ , {flowDynamic}) => {\nconstdataApi=awaitfetch(`https://shibe.online/api/shibes?count=1&urls=true&httpsUrls=true`)\nconst [imageUrl] =awaitdataApi.json()\nawaitflowDynamic([{body:'😜', media: imageUrl}])\n  })\n\nconstmain=async () => {\nconstadapterDB=newMemoryDB()\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(BaileysProvider)\nadapterProvider.initHttpServer(3000)\nawaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\n}\nmain()\n\n```\nCopyCopied!\nstate\n## [Conversational history](https://www.builderbot.app/en/</en/uses-cases#conversational-history>)\nOften, we will need to manage conversations and keep the context in a memory called `state` which is volatile and accessible from any function executed in a stream.\n```\nconstwelcomeFlow=addKeyword(['hello'])\n.addAnswer(\n\"¿What's your name?\",\n     {capture:true},\nasync (ctx, { flowDynamic, state }) => {\nawaitstate.update({ name:ctx.body })\nawaitflowDynamic('Thanks for giving me your name!')\n     }\n   )\n.addAnswer(\n'¿How old are you?',\n      {capture:true},\nasync (ctx, { flowDynamic, state }) => {\nconstname=state.get('name')\nawaitstate.update({ age:ctx.body })\nawaitflowDynamic(`Thanks for sharing your age! ${name}`)\n     }\n   )\n.addAnswer('Here is your data:',null,async (_, { flowDynamic, state }) => {\nconstmyState=state.getMyState()\nawaitflowDynamic(`Name: ${myState.name} Age: ${myState.age}`)\n   })\n\n```\nCopyCopied!\nflowDynamic\n## [Dynamic Messages](https://www.builderbot.app/en/</en/uses-cases#dynamic-messages>)\nIn other occasions we need to send messages in a dynamic way of data that can be variable, below you can see an example of how you should do it and how you should NOT do it.\n❌ Avoid it this, does not work because addAnswer serializes the content at the start of execution.\n```\nlet name =''\nconstflow=addKeyword('hello')\n.addAnswer(`What is your name?`, { capture:true },async (ctx) => {\n    name =ctx.body\n  })\n.addAnswer(`Your name is: ${name}`)\n\n```\nCopyCopied!\nIf you want to send a dynamic message use flowDynamic.\n```\nconstflow=addKeyword('hello')\n.addAnswer(`What is your name?`, { capture:true },async (ctx, { state }) => {\nawaitstate.update({ name:ctx.body })\n  })\n.addAction(async (ctx, { state, flowDynamic }) => {\nconstname=state.get('name')\nawaitflowDynamic(`Your name is: ${name}`)\n  })\n\n```\nCopyCopied!\n## [Send File](https://www.builderbot.app/en/</en/uses-cases#send-file>)\nWhen you want to **send an image, audio** , file or any other file you can do it this way. It is **important** to note that the URL must be **publicly accessible**.\n```\nconstflow=addKeyword('hello')\n.addAnswer(`Send image from URL`,\n    { media:'https://i.imgur.com/0HpzsEm.png' }\n  )\n.addAnswer(`Send video from Local`,\n    { media:join(process.cwd(),'assets','sample.png') }\n  )\n.addAnswer(`Send video from URL`,\n    { media:'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4' }\n  )\n.addAnswer(`Send file from URL`,\n    { media:'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf' }\n  )\n\n```\nCopyCopied!\nOther ways to use when the route is coming from a dynamic data source\n```\nconstflow=addKeyword('hello')\n.addAction(async (_,{flowDynamic}) => {\n// ...db get source...\nawaitflowDynamic([\n      {body:'This is an image', media:'https://i.imgur.com/0HpzsEm.png'}\n    ])\nawaitflowDynamic([\n      {body:'This is a video', media:'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4'}\n    ])\n  })\n\n```\nCopyCopied!\nIf you need to send a file that is stored locally you can do that too. The use of `join` is recommended to ensure correct directory concatenation.\n```\nconstflow=addKeyword('hello')\n.addAction(async (_,{flowDynamic}) => {\nconstpathLocal=join('assets','doc.pdf')\n// pathLocal = c:/doc.pdf\nawaitflowDynamic([\n      {body:'This is a video', media: pathLocal }\n    ])\n  })\n\n```\nCopyCopied!\ngotoFlow\n## [Switch to another flow](https://www.builderbot.app/en/</en/uses-cases#switch-to-another-flow>)\nIf you want to divert a conversational flow to another logic flow based on a response input you can do it in this way:\n```\n\nconstflowToA=addKeyword(EVENTS.ACTION).addAnswer('Here we have Option A!')\nconstflowToB=addKeyword(EVENTS.ACTION).addAnswer('Here we have Option B!')\nconstflowToC=addKeyword(EVENTS.ACTION).addAnswer('Here we have Option C!')\nconstflowDefault=addKeyword(EVENTS.ACTION).addAnswer(\"We don't have that Option 🤔\")\nconstflow=addKeyword('order')\n.addAnswer(\n    [\n`Which one is the best option for you?`,\n`Type **A**`,\n`Type **B**`,\n`Type **C**`,\n    ],\n    { capture:true }\n  )\n.addAnswer(`Thanks for you answer`,async (ctx, {gotoFlow})=> {\nconstuserAnswer=ctx.body\nif(userAnswer ==='A'){\nreturngotoFlow(flowToA)\n    } \nif(userAnswer ==='B'){\nreturngotoFlow(flowToB)\n    } \nif(userAnswer ==='C'){\nreturngotoFlow(flowToC)\n    } \nreturngotoFlow(flowDefault)\n  })\n.addAnswer(`this message will not be sent`)\n\n```\nCopyCopied!\n❌ This does not work, the invocation of the gotoFlow function must necessarily include a return.\n```\n\n//...Previous code...\n....addAnswer(`Thanks for you answer`,async (ctx, {gotoFlow})=> {\ngotoFlow(flowToA)\n  })\n.addAnswer(`this message will not be sent`)\n\n```\nCopyCopied!\nThis does work\n```\n\n//...Previous code...\n....addAnswer(`Thanks for you answer`,async (ctx, {gotoFlow})=> {\nreturngotoFlow(flowToA)\n  })\n.addAnswer(`this message will not be sent`)\n\n```\nCopyCopied!\nstate\n## [Turn off bot a certain user](https://www.builderbot.app/en/</en/uses-cases#turn-off-bot-a-certain-user>)\nSometimes we will need to turn off the bot for a certain user, so that we can have a conversation with the client without the bot interfering.\n```\nconstflow=addKeyword<BaileysProvider>('magic keyword')\n.addAction(async (_, { state, endFlow }) => {\nconstbotOffForThisUser=state.get<boolean>('botOffForThisUser')\nawaitstate.update({botOffForThisUser:!botOffForThisUser})\nif(botOffForThisUser) returnendFlow()\n })\n.addAnswer('Hello!')\n\n```\nCopyCopied!\nstate\n## [Turn off for everyone](https://www.builderbot.app/en/</en/uses-cases#turn-off-for-everyone>)\nSometimes we will need to disable the bot for all people, without the need to shut down the server or stop the script.\n```\nconstflow=addKeyword<BaileysProvider>('botoff')\n.addAction(async (_, { globalState, endFlow }) => {\nconstbotOffForEveryOne=globalState.get<boolean>('botOffForEveryOne')\nawaitglobalState.update({botOffForEveryOne:!botOffForEveryOne})\nif(botOffForEveryOne) returnendFlow()\n })\n.addAnswer('Hello!')\n\n```\nCopyCopied!\nstate\n## [Bot Self-Interaction](https://www.builderbot.app/en/</en/uses-cases#bot-self-interaction>)\nIn certain scenarios, it is necessary for the bot's phone number (host) to be able to interact within logical flows. To achieve this, we have several configurable options:\n  * **host:** This is used when you want the bot to be able to respond to messages in the same chat with itself. For example, if the bot's number is 0000, it will be able to send and receive messages to/from 0000.\n  * **both:** This option allows both the bot and you (the developer/administrator) to intervene in the chat of a person interacting with the bot.\n  * **none:** (default option) Only allows interaction between the user and the bot, without intervention from the host number.\n\n\n### app.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword, MemoryDB } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\nconstmain=async () => {\nconstadapterDB=newMemoryDB()\nconstadapterFlow=createFlow([...])\nconstadapterProvider=createProvider(BaileysProvider, {\n     writeMyself:'host'as'none'|'host'|'both'\n  })\nadapterProvider.initHttpServer(3000)\nawaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\n}\nmain()\n\n```\nCopyCopied!\n## [Guides](https://www.builderbot.app/en/</en/uses-cases#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/</plugins>)\n## [Resources](https://www.builderbot.app/en/</en/uses-cases#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "chunks": [{"content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "Sin título"}, "embedding": null}, {"content": "[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/uses-cases#>)\n  * [Support](https://www.builderbot.app/en/</en/uses-cases#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n      * [How to Update to the Latest Version](https://www.builderbot.app/en/</en/uses-cases#how-to-update-to-the-latest-version>)\n      * [My first bot](https://www.builderbot.app/en/</en/uses-cases#my-first-bot>)\n      * [Conversational historystate](https://www.builderbot.app/en/</en/uses-cases#conversational-history>)\n      * [Dynamic MessagesflowDynamic](https://www.builderbot.app/en/</en/uses-cases#dynamic-messages>)\n      * [Send File](https://www.builderbot.app/en/</en/uses-cases#send-file>)\n      * [Switch to another flowgotoFlow](https://www.builderbot.app/en/</en/uses-cases#switch-to-another-flow>)\n      * [Turn off bot a certain userstate](https://www.builderbot.app/en/</en/uses-cases#turn-off-bot-a-certain-user>)\n      * [Turn off for everyonestate](https://www.builderbot.app/en/</en/uses-cases#turn-off-for-everyone>)\n      * [Bot Self-Interactionstate](https://www.builderbot.app/en/</en/uses-cases#bot-self-interaction>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/uses-cases#>)", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "Sin título"}, "embedding": null}, {"content": "# Examples\nBelow you will find different examples showing the implementation in different use cases. These examples have been compiled based on the community, feel free to post an example that you like or that you think would be useful for new people.\n", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "Examples"}, "embedding": null}, {"content": "## [How to Update to the Latest Version](https://www.builderbot.app/en/</en/uses-cases#how-to-update-to-the-latest-version>)\nTo ensure you're using the most up-to-date features and bug fixes, it's important to keep your BuilderBot installation current. Follow the steps below to update to the latest version. To keep your project up to date, make sure to run the command to update the core and the corresponding provider\n```\npnpminstall@builderbot/bot@latest\npnpminstall@builderbot/provider-baileys@latest\npnpminstall@builderbot/provider-wppconnect@latest\npnpminstall@builderbot/provider-venom@latest\npnpminstall@builderbot/provider-meta@latest\npnpminstall@builderbot/provider-twilio@latest\n\n```\nCopyCopied!\n", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[How to Update to the Latest Version](https://www.builderbot.app/en/</en/uses-cases#how-to-update-to-the-latest-version>)"}, "embedding": null}, {"content": "## [My first bot](https://www.builderbot.app/en/</en/uses-cases#my-first-bot>)\nThe following code represents the quick use of a bot that when you type the word `hi`, greets you with a welcome message and asks you for your name and then returns a funny image\n### app.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword, MemoryDB } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\nconstwelcomeFlow=addKeyword<BaileysProvider,MemoryDB>(['hi'])\n.addAnswer('Ey! welcome')\n.addAnswer('Your name is?', { capture:true },async (ctx, { flowDynamic }) => {\nawaitflowDynamic([`nice! ${ctx.body}`,'I will send you a funny image'])\n  })\n.addAction(async(_ , {flowDynamic}) => {\nconstdataApi=awaitfetch(`https://shibe.online/api/shibes?count=1&urls=true&httpsUrls=true`)\nconst [imageUrl] =awaitdataApi.json()\nawaitflowDynamic([{body:'😜', media: imageUrl}])\n  })", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[My first bot](https://www.builderbot.app/en/</en/uses-cases#my-first-bot>)"}, "embedding": null}, {"content": "constmain=async () => {\nconstadapterDB=newMemoryDB()\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(BaileysProvider)\nadapterProvider.initHttpServer(3000)\nawaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\n}\nmain()\n\n```\nCopyCopied!\nstate\n", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[My first bot](https://www.builderbot.app/en/</en/uses-cases#my-first-bot>)"}, "embedding": null}, {"content": "## [Conversational history](https://www.builderbot.app/en/</en/uses-cases#conversational-history>)\nOften, we will need to manage conversations and keep the context in a memory called `state` which is volatile and accessible from any function executed in a stream.\n```\nconstwelcomeFlow=addKeyword(['hello'])\n.addAnswer(\n\"¿What's your name?\",\n     {capture:true},\nasync (ctx, { flowDynamic, state }) => {\nawaitstate.update({ name:ctx.body })\nawaitflowDynamic('Thanks for giving me your name!')\n     }\n   )\n.addAnswer(\n'¿How old are you?',\n      {capture:true},\nasync (ctx, { flowDynamic, state }) => {\nconstname=state.get('name')\nawaitstate.update({ age:ctx.body })\nawaitflowDynamic(`Thanks for sharing your age! ${name}`)\n     }\n   )\n.addAnswer('Here is your data:',null,async (_, { flowDynamic, state }) => {\nconstmyState=state.getMyState()\nawaitflowDynamic(`Name: ${myState.name} Age: ${myState.age}`)\n   })\n\n```\nCopyCopied!\nflowDynamic\n", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[Conversational history](https://www.builderbot.app/en/</en/uses-cases#conversational-history>)"}, "embedding": null}, {"content": "## [Dynamic Messages](https://www.builderbot.app/en/</en/uses-cases#dynamic-messages>)\nIn other occasions we need to send messages in a dynamic way of data that can be variable, below you can see an example of how you should do it and how you should NOT do it.\n❌ Avoid it this, does not work because add<PERSON><PERSON>wer serializes the content at the start of execution.\n```\nlet name =''\nconstflow=addKeyword('hello')\n.addAnswer(`What is your name?`, { capture:true },async (ctx) => {\n    name =ctx.body\n  })\n.addAnswer(`Your name is: ${name}`)\n\n```\nCopyCopied!\nIf you want to send a dynamic message use flowDynamic.\n```\nconstflow=addKeyword('hello')\n.addAnswer(`What is your name?`, { capture:true },async (ctx, { state }) => {\nawaitstate.update({ name:ctx.body })\n  })\n.addAction(async (ctx, { state, flowDynamic }) => {\nconstname=state.get('name')\nawaitflowDynamic(`Your name is: ${name}`)\n  })\n\n```\nCopyCopied!\n", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[Dynamic Messages](https://www.builderbot.app/en/</en/uses-cases#dynamic-messages>)"}, "embedding": null}, {"content": "## [Send File](https://www.builderbot.app/en/</en/uses-cases#send-file>)\nWhen you want to **send an image, audio** , file or any other file you can do it this way. It is **important** to note that the URL must be **publicly accessible**.\n```\nconstflow=addKeyword('hello')\n.addAnswer(`Send image from URL`,\n    { media:'https://i.imgur.com/0HpzsEm.png' }\n  )\n.addAnswer(`Send video from Local`,\n    { media:join(process.cwd(),'assets','sample.png') }\n  )\n.addAnswer(`Send video from URL`,\n    { media:'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4' }\n  )\n.addAnswer(`Send file from URL`,\n    { media:'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf' }\n  )", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[Send File](https://www.builderbot.app/en/</en/uses-cases#send-file>)"}, "embedding": null}, {"content": "```\nCopyCopied!\nOther ways to use when the route is coming from a dynamic data source\n```\nconstflow=addKeyword('hello')\n.addAction(async (_,{flowDynamic}) => {\n// ...db get source...\nawaitflowDynamic([\n      {body:'This is an image', media:'https://i.imgur.com/0HpzsEm.png'}\n    ])\nawaitflowDynamic([\n      {body:'This is a video', media:'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4'}\n    ])\n  })\n\n```\nCopyCopied!\nIf you need to send a file that is stored locally you can do that too. The use of `join` is recommended to ensure correct directory concatenation.\n```\nconstflow=addKeyword('hello')\n.addAction(async (_,{flowDynamic}) => {\nconstpathLocal=join('assets','doc.pdf')\n// pathLocal = c:/doc.pdf\nawaitflowDynamic([\n      {body:'This is a video', media: pathLocal }\n    ])\n  })\n\n```\nCopyCopied!\ngotoFlow\n", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[Send File](https://www.builderbot.app/en/</en/uses-cases#send-file>)"}, "embedding": null}, {"content": "## [Switch to another flow](https://www.builderbot.app/en/</en/uses-cases#switch-to-another-flow>)\nIf you want to divert a conversational flow to another logic flow based on a response input you can do it in this way:\n```", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[Switch to another flow](https://www.builderbot.app/en/</en/uses-cases#switch-to-another-flow>)"}, "embedding": null}, {"content": "constflowToA=addKeyword(EVENTS.ACTION).addAnswer('Here we have Option A!')\nconstflowToB=addKeyword(EVENTS.ACTION).addAnswer('Here we have Option B!')\nconstflowToC=addKeyword(EVENTS.ACTION).addAnswer('Here we have Option C!')\nconstflowDefault=addKeyword(EVENTS.ACTION).addAnswer(\"We don't have that Option 🤔\")\nconstflow=addKeyword('order')\n.addAnswer(\n    [\n`Which one is the best option for you?`,\n`Type **A**`,\n`Type **B**`,\n`Type **C**`,\n    ],\n    { capture:true }\n  )\n.addAnswer(`Thanks for you answer`,async (ctx, {gotoFlow})=> {\nconstuserAnswer=ctx.body\nif(userAnswer ==='A'){\nreturngotoFlow(flowToA)\n    } \nif(userAnswer ==='B'){\nreturngotoFlow(flowToB)\n    } \nif(userAnswer ==='C'){\nreturngotoFlow(flowToC)\n    } \nreturngotoFlow(flowDefault)\n  })\n.addAnswer(`this message will not be sent`)\n\n```\nCopyCopied!\n❌ This does not work, the invocation of the gotoFlow function must necessarily include a return.\n```", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[Switch to another flow](https://www.builderbot.app/en/</en/uses-cases#switch-to-another-flow>)"}, "embedding": null}, {"content": "//...Previous code...\n....addAnswer(`Thanks for you answer`,async (ctx, {gotoFlow})=> {\ngotoFlow(flowToA)\n  })\n.addAnswer(`this message will not be sent`)\n\n```\nCopyCopied!\nThis does work\n```\n\n//...Previous code...\n....addAnswer(`Thanks for you answer`,async (ctx, {gotoFlow})=> {\nreturngotoFlow(flowToA)\n  })\n.addAnswer(`this message will not be sent`)\n\n```\nCopyCopied!\nstate\n", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[Switch to another flow](https://www.builderbot.app/en/</en/uses-cases#switch-to-another-flow>)"}, "embedding": null}, {"content": "## [Turn off bot a certain user](https://www.builderbot.app/en/</en/uses-cases#turn-off-bot-a-certain-user>)\nSometimes we will need to turn off the bot for a certain user, so that we can have a conversation with the client without the bot interfering.\n```\nconstflow=addKeyword<BaileysProvider>('magic keyword')\n.addAction(async (_, { state, endFlow }) => {\nconstbotOffForThisUser=state.get<boolean>('botOffForThisUser')\nawaitstate.update({botOffForThisUser:!botOffForThisUser})\nif(botOffForThisUser) returnendFlow()\n })\n.addAnswer('Hello!')\n\n```\nCopyCopied!\nstate\n", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[Turn off bot a certain user](https://www.builderbot.app/en/</en/uses-cases#turn-off-bot-a-certain-user>)"}, "embedding": null}, {"content": "## [Turn off for everyone](https://www.builderbot.app/en/</en/uses-cases#turn-off-for-everyone>)\nSometimes we will need to disable the bot for all people, without the need to shut down the server or stop the script.\n```\nconstflow=addKeyword<BaileysProvider>('botoff')\n.addAction(async (_, { globalState, endFlow }) => {\nconstbotOffForEveryOne=globalState.get<boolean>('botOffForEveryOne')\nawaitglobalState.update({botOffForEveryOne:!botOffForEveryOne})\nif(botOffForEveryOne) returnendFlow()\n })\n.addAnswer('Hello!')\n\n```\nCopyCopied!\nstate\n", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[Turn off for everyone](https://www.builderbot.app/en/</en/uses-cases#turn-off-for-everyone>)"}, "embedding": null}, {"content": "## [Bot Self-Interaction](https://www.builderbot.app/en/</en/uses-cases#bot-self-interaction>)\nIn certain scenarios, it is necessary for the bot's phone number (host) to be able to interact within logical flows. To achieve this, we have several configurable options:\n  * **host:** This is used when you want the bot to be able to respond to messages in the same chat with itself. For example, if the bot's number is 0000, it will be able to send and receive messages to/from 0000.\n  * **both:** This option allows both the bot and you (the developer/administrator) to intervene in the chat of a person interacting with the bot.\n  * **none:** (default option) Only allows interaction between the user and the bot, without intervention from the host number.", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[Bot Self-Interaction](https://www.builderbot.app/en/</en/uses-cases#bot-self-interaction>)"}, "embedding": null}, {"content": "### app.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword, MemoryDB } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\nconstmain=async () => {\nconstadapterDB=newMemoryDB()\nconstadapterFlow=createFlow([...])\nconstadapterProvider=createProvider(BaileysProvider, {\n     writeMyself:'host'as'none'|'host'|'both'\n  })\nadapterProvider.initHttpServer(3000)\nawaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\n}\nmain()\n\n```\nCopyCopied!\n", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[Bot Self-Interaction](https://www.builderbot.app/en/</en/uses-cases#bot-self-interaction>)"}, "embedding": null}, {"content": "## [Guides](https://www.builderbot.app/en/</en/uses-cases#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/</plugins>)\n", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[Guides](https://www.builderbot.app/en/</en/uses-cases#guides>)"}, "embedding": null}, {"content": "## [Resources](https://www.builderbot.app/en/</en/uses-cases#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "[Resources](https://www.builderbot.app/en/</en/uses-cases#resources>)"}, "embedding": null}], "headers": [{"text": "BuilderBot", "level": 4, "id": ""}, {"text": "BuilderBot", "level": 4, "id": ""}, {"text": "Start here", "level": 2, "id": ""}, {"text": "Basics", "level": 2, "id": ""}, {"text": "Built-in", "level": 2, "id": ""}, {"text": "Providers", "level": 2, "id": ""}, {"text": "Deploy", "level": 2, "id": ""}, {"text": "Recipes", "level": 2, "id": ""}, {"text": "Tutorials", "level": 2, "id": ""}, {"text": "Community Contribute", "level": 2, "id": ""}, {"text": "Plugins", "level": 2, "id": ""}, {"text": "Examples", "level": 1, "id": ""}, {"text": "How to Update to the Latest Version", "level": 2, "id": "how-to-update-to-the-latest-version"}, {"text": "My first bot", "level": 2, "id": "my-first-bot"}, {"text": "app.ts", "level": 3, "id": ""}, {"text": "Conversational history", "level": 2, "id": "conversational-history"}, {"text": "Dynamic Messages", "level": 2, "id": "dynamic-messages"}, {"text": "Send File", "level": 2, "id": "send-file"}, {"text": "Switch to another flow", "level": 2, "id": "switch-to-another-flow"}, {"text": "Turn off bot a certain user", "level": 2, "id": "turn-off-bot-a-certain-user"}, {"text": "Turn off for everyone", "level": 2, "id": "turn-off-for-everyone"}, {"text": "Bot Self-Interaction", "level": 2, "id": "bot-self-interaction"}, {"text": "app.ts", "level": 3, "id": ""}, {"text": "Guides", "level": 2, "id": "guides"}, {"text": "My first chatbot", "level": 3, "id": ""}, {"text": "Concepts", "level": 3, "id": ""}, {"text": "Add Functions", "level": 3, "id": ""}, {"text": "Plugins", "level": 3, "id": ""}, {"text": "Resources", "level": 2, "id": "resources"}, {"text": "Modularize", "level": 3, "id": ""}, {"text": "Send Message", "level": 3, "id": ""}, {"text": "Dockerizer", "level": 3, "id": ""}, {"text": "Events", "level": 3, "id": ""}], "code_blocks": [{"language": "language-bash", "code": "pnpm install @builderbot/bot@latest \npnpm install @builderbot/provider-baileys@latest\npnpm install @builderbot/provider-wppconnect@latest\npnpm install @builderbot/provider-venom@latest\npnpm install @builderbot/provider-meta@latest\npnpm install @builderbot/provider-twilio@latest\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, create<PERSON>low, addKeyword, MemoryDB } from '@builderbot/bot'\nimport { BaileysProvider } from '@builderbot/provider-baileys'\n\nconst welcomeFlow = addKeyword<BaileysProvider, MemoryDB>(['hi'])\n    .addAnswer('Ey! welcome')\n    .addAnswer('Your name is?', { capture: true }, async (ctx, { flowDynamic }) => {\n        await flowDynamic([`nice! ${ctx.body}`,'I will send you a funny image'])\n    })\n    .addAction(async(_ , {flowDynamic}) => {\n        const dataApi = await fetch(`https://shibe.online/api/shibes?count=1&urls=true&httpsUrls=true`)\n        const [imageUrl] = await dataApi.json()\n        await flowDynamic([{body:'😜', media: imageUrl}])\n    })\n\n\nconst main = async () => {\n    const adapterDB = new MemoryDB()\n    const adapterFlow = createFlow([welcomeFlow])\n    const adapterProvider = createProvider(BaileysProvider)\n\n    adapterProvider.initHttpServer(3000)\n\n    await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n}\n\nmain()\n"}, {"language": "language-ts", "code": "  const welcomeFlow = addKeyword(['hello'])\n      .addAnswer(\n          \"¿What's your name?\",\n          {capture: true},\n          async (ctx, { flowDynamic, state }) => {\n              await state.update({ name: ctx.body })\n              await flowDynamic('Thanks for giving me your name!')\n          }\n      )\n      .addAnswer(\n          '¿How old are you?',\n           {capture: true},\n          async (ctx, { flowDynamic, state }) => {\n              const name = state.get('name')\n              await state.update({ age: ctx.body })\n              await flowDynamic(`Thanks for sharing your age! ${name}`)\n          }\n      )\n      .addAnswer('Here is your data:', null, async (_, { flowDynamic, state }) => {\n          const myState = state.getMyState()\n          await flowDynamic(`Name: ${myState.name} Age: ${myState.age}`)\n      })\n"}, {"language": "language-ts", "code": "let name = ''\n\nconst flow = addKeyword('hello')\n    .addAnswer(`What is your name?`, { capture: true }, async (ctx) => {\n        name = ctx.body\n    })\n    .addAnswer(`Your name is: ${name}`)\n"}, {"language": "language-ts", "code": "const flow = addKeyword('hello')\n    .addAnswer(`What is your name?`, { capture: true }, async (ctx, { state }) => {\n        await state.update({ name: ctx.body })\n    })\n    .addAction(async (ctx, { state, flowDynamic }) => {\n        const name = state.get('name')\n        await flowDynamic(`Your name is: ${name}`)\n    })\n"}, {"language": "language-ts", "code": "const flow = addKeyword('hello')\n    .addAnswer(`Send image from URL`, \n        { media: 'https://i.imgur.com/0HpzsEm.png' }\n    )\n    .addAnswer(`Send video from Local`, \n        { media: join(process.cwd(), 'assets', 'sample.png') }\n    )\n    .addAnswer(`Send video from URL`, \n        { media: 'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4' }\n    )\n    .addAnswer(`Send file from URL`, \n        { media: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf' }\n    )\n"}, {"language": "language-ts", "code": "const flow = addKeyword('hello')\n    .addAction(async (_,{flowDynamic}) => {\n        // ...db get source...\n        await flowDynamic([\n            {body:'This is an image', media:'https://i.imgur.com/0HpzsEm.png'}\n        ])\n        await flowDynamic([\n            {body:'This is a video', media:'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4'}\n        ])\n    })\n\n"}, {"language": "language-ts", "code": "const flow = addKeyword('hello')\n    .addAction(async (_,{flowDynamic}) => {\n        const pathLocal = join('assets','doc.pdf')\n        // pathLocal = c:/doc.pdf\n        await flowDynamic([\n            {body:'This is a video', media: pathLocal }\n        ])\n    })\n\n"}, {"language": "language-ts", "code": "\nconst flowToA = addKeyword(EVENTS.ACTION).addAnswer('Here we have Option A!')\nconst flowToB = addKeyword(EVENTS.ACTION).addAnswer('Here we have Option B!')\nconst flowToC = addKeyword(EVENTS.ACTION).addAnswer('Here we have Option C!')\n\nconst flowDefault = addKeyword(EVENTS.ACTION).addAnswer(\"We don't have that Option 🤔\")\n\nconst flow = addKeyword('order')\n    .addAnswer(\n        [\n            `Which one is the best option for you?`,\n            `Type **A**`,\n            `Type **B**`,\n            `Type **C**`,\n        ], \n        { capture: true }\n    )\n    .addAnswer(`Thanks for you answer`,async (ctx, {gotoFlow})=> {\n        const userAnswer = ctx.body\n        if(userAnswer === 'A'){\n            return gotoFlow(flowToA)\n        } \n        if(userAnswer === 'B'){\n            return gotoFlow(flowToB)\n        } \n        if(userAnswer === 'C'){\n            return gotoFlow(flowToC)\n        } \n        return gotoFlow(flowDefault)\n\n    })\n    .addAnswer(`this message will not be sent`)\n"}, {"language": "language-ts", "code": "\n//...Previous code...\n    ....addAnswer(`Thanks for you answer`,async (ctx, {gotoFlow})=> {\n        gotoFlow(flowToA)\n\n    })\n    .addAnswer(`this message will not be sent`)\n"}, {"language": "language-ts", "code": "\n//...Previous code...\n    ....addAnswer(`Thanks for you answer`,async (ctx, {gotoFlow})=> {\n        return gotoFlow(flowToA)\n\n    })\n    .addAnswer(`this message will not be sent`)\n"}, {"language": "language-ts", "code": "const flow = addKeyword<BaileysProvider>('magic keyword')\n  .addAction(async (_, { state, endFlow }) => {\n      const botOffForThisUser = state.get<boolean>('botOffForThisUser')\n      await state.update({botOffForThisUser:!botOffForThisUser})\n      if(botOffForThisUser) return endFlow()\n  })\n  .addAnswer('Hello!')\n"}, {"language": "language-ts", "code": "const flow = addKeyword<BaileysProvider>('botoff')\n  .addAction(async (_, { globalState, endFlow }) => {\n  const botOffForEveryOne = globalState.get<boolean>('botOffForEveryOne')\n    await globalState.update({botOffForEveryOne:!botOffForEveryOne})\n    if(botOffForEveryOne) return endFlow()\n  })\n  .addAnswer('Hello!')\n  \n"}, {"language": "language-ts", "code": "import { createBot, createProvider, create<PERSON>low, addKeyword, MemoryDB } from '@builderbot/bot'\nimport { BaileysProvider } from '@builderbot/provider-baileys'\n\nconst main = async () => {\n    const adapterDB = new MemoryDB()\n    const adapterFlow = createFlow([...])\n    const adapterProvider = createProvider(BaileysProvider, {\n          writeMyself: 'host' as 'none' | 'host' | 'both'\n    })\n\n    adapterProvider.initHttpServer(3000)\n\n    await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n}\n\nmain()\n"}], "success": true, "error_message": ""}]