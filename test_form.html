<!DOCTYPE html>
<html>
<head>
    <title>Test Form with File Upload</title>
</head>
<body>
    <h1>Registration Form</h1>
    
    <!-- Form with various field types -->
    <form action="/submit" method="post" enctype="multipart/form-data">
        <!-- Personal Information -->
        <fieldset>
            <legend>Personal Information</legend>
            
            <label for="name">Full Name:</label>
            <input type="text" id="name" name="name" required placeholder="Enter your full name">
            <br><br>
            
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required placeholder="<EMAIL>">
            <br><br>
            
            <label for="phone">Phone:</label>
            <input type="tel" id="phone" name="phone" pattern="[0-9]{10}" placeholder="1234567890">
            <br><br>
        </fieldset>
        
        <!-- File Uploads -->
        <fieldset>
            <legend>Document Upload</legend>
            
            <!-- Single file upload -->
            <label for="profile">Profile Picture:</label>
            <input type="file" id="profile" name="profile" accept="image/*">
            <br><br>
            
            <!-- Multiple file upload -->
            <label for="documents">Additional Documents:</label>
            <input type="file" id="documents" name="documents" multiple accept=".pdf,.doc,.docx">
            <br><br>
            
            <!-- Camera capture -->
            <label for="photo">Take Photo:</label>
            <input type="file" id="photo" name="photo" accept="image/*" capture="user">
            <br><br>
        </fieldset>
        
        <!-- Preferences -->
        <fieldset>
            <legend>Preferences</legend>
            
            <label for="department">Department:</label>
            <select id="department" name="department" required>
                <option value="">Select a department</option>
                <option value="hr">Human Resources</option>
                <option value="it">Information Technology</option>
                <option value="finance">Finance</option>
            </select>
            <br><br>
            
            <!-- Multiple checkboxes -->
            <label>Skills:</label><br>
            <input type="checkbox" id="skill1" name="skills" value="python">
            <label for="skill1">Python</label>
            <input type="checkbox" id="skill2" name="skills" value="javascript">
            <label for="skill2">JavaScript</label>
            <input type="checkbox" id="skill3" name="skills" value="java">
            <label for="skill3">Java</label>
            <br><br>
            
            <!-- Text area -->
            <label for="comments">Additional Comments:</label><br>
            <textarea id="comments" name="comments" rows="4" cols="50" maxlength="500" 
                      placeholder="Enter any additional information here..."></textarea>
        </fieldset>
        
        <br>
        <input type="submit" value="Submit">
        <input type="reset" value="Reset Form">
    </form>
</body>
</html>
