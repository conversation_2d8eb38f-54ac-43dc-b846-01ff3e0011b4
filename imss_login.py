import asyncio
from crawl4ai import Async<PERSON>eb<PERSON>rawler
from pathlib import Path
import os
from rich import print
from rich.console import Console

async def login_imss(cer_path: str, key_path: str, password: str, user_id: str):
    """
    Intenta hacer login en el IMSS usando certificado
    
    Args:
        cer_path: Ruta al archivo .cer
        key_path: Ruta al archivo .key
        password: Contraseña de la FIEL
        user_id: ID de usuario
    """
    console = Console()
    
    # Verificar que los archivos existen
    if not os.path.exists(cer_path):
        console.print(f"[red]Error: No se encuentra el archivo certificado: {cer_path}[/red]")
        return False
        
    if not os.path.exists(key_path):
        console.print(f"[red]Error: No se encuentra el archivo llave: {key_path}[/red]")
        return False
    
    # Configurar el crawler
    async with AsyncWebCrawler(
        browser_required=True,
        javascript_enabled=True,
        wait_until='networkidle',
        timeout=30000  # 30 segundos de timeout
    ) as crawler:
        try:
            url = "http://idse.imss.gob.mx/imss/"
            console.print(f"\n[bold blue]Intentando login en {url}...[/bold blue]")
            
            # Cargar la página inicial
            result = await crawler.arun(url=url)
            
            if result and result.html:
                # Llenar el formulario
                await crawler.fill_form(
                    selector="form",
                    data={
                        "certificado": cer_path,
                        "llave": key_path,
                        "idUsuario": user_id,
                        "password": password
                    }
                )
                
                # Hacer click en el botón de login (ajusta el selector según sea necesario)
                await crawler.click("button[type='submit']")
                
                # Esperar a que la página cargue
                await crawler.wait_for_navigation()
                
                # Verificar si el login fue exitoso
                # Esto dependerá de cómo podemos detectar que estamos logueados
                # Por ejemplo, buscando algún elemento que solo aparezca cuando estamos dentro
                success = await crawler.exists_element("texto-o-elemento-que-indica-login-exitoso")
                
                if success:
                    console.print("[green]Login exitoso![/green]")
                    return True
                else:
                    console.print("[red]Error: Login fallido[/red]")
                    return False
                    
        except Exception as e:
            console.print(f"[red]Error durante el login: {str(e)}[/red]")
            return False

async def main():
    # Rutas a los archivos (ajusta según tu caso)
    cer_path = "ruta/al/certificado.cer"
    key_path = "ruta/al/archivo.key"
    password = "tu-contraseña-fiel"
    user_id = "tu-usuario"
    
    success = await login_imss(
        cer_path=cer_path,
        key_path=key_path,
        password=password,
        user_id=user_id
    )
    
    if success:
        print("\n[bold green]✓ Login completado exitosamente[/bold green]")
    else:
        print("\n[bold red]✗ No se pudo completar el login[/bold red]")

if __name__ == "__main__":
    asyncio.run(main())
