import asyncio
from crawl4ai import Async<PERSON>eb<PERSON>rawler
from rich import print
import json
from bs4 import BeautifulSoup

async def main():
    # Create an instance of AsyncWebCrawler with advanced configuration
    async with AsyncWebCrawler(
        browser_required=True,
        max_concurrent_requests=5,
        request_delay=1,
        # Enable JavaScript rendering
        javascript_enabled=True,
        # Wait for network to be idle
        wait_until='networkidle',
        # Extract specific elements using CSS selectors
        css_selectors={
            'title': 'h1',  # Extract main title
            'paragraphs': 'p',  # Extract all paragraphs
            'links': 'a',  # Extract all links
            'images': 'img'  # Extract all images
        },
        # Configure text extraction
        text_mode=True,
        extract_metadata=True,
        extract_links=True
    ) as crawler:
        # List of URLs to crawl
        urls = [
            "https://github.com/rcereceda/computrabajo/blob/master/README.md"
        ]
        
        results = []
        
        for url in urls:
            try:
                print(f"\n[bold blue]Crawling {url}...[/bold blue]")
                
                # Run the crawler
                result = await crawler.arun(url=url)
                
                # Parse HTML content to extract images
                soup = BeautifulSoup(result.html, 'html.parser') if hasattr(result, 'html') else None
                images = []
                if soup:
                    for img in soup.find_all('img'):
                        src = img.get('src', '')
                        alt = img.get('alt', '')
                        if src:
                            images.append({
                                'src': src,
                                'alt': alt
                            })
                
                # Extract and format the data
                page_data = {
                    'url': url,
                    'title': result.metadata.get('title', 'N/A') if result.metadata else 'N/A',
                    'description': result.metadata.get('description', 'N/A') if result.metadata else 'N/A',
                    'content': result.markdown,
                    'links': list(result.links) if result.links else [],
                    'images': images
                }
                
                results.append(page_data)
                
                # Print a summary
                print(f"[green]✓[/green] Title: {page_data['title']}")
                print(f"[green]✓[/green] Description: {page_data['description']}")
                print(f"[green]✓[/green] Found {len(page_data['links'])} links")
                print(f"[green]✓[/green] Found {len(page_data['images'])} images")
                
            except Exception as e:
                print(f"[red]Error crawling {url}: {str(e)}[/red]")
                print(f"[yellow]Continuing with next URL...[/yellow]")
        
        # Save results to a JSON file
        output_file = 'crawling_results.json'
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"\n[bold green]✓ Results saved to {output_file}[/bold green]")
        except Exception as e:
            print(f"[red]Error saving results to {output_file}: {str(e)}[/red]")

if __name__ == "__main__":
    asyncio.run(main())
