import asyncio
from crawl4ai import As<PERSON><PERSON>eb<PERSON>rawler
from bs4 import Beautiful<PERSON>oup
from rich import print
from rich.table import Table
from rich.console import Console
import json

async def analyze_forms(url):
    """Analyze forms in a webpage using crawl4ai."""
    console = Console()
    
    async with AsyncWebCrawler(
        browser_required=True,
        javascript_enabled=True,
        wait_until='networkidle'
    ) as crawler:
        try:
            print(f"\n[bold blue]Analyzing forms on {url}...[/bold blue]")
            result = await crawler.arun(url=url)
            
            if result and result.html:
                # Parse HTML with BeautifulSoup
                soup = BeautifulSoup(result.html, 'html.parser')
                forms = []
                
                # Find all forms
                for form in soup.find_all('form'):
                    form_data = {
                        'action': form.get('action', ''),
                        'method': form.get('method', 'get'),
                        'enctype': form.get('enctype', ''),
                        'fields': []
                    }
                    
                    # Find all input fields
                    for field in form.find_all(['input', 'textarea', 'select']):
                        field_data = {
                            'type': field.get('type', 'text') if field.name == 'input' else field.name,
                            'name': field.get('name', ''),
                            'required': field.get('required') is not None,
                            'accept': field.get('accept', ''),
                            'multiple': field.get('multiple') is not None
                        }
                        form_data['fields'].append(field_data)
                    
                    forms.append(form_data)
                
                if forms:
                    console.print(f"\n[green]Found {len(forms)} form(s)[/green]")
                    
                    for i, form in enumerate(forms, 1):
                        # Form details table
                        details = Table(title=f"Form {i}", show_header=True, header_style="bold magenta")
                        details.add_column("Property", style="dim")
                        details.add_column("Value")
                        
                        details.add_row("Method", form['method'].upper())
                        details.add_row("Action", form['action'])
                        details.add_row("Encoding", form['enctype'] or '(default)')
                        
                        console.print(details)
                        
                        # Fields table
                        fields = Table(title="Fields", show_header=True, header_style="bold magenta")
                        fields.add_column("Type", style="cyan")
                        fields.add_column("Name")
                        fields.add_column("Required", justify="center")
                        fields.add_column("Properties")
                        
                        for field in form['fields']:
                            properties = []
                            if field['type'] == 'file':
                                properties.append(f"Accept: {field['accept'] or 'any'}")
                                if field['multiple']:
                                    properties.append("Multiple files allowed")
                            
                            fields.add_row(
                                field['type'],
                                field['name'] or '(unnamed)',
                                '✓' if field['required'] else '',
                                '\n'.join(properties) or '(none)'
                            )
                        
                        console.print(fields)
                        console.print()
                    
                    return forms
                else:
                    console.print("[yellow]No forms found[/yellow]")
                    return []
                    
        except Exception as e:
            console.print(f"[red]Error analyzing {url}: {str(e)}[/red]")
            return []

async def main():
    urls = [
        "http://idse.imss.gob.mx/imss/"
    ]
    
    all_results = []
    
    for url in urls:
        forms = await analyze_forms(url)
        if forms:
            all_results.append({
                'url': url,
                'forms': forms
            })
    
    # Save results
    if all_results:
        with open('form_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2)
        print("\n[bold green]✓ Results saved to form_analysis.json[/bold green]")

if __name__ == "__main__":
    asyncio.run(main())
