import json
import os

def load_json_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def merge_docs():
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # List all smolagents doc files
    doc_files = [
        f for f in os.listdir(base_dir)
        if f.startswith('smolagents_docs') and f.endswith('.json')
        and not f.endswith('unified.json')
        and not f.endswith('complete.json')
    ]
    
    all_content = []
    
    # Load and merge content from all files
    for doc_file in doc_files:
        file_path = os.path.join(base_dir, doc_file)
        try:
            data = load_json_file(file_path)
            if isinstance(data, list):
                all_content.extend(data)
            else:
                all_content.append(data)
        except Exception as e:
            print(f"Error loading {doc_file}: {str(e)}")
    
    # Create unified structure with better content preservation
    unified_docs = {
        "title": "SmoLAgents Documentation",
        "description": "Complete documentation for SmoLAgents library",
        "pages": []
    }
    
    # Process and deduplicate content while preserving all fields
    seen_urls = set()
    for item in all_content:
        if 'url' in item and item['url'] not in seen_urls:
            seen_urls.add(item['url'])
            # Preserve all original fields
            page = item.copy()
            # Ensure required fields exist
            if 'content' not in page:
                page['content'] = ''
            if 'code_blocks' not in page:
                page['code_blocks'] = []
            if 'sections' not in page:
                page['sections'] = []
            unified_docs['pages'].append(page)
    
    # Save merged documentation
    output_path = os.path.join(base_dir, 'smolagents_docs_complete.json')
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(unified_docs, f, indent=2, ensure_ascii=False)
    
    print(f"Successfully merged {len(unified_docs['pages'])} pages into {output_path}")

if __name__ == '__main__':
    merge_docs()