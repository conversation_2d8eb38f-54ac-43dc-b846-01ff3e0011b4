# Crawl4AI Documentation Knowledge Agent

Un agente avanzado para extraer y procesar documentación web para su uso en bases de conocimiento y sistemas RAG (Retrieval Augmented Generation).

## Descripción

Este proyecto proporciona herramientas para:

1. **Extraer documentación web**: Utiliza web crawling asíncrono para extraer contenido de múltiples URLs.
2. **Procesar y estructurar el contenido**: Divide el contenido en fragmentos significativos y extrae metadatos.
3. **Generar bases de conocimiento**: Convierte la documentación extraída en JSON estructurado.
4. **Utilizar la base de conocimiento**: Implementa un sistema RAG simple para responder preguntas basadas en la documentación.

## Componentes

- `docs_knowledge_agent.py`: Agente principal para la extracción de documentación.
- `use_knowledge_base.py`: Sistema RAG simple para utilizar la base de conocimiento generada.
- `agent_coder.py`: Agente codificador para generar código y estructuras de proyectos.

## Requisitos

```
crawl4ai>=0.1.0
pydantic>=2.0.0
rich>=12.0.0
beautifulsoup4>=4.10.0
asyncio
```

Para la búsqueda semántica (opcional):
```
numpy
scikit-learn
sentence-transformers
```

## Instalación

```bash
pip install -r requirements.txt
```

## Uso

### Extracción de documentación

```bash
python docs_knowledge_agent.py --urls https://docs.python.org/3/tutorial/ --output python_docs_knowledge.json
```

Opciones:
- `--urls`: URLs de documentación a extraer (separadas por espacios)
- `--output`: Nombre del archivo de salida (por defecto: `knowledge_base.json`)
- `--cache-dir`: Directorio de caché (por defecto: `./.cache`)
- `--output-dir`: Directorio de salida (por defecto: `./knowledge_base`)
- `--llm-provider`: Proveedor de LLM opcional para extracción estructurada
- `--llm-api-key`: Clave API para el proveedor de LLM

### Uso de la base de conocimiento

```bash
python use_knowledge_base.py --kb ./knowledge_base/python_docs_knowledge.json --semantic
```

Opciones:
- `--kb`: Ruta al archivo JSON de la base de conocimiento
- `--semantic`: Usar búsqueda semántica (requiere bibliotecas adicionales)

### Agente Codificador

Este proyecto también incluye un agente codificador que puede utilizar la base de conocimiento extraída para generar código y estructuras de proyectos.

#### Uso del Agente Codificador

```bash
python agent_coder.py --kb ./knowledge_base/builderbot_docs.json --project-name "MiChatbot" --description "Un chatbot para atención al cliente" --output-dir ./proyectos
```

Opciones:
- `--kb`: Ruta al archivo JSON de la base de conocimiento
- `--project-name`: Nombre del proyecto a generar
- `--description`: Descripción del proyecto
- `--output-dir`: Directorio de salida para el proyecto (por defecto: `./projects`)

#### Flujo de trabajo completo

1. Extraer documentación:
```bash
python docs_knowledge_agent.py --urls-file builderbot_urls.txt --output builderbot_docs.json
```

2. Generar un proyecto utilizando la base de conocimiento:
```bash
python agent_coder.py --kb ./knowledge_base/builderbot_docs.json --project-name "AsistenteChatbot" --description "Un chatbot para responder preguntas sobre productos" --output-dir ./proyectos
```

## Características

### Extracción de documentación
- Crawling web asíncrono
- Extracción inteligente de contenido
- División en fragmentos para RAG
- Extracción de metadatos (encabezados, bloques de código, secciones)

### Sistema RAG
- Búsqueda por palabras clave
- Búsqueda semántica (con sentence-transformers)
- Respuestas basadas en fragmentos relevantes
- Referencias a las fuentes originales

## Ejemplos

### Extracción de documentación de Python
```bash
python docs_knowledge_agent.py --urls https://docs.python.org/3/tutorial/ --output python_tutorial.json
```

### Extracción de documentación de múltiples fuentes
```bash
python docs_knowledge_agent.py --urls https://docs.python.org/3/tutorial/ https://docs.python.org/3/library/ --output python_docs.json
```

### Consulta a la base de conocimiento
```bash
python use_knowledge_base.py --kb ./knowledge_base/python_tutorial.json
```

## Contribuciones

Las contribuciones son bienvenidas. Por favor, abre un issue para discutir los cambios propuestos.

## Licencia

MIT
