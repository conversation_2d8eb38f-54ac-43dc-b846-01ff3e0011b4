[{"url": "https://www.builderbot.app/en", "title": "BuilderBot.app Create a WhatsApp Chatbot, Without Limit", "content": "Get started with BuilderBot This is a free and open source framework with an intuitive and extensible way to create chatbot and smart apps that connect to different communication channels like Whatsapp , Telegram and others.\nWe have made an intuitive framework so you can have your first chatbot in minutes. Winner of the first prize at OpenExpo 2024 🏆 Quick Start To create quickly with the following command pnpm npm pnpm create builderbot@latest Copy Copied! Installation and requirements ⚡ Building an AI bot In this few minutes tutorial you can have your own chatbot with whatsapp and artificial intelligence to talk about your business. Learn how to create a bot with the new open ai assistants Code repository Quick Example In this example we can see the basis of a simple bot which responds to the keywords sent by a user, the words are: info, hello, hi . You can see how to create the bot and implement the flows . main.ts main.js import { createBot , createProvider , createFlow , addKeyword , MemoryDB } from '@builderbot/bot' import { BaileysProvider } from '@builderbot/provider-baileys' const welcomeFlow = addKeyword < BaileysProvider , MemoryDB >([ 'hello' , 'hi' ]) .addAnswer ( 'Ey! welcome' ) .addAnswer ( `Send image from URL` , { media : 'https://i.imgur.com/0HpzsEm.png' }) const main = async () => { const adapterDB = new MemoryDB () const adapterFlow = createFlow ([welcomeFlow]) const adapterProvider = createProvider (BaileysProvider) const { handleCtx , httpServer } = await createBot ({ flow : adapterFlow , provider : adapterProvider , database : adapterDB , }) httpServer ( 3000 ) adapterProvider . server .post ( '/v1/messages' , handleCtx ( async (bot , req , res) => { const { number , message } = req .body await bot .sendMessage (number , message , {}) return res .end ( 'send' ) })) } main () Copy Copied! Guides My first chatbot Learn how build your first chatbot in few minutes Read more Concepts Understand the essential concepts for building bots Read more Add Functions The key to learning how to write flows is add-functions. Read more Plugins Unlimitate and start implementing the community plugins. Read more Resources Modularize Learn how to modularise flows so that you can have a more maintainable bot. Send Message How to send a message via HTTP to start conversations, you can send multimedia as well. Dockerizer A good practice is to dockerise your bots to make them more maintainable and effective. Events Learning about events will make us more fluent when creating chatbots.", "chunks": [{"content": "Get started with BuilderBot This is a free and open source framework with an intuitive and extensible way to create chatbot and smart apps that connect to different communication channels like Whatsapp , Telegram and others.\nWe have made an intuitive framework so you can have your first chatbot in minutes. Winner of the first prize at OpenExpo 2024 🏆 Quick Start To create quickly with the following command pnpm npm pnpm create builderbot@latest Copy Copied! Installation and requirements ⚡ Building an AI bot In this few minutes tutorial you can have your own chatbot with whatsapp and artificial intelligence to talk about your business. Learn how to create a bot with the new open ai assistants Code repository Quick Example In this example we can see the basis of a simple bot which responds to the keywords sent by a user, the words are: info, hello, hi . You can see how to create the bot and implement the flows . main.ts main.js import { createBot , createProvider , createFlow , addKeyword , MemoryDB } from '@builderbot/bot' import { BaileysProvider } from '@builderbot/provider-baileys' const welcomeFlow = addKeyword < BaileysProvider , MemoryDB >([ 'hello' , 'hi' ]) .addAnswer ( 'Ey! welcome' ) .addAnswer ( `Send image from URL` , { media : 'https://i.imgur.com/0HpzsEm.png' }) const main = async () => { const adapterDB = new MemoryDB () const adapterFlow = createFlow ([welcomeFlow]) const adapterProvider = createProvider (BaileysProvider) const { handleCtx , httpServer } = await createBot ({ flow : adapterFlow , provider : adapterProvider , database : adapterDB , }) httpServer ( 3000 ) adapterProvider . server .post ( '/v1/messages' , handleCtx ( async (bot , req , res) => { const { number , message } = req .body await bot .sendMessage (number , message , {}) return res .end ( 'send' ) })) } main () Copy Copied! Guides My first chatbot Learn how build your first chatbot in few minutes Read more Concepts Understand the essential concepts for building bots Read more Add Functions The key to learning how to write flows is add-functions. Read more Plugins Unlimitate and start implementing the community plugins. Read more Resources Modularize Learn how to modularise flows so that you can have a more maintainable bot. Send Message How to send a message via HTTP to start conversations, you can send multimedia as well. Dockerizer A good practice is to dockerise your bots to make them more maintainable and effective. Events Learning about events will make us more fluent when creating chatbots.", "metadata": {"title": "BuilderBot.app Create a WhatsApp Chatbot, Without Limit", "url": "https://www.builderbot.app/en", "section": "Sin título"}, "embedding": null}], "headers": [{"level": 4, "text": "BuilderBot", "id": ""}, {"level": 4, "text": "BuilderBot", "id": ""}, {"level": 2, "text": "Start here", "id": ""}, {"level": 2, "text": "Basics", "id": ""}, {"level": 2, "text": "Built-in", "id": ""}, {"level": 2, "text": "Providers", "id": ""}, {"level": 2, "text": "Deploy", "id": ""}, {"level": 2, "text": "Recipes", "id": ""}, {"level": 2, "text": "Tutorials", "id": ""}, {"level": 2, "text": "Community Contribute", "id": ""}, {"level": 2, "text": "Plugins", "id": ""}, {"level": 1, "text": "Get started with BuilderBot", "id": ""}, {"level": 2, "text": "Quick Start", "id": "quick-start"}, {"level": 2, "text": "⚡ Building an AI bot", "id": "building-an-ai-bot"}, {"level": 3, "text": "Learn how to create a bot with the new open ai assistants", "id": ""}, {"level": 2, "text": "Quick Example", "id": ""}, {"level": 2, "text": "Guides", "id": "guides"}, {"level": 3, "text": "My first chatbot", "id": ""}, {"level": 3, "text": "Concepts", "id": ""}, {"level": 3, "text": "Add Functions", "id": ""}, {"level": 3, "text": "Plugins", "id": ""}, {"level": 2, "text": "Resources", "id": "resources"}, {"level": 3, "text": "Modularize", "id": ""}, {"level": 3, "text": "Send Message", "id": ""}, {"level": 3, "text": "Dockerizer", "id": ""}, {"level": 3, "text": "Events", "id": ""}], "code_blocks": [{"language": "overflow-x-auto", "code": "pnpmcreatebuilderbot@latest"}, {"language": "language-bash", "code": "pnpmcreatebuilderbot@latest"}, {"language": "", "code": "info, hello, hi"}, {"language": "overflow-x-auto", "code": "import{ createBot,createProvider,create<PERSON>low,addKeyword,MemoryDB }from'@builderbot/bot'import{ BaileysProvider }from'@builderbot/provider-baileys'constwelcomeFlow=addKeyword<BaileysProvider,MemoryDB>(['hello','hi']).addAnswer('Ey! welcome').addAnswer(`Send image from URL`,{ media:'https://i.imgur.com/0HpzsEm.png'})constmain=async()=>{constadapterDB=newMemoryDB()constadapterFlow=createFlow([welcomeFlow])constadapterProvider=createProvider(BaileysProvider)const{handleCtx,httpServer}=awaitcreateBot({flow:adapterFlow,provider:adapterProvider,database:adapterDB,})httpServer(3000)adapterProvider.server.post('/v1/messages',handleCtx(async(bot,req,res)=>{const{number,message}=req.bodyawaitbot.sendMessage(number,message,{})returnres.end('send')}))}main()"}, {"language": "language-ts", "code": "import{ createBot,createProvider,create<PERSON>low,addKeyword,MemoryDB }from'@builderbot/bot'import{ BaileysProvider }from'@builderbot/provider-baileys'constwelcomeFlow=addKeyword<BaileysProvider,MemoryDB>(['hello','hi']).addAnswer('Ey! welcome').addAnswer(`Send image from URL`,{ media:'https://i.imgur.com/0HpzsEm.png'})constmain=async()=>{constadapterDB=newMemoryDB()constadapterFlow=createFlow([welcomeFlow])constadapterProvider=createProvider(BaileysProvider)const{handleCtx,httpServer}=awaitcreateBot({flow:adapterFlow,provider:adapterProvider,database:adapterDB,})httpServer(3000)adapterProvider.server.post('/v1/messages',handleCtx(async(bot,req,res)=>{const{number,message}=req.bodyawaitbot.sendMessage(number,message,{})returnres.end('send')}))}main()"}], "success": true, "error_message": ""}, {"url": "https://www.builderbot.app/en/contribute", "title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "Contribute Welcome to the BuilderBot Contribution Guide We're glad to have you here. This page provides instructions on how to edit BuilderBot documentation. Our goal is to ensure that everyone in the community feels empowered to contribute and improve our documentation. Quick View Make a fork of the project Clone the project git clone https://github.com/USERNAME/documentation Install dependencies npm install Make your changes Send your contributions (PullRequest) Why Contribute? Open source work never ends, and neither does documentation. Contributing to the documentation is a great way for beginners to get involved in open source and for experienced developers to clarify more complex issues while sharing their knowledge with the community. By contributing to BuilderBot documentation, you help us create a more robust learning resource for all developers. If you've found a typo, a confusing section, or noticed that a particular topic is missing, your contributions are welcome and appreciated. How to Contribute The content of the documentation is located in the BuilderBot repository . To contribute, you can edit the files directly on GitHub or clone the repository and edit the files locally. GitHub Workflow If you're new to GitHub, we recommend you read the GitHub Open Source Guide to learn how to fork a repository, create a branch, and send a pull request. The code in the underlying documents lives in a private codebase that syncs with the public BuilderBot repository. This means that you cannot preview the docs locally. However, you will see your changes in builderbot.app after merging a pull request. Writing MDX The docs are written in MDX , a markdown format that supports JSX syntax. This allows us to embed React components in the docs. See the GitHub Markdown Guide for a quick overview of markdown syntax. VSCode Previewing Changes Locally VSCode has a built-in markdown previewer that you can use to see your edits locally. To enable the previewer for MDX files, you'll need to add a configuration option to your user settings. Open the command palette ( ⌘ + ⇧ + P on Mac or Ctrl + Shift + P on Windows) and search from Preferences: Open User Settings (JSON) . Then, add the following line to your settings.json file: { \"files.associations\" : { \"*.mdx\" : \"markdown\" } } Copy Copied! Next, open the command palette again, and search for Markdown: Preview File or Markdown: Open Preview to the Side . This will open a preview window where you can see your formatted changes. Extensions We also recommend the following extensions for VSCode users: MDX : Intellisense and syntax highlighting for MDX. Grammarly : Grammar and spell checker. Prettier : Format MDX files on save. Review Process Once you have submitted your contribution, a Core Team member will review your changes, provide feedback and merge the pull request when ready. Please let us know if you have any questions or need further assistance in the comments of your PR. Thank you for contributing to the BuilderBot docs and for being part of our community. File Structure Documents use file system routing. Each folder and file within /pages represents a path segment. These segments are used to generate URL paths, navigation and breadcrumbs. en ├── showcases │   └── api-use.mdx └── ... Copy Copied! Each folder prefix en , es , pt represents the language in which the content is represented. en ├── showcases │   └── api-use.mdx └── ... es ├── showcases │   └── api-use.mdx └── ... pt ├── showcases │   └── api-use.mdx └── ... Copy Copied! Required Fields The following fields are required : Field Description description The page's description, used in the <meta name=\"description\"> tag for SEO. title The page's <h1> title, used for SEO and OG Images. export const description = 'In this guide, we will talk ...' # Community Copy Copied! Code Blocks The code blocks must contain a minimal working example that can be copied and pasted. This means that the code must be able to run without any additional configuration. For example if we want to print TS or JS code example.ts const flow = addKeyword ( 'hello' ) .addAnswer ( `What is your name?` , { capture : true } , async (ctx , { state }) => { await state .update ({ name : ctx .body }) }) .addAction ( async (ctx , { state , flowDynamic }) => { const name = state .get ( 'name' ) await flowDynamic ( `Your name is: ${ name } ` ) }) } Copy Copied! Always run examples locally before committing them. This will ensure that the code is up-to-date and working. Language and Filename Code blocks should have a header that includes the language and the filename . Add a filename prop to render a special Terminal icon that helps orientate users where to input the command. For example: ``` ts {{ title: 'example.ts' }} const flow = addKeyword ( 'hello' ) .addAnswer ( `What is your name?` , { capture : true } , async (ctx , { state }) => { await state .update ({ name : ctx .body }) }) .addAction ( async (ctx , { state , flowDynamic }) => { const name = state .get ( 'name' ) await flowDynamic ( `Your name is: ${ name } ` ) }) } ``` Copy Copied! Most examples in the docs are written in tsx and jsx , and a few in bash . However, you can use any supported language, here's the full list . When writing JavaScript code blocks, we use the following language and extension combinations. Language Extension JavaScript files ```js .js TypeScript files ```ts .ts Grouped code blocks Sometimes we will need to represent a group of blocks of code grouped together even with different file names and in multiple languages we can do it in the following way app.ts provider/index.ts database/index.ts flow/index.ts flow/welcome.flow.ts services/ai.ts import { createBot } from '@builderbot/bot' ; import { flow } from \"./flow\" ; import { database } from \"./database\" ; import { provider } from \"./provider\" ; import { ai } from \"./services/ai\" ; const main = async () => { await createBot ({ flow , provider , database , } , extensions: { ai // Dependency AI }) provider .initHttpServer ( 3000 ) } main () Copy Copied! The template already provides internally a <CodeGroup> component that has the ability to interpret code blocks. < CodeGroup > ``` ts {{ title: 'app.ts' }} import { createBot } from '@builderbot/bot' ; import { flow } from \"./flow\" ; import { database } from \"./database\" ; import { provider } from \"./provider\" ; import { ai } from \"./services/ai\" ; const main = async () => { await createBot ({ flow , provider , database , } , extensions: { ai // Dependency AI }) provider .initHttpServer ( 3000 ) } main () ``` ``` ts {{ title: 'provider/index.ts' }} import { createProvider } from '@builderbot/bot' ; import { BaileysProvider } from '@builderbot/provider-baileys' ; export const provider = createProvider (BaileysProvider) ``` ``` ts {{ title: 'database/index.ts' }} export const database = new MemoryDB () ``` ``` ts {{ title: 'flow/index.ts' }} import { createFlow } from '@builderbot/bot' ; import { flowWelcome } from \"./welcome.flow\" ; import { byeFlow } from \"./bye.flow\" ; import { mediaFlow } from \"./media.flow\" ; // other flows.... export const flow = createFlow ([flowWelcome , byeFlow , mediaFlow]) ``` ``` ts {{ title: 'flow/welcome.flow.ts' }} import { addKeyword , EVENTS } from '@builderbot/bot' ; export const flowWelcome = addKeyword ( EVENTS . WELCOME ) .addAction ( async (ctx , {flowDynamic , extensions}) => { const { ai } = extensions const talkWithGPT = ai .chat ( ctx .body) // Dependency AI from app.ts await flowDynamic (talkWithGPT) }) ``` ``` ts {{ title: 'services/ai.ts' }} // .... export const ai = new AiService ( process . env . OPEN_AI_KEY ); ``` </ CodeGroup > Copy Copied! Guides My first chatbot Learn how build your first chatbot in few minutes Read more Concepts Understand the essential concepts for building bots Read more Add Functions The key to learning how to write flows is add-functions. Read more Plugins Unlimitate and start implementing the community plugins. Read more Resources Modularize Learn how to modularise flows so that you can have a more maintainable bot. Send Message How to send a message via HTTP to start conversations, you can send multimedia as well. Dockerizer A good practice is to dockerise your bots to make them more maintainable and effective. Events Learning about events will make us more fluent when creating chatbots.", "chunks": [{"content": "Contribute Welcome to the BuilderBot Contribution Guide We're glad to have you here. This page provides instructions on how to edit BuilderBot documentation. Our goal is to ensure that everyone in the community feels empowered to contribute and improve our documentation. Quick View Make a fork of the project Clone the project git clone https://github.com/USERNAME/documentation Install dependencies npm install Make your changes Send your contributions (PullRequest) Why Contribute? Open source work never ends, and neither does documentation. Contributing to the documentation is a great way for beginners to get involved in open source and for experienced developers to clarify more complex issues while sharing their knowledge with the community. By contributing to BuilderBot documentation, you help us create a more robust learning resource for all developers. If you've found a typo, a confusing section, or noticed that a particular topic is missing, your contributions are welcome and appreciated. How to Contribute The content of the documentation is located in the BuilderBot repository . To contribute, you can edit the files directly on GitHub or clone the repository and edit the files locally. GitHub Workflow If you're new to GitHub, we recommend you read the GitHub Open Source Guide to learn how to fork a repository, create a branch, and send a pull request. The code in the underlying documents lives in a private codebase that syncs with the public BuilderBot repository. This means that you cannot preview the docs locally. However, you will see your changes in builderbot.app after merging a pull request. Writing MDX The docs are written in MDX , a markdown format that supports JSX syntax. This allows us to embed React components in the docs. See the GitHub Markdown Guide for a quick overview of markdown syntax. VSCode Previewing Changes Locally VSCode has a built-in markdown previewer that you can use to see your edits locally. To enable the previewer for MDX files, you'll need to add a configuration option to your user settings. Open the command palette ( ⌘ + ⇧ + P on Mac or Ctrl + Shift + P on Windows) and search from Preferences: Open User Settings (JSON) . Then, add the following line to your settings.json file: { \"files.associations\" : { \"*.mdx\" : \"markdown\" } } Copy Copied! Next, open the command palette again, and search for Markdown: Preview File or Markdown: Open Preview to the Side . This will open a preview window where you can see your formatted changes. Extensions We also recommend the following extensions for VSCode users: MDX : Intellisense and syntax highlighting for MDX. Grammarly : Grammar and spell checker. Prettier : Format MDX files on save. Review Process Once you have submitted your contribution, a Core Team member will review your changes, provide feedback and merge the pull request when ready. Please let us know if you have any questions or need further assistance in the comments of your PR. Thank you for contributing to the BuilderBot docs and for being part of our community. File Structure Documents use file system routing. Each folder and file within /pages represents a path segment. These segments are used to generate URL paths, navigation and breadcrumbs. en ├── showcases │   └── api-use.mdx └── ... Copy Copied! Each folder prefix en , es , pt represents the language in which the content is represented. en ├── showcases │   └── api-use.mdx └── ... es ├── showcases │   └── api-use.mdx └── ... pt ├── showcases │   └── api-use.mdx └── ... Copy Copied! Required Fields The following fields are required : Field Description description The page's description, used in the <meta name=\"description\"> tag for SEO. title The page's <h1> title, used for SEO and OG Images. export const description = 'In this guide, we will talk ...' # Community Copy Copied! Code Blocks The code blocks must contain a minimal working example that can be copied and pasted. This means that the code must be able to run without any additional configuration. For example if we want to print TS or JS code example.ts const flow = addKeyword ( 'hello' ) .addAnswer ( `What is your name?` , { capture : true } , async (ctx , { state }) => { await state .update ({ name : ctx .body }) }) .addAction ( async (ctx , { state , flowDynamic }) => { const name = state .get ( 'name' ) await flowDynamic ( `Your name is: ${ name } ` ) }) } Copy Copied! Always run examples locally before committing them. This will ensure that the code is up-to-date and working. Language and Filename Code blocks should have a header that includes the language and the filename . Add a filename prop to render a special Terminal icon that helps orientate users where to input the command. For example: ``` ts {{ title: 'example.ts' }} const flow = addKeyword ( 'hello' ) .addAnswer ( `What is your name?` , { capture : true } , async (ctx , { state }) => { await state .update ({ name : ctx .body }) }) .addAction ( async (ctx , { state , flowDynamic }) => { const name = state .get ( 'name' ) await flowDynamic ( `Your name is: ${ name } ` ) }) } ``` Copy Copied! Most examples in the docs are written in tsx and jsx , and a few in bash . However, you can use any supported language, here's the full list . When writing JavaScript code blocks, we use the following language and extension combinations. Language Extension JavaScript files ```js .js TypeScript files ```ts .ts Grouped code blocks Sometimes we will need to represent a group of blocks of code grouped together even with different file names and in multiple languages we can do it in the following way app.ts provider/index.ts database/index.ts flow/index.ts flow/welcome.flow.ts services/ai.ts import { createBot } from '@builderbot/bot' ; import { flow } from \"./flow\" ; import { database } from \"./database\" ; import { provider } from \"./provider\" ; import { ai } from \"./services/ai\" ; const main = async () => { await createBot ({ flow , provider , database , } , extensions: { ai // Dependency AI }) provider .initHttpServer ( 3000 ) } main () Copy Copied! The template already provides internally a <CodeGroup> component that has the ability to interpret code blocks. < CodeGroup > ``` ts {{ title: 'app.ts' }} import { createBot } from '@builderbot/bot' ; import { flow } from \"./flow\" ; import { database } from \"./database\" ; import { provider } from \"./provider\" ; import { ai } from \"./services/ai\" ; const main = async () => { await createBot ({ flow , provider , database , } , extensions: { ai // Dependency AI }) provider .initHttpServer ( 3000 ) } main () ``` ``` ts {{ title: 'provider/index.ts' }} import { createProvider } from '@builderbot/bot' ; import { BaileysProvider } from '@builderbot/provider-baileys' ; export const provider = createProvider (BaileysProvider) ``` ``` ts {{ title: 'database/index.ts' }} export const database = new MemoryDB () ``` ``` ts {{ title: 'flow/index.ts' }} import { createFlow } from '@builderbot/bot' ; import { flowWelcome } from \"./welcome.flow\" ; import { byeFlow } from \"./bye.flow\" ; import { mediaFlow } from \"./media.flow\" ; // other flows.... export const flow = createFlow ([flowWelcome , byeFlow , mediaFlow]) ``` ``` ts {{ title: 'flow/welcome.flow.ts' }} import { addKeyword , EVENTS } from '@builderbot/bot' ; export const flowWelcome = addKeyword ( EVENTS . WELCOME ) .addAction ( async (ctx , {flowDynamic , extensions}) => { const { ai } = extensions const talkWithGPT = ai .chat ( ctx .body) // Dependency AI from app.ts await flowDynamic (talkWithGPT) }) ``` ``` ts {{ title: 'services/ai.ts' }} // .... export const ai = new AiService ( process . env . OPEN_AI_KEY ); ``` </ CodeGroup > Copy Copied! Guides My first chatbot Learn how build your first chatbot in few minutes Read more Concepts Understand the essential concepts for building bots Read more Add Functions The key to learning how to write flows is add-functions. Read more Plugins Unlimitate and start implementing the community plugins. Read more Resources Modularize Learn how to modularise flows so that you can have a more maintainable bot. Send Message How to send a message via HTTP to start conversations, you can send multimedia as well. Dockerizer A good practice is to dockerise your bots to make them more maintainable and effective. Events Learning about events will make us more fluent when creating chatbots.", "metadata": {"title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/contribute", "section": "Sin título"}, "embedding": null}], "headers": [{"level": 4, "text": "BuilderBot", "id": ""}, {"level": 4, "text": "BuilderBot", "id": ""}, {"level": 2, "text": "Start here", "id": ""}, {"level": 2, "text": "Basics", "id": ""}, {"level": 2, "text": "Built-in", "id": ""}, {"level": 2, "text": "Providers", "id": ""}, {"level": 2, "text": "Deploy", "id": ""}, {"level": 2, "text": "Recipes", "id": ""}, {"level": 2, "text": "Tutorials", "id": ""}, {"level": 2, "text": "Community Contribute", "id": ""}, {"level": 2, "text": "Plugins", "id": ""}, {"level": 1, "text": "Contribute", "id": ""}, {"level": 2, "text": "Quick View", "id": "quick-view"}, {"level": 2, "text": "Why Contribute?", "id": "why-contribute"}, {"level": 2, "text": "How to Contribute", "id": "how-to-contribute"}, {"level": 2, "text": "GitHub Workflow", "id": "git-hub-workflow"}, {"level": 2, "text": "Writing MDX", "id": "writing-mdx"}, {"level": 2, "text": "VSCode", "id": "vs-code"}, {"level": 3, "text": "Previewing Changes Locally", "id": ""}, {"level": 2, "text": "Extensions", "id": "extensions"}, {"level": 2, "text": "Review Process", "id": "review-process"}, {"level": 2, "text": "File Structure", "id": "file-structure"}, {"level": 2, "text": "Required <PERSON>", "id": "required-fields"}, {"level": 2, "text": "Code Blocks", "id": "code-blocks"}, {"level": 3, "text": "example.ts", "id": ""}, {"level": 2, "text": "Language and Filename", "id": "language-and-filename"}, {"level": 2, "text": "Grouped code blocks", "id": "grouped-code-blocks"}, {"level": 2, "text": "Guides", "id": "guides"}, {"level": 3, "text": "My first chatbot", "id": ""}, {"level": 3, "text": "Concepts", "id": ""}, {"level": 3, "text": "Add Functions", "id": ""}, {"level": 3, "text": "Plugins", "id": ""}, {"level": 2, "text": "Resources", "id": "resources"}, {"level": 3, "text": "Modularize", "id": ""}, {"level": 3, "text": "Send Message", "id": ""}, {"level": 3, "text": "Dockerizer", "id": ""}, {"level": 3, "text": "Events", "id": ""}], "code_blocks": [{"language": "", "code": "git clone https://github.com/USERNAME/documentation"}, {"language": "", "code": "npm install"}, {"language": "", "code": "⌘ + ⇧ + P"}, {"language": "", "code": "Ctrl + Shift + P"}, {"language": "", "code": "Preferences: Open User Settings (JSON)"}, {"language": "", "code": "settings.json"}, {"language": "overflow-x-auto", "code": "{\"files.associations\":{\"*.mdx\":\"markdown\"}}"}, {"language": "language-json", "code": "{\"files.associations\":{\"*.mdx\":\"markdown\"}}"}, {"language": "", "code": "Markdown: Preview File"}, {"language": "", "code": "Markdown: Open Preview to the Side"}, {"language": "", "code": "/pages"}, {"language": "overflow-x-auto", "code": "en├── showcases│   └── api-use.mdx└── ..."}, {"language": "language-txt", "code": "en├── showcases│   └── api-use.mdx└── ..."}, {"language": "", "code": "en"}, {"language": "", "code": "es"}, {"language": "", "code": "pt"}, {"language": "overflow-x-auto", "code": "en├── showcases│   └── api-use.mdx└── ...es├── showcases│   └── api-use.mdx└── ...pt├── showcases│   └── api-use.mdx└── ..."}, {"language": "language-txt", "code": "en├── showcases│   └── api-use.mdx└── ...es├── showcases│   └── api-use.mdx└── ...pt├── showcases│   └── api-use.mdx└── ..."}, {"language": "", "code": "description"}, {"language": "", "code": "<meta name=\"description\">"}, {"language": "", "code": "title"}, {"language": "", "code": "<h1>"}, {"language": "overflow-x-auto", "code": "exportconstdescription='In this guide, we will talk ...'# Community"}, {"language": "language-mdx", "code": "exportconstdescription='In this guide, we will talk ...'# Community"}, {"language": "overflow-x-auto", "code": "constflow=addKeyword('hello').addAnswer(`What is your name?`,{ capture:true},async(ctx,{ state })=>{awaitstate.update({ name:ctx.body })}).addAction(async(ctx,{ state,flowDynamic })=>{constname=state.get('name')awaitflowDynamic(`Your name is:${name}`)})}"}, {"language": "language-ts", "code": "constflow=addKeyword('hello').addAnswer(`What is your name?`,{ capture:true},async(ctx,{ state })=>{awaitstate.update({ name:ctx.body })}).addAction(async(ctx,{ state,flowDynamic })=>{constname=state.get('name')awaitflowDynamic(`Your name is:${name}`)})}"}, {"language": "", "code": "filename"}, {"language": "", "code": "filename"}, {"language": "overflow-x-auto", "code": "```ts{{ title: 'example.ts' }}constflow=addKeyword('hello').addAnswer(`What is your name?`,{ capture:true},async(ctx,{ state })=>{awaitstate.update({ name:ctx.body })}).addAction(async(ctx,{ state,flowDynamic })=>{constname=state.get('name')awaitflowDynamic(`Your name is:${name}`)})}```"}, {"language": "language-mdx", "code": "```ts{{ title: 'example.ts' }}constflow=addKeyword('hello').addAnswer(`What is your name?`,{ capture:true},async(ctx,{ state })=>{awaitstate.update({ name:ctx.body })}).addAction(async(ctx,{ state,flowDynamic })=>{constname=state.get('name')awaitflowDynamic(`Your name is:${name}`)})}```"}, {"language": "", "code": "tsx"}, {"language": "", "code": "jsx"}, {"language": "", "code": "bash"}, {"language": "overflow-x-auto", "code": "import{ createBot }from'@builderbot/bot';import{ flow }from\"./flow\";import{ database }from\"./database\";import{ provider }from\"./provider\";import{ ai }from\"./services/ai\";constmain=async()=>{awaitcreateBot({flow,provider,database,},extensions: {ai// Dependency AI})provider.initHttpServer(3000)}main()"}, {"language": "language-ts", "code": "import{ createBot }from'@builderbot/bot';import{ flow }from\"./flow\";import{ database }from\"./database\";import{ provider }from\"./provider\";import{ ai }from\"./services/ai\";constmain=async()=>{awaitcreateBot({flow,provider,database,},extensions: {ai// Dependency AI})provider.initHttpServer(3000)}main()"}, {"language": "", "code": "<CodeGroup>"}, {"language": "overflow-x-auto", "code": "<CodeGroup>```ts{{ title: 'app.ts' }}import{ createBot }from'@builderbot/bot';import{ flow }from\"./flow\";import{ database }from\"./database\";import{ provider }from\"./provider\";import{ ai }from\"./services/ai\";constmain=async()=>{awaitcreateBot({flow,provider,database,},extensions: {ai// Dependency AI})provider.initHttpServer(3000)}main()``````ts{{ title: 'provider/index.ts' }}import{ createProvider }from'@builderbot/bot';import{ BaileysProvider }from'@builderbot/provider-baileys';exportconstprovider=createProvider(BaileysProvider)``````ts{{ title: 'database/index.ts' }}exportconstdatabase=newMemoryDB()``````ts{{ title: 'flow/index.ts' }}import{ createFlow }from'@builderbot/bot';import{ flowWelcome }from\"./welcome.flow\";import{ byeFlow }from\"./bye.flow\";import{ mediaFlow }from\"./media.flow\";// other flows....exportconstflow=createFlow([flowWelcome,byeFlow,mediaFlow])``````ts{{ title: 'flow/welcome.flow.ts' }}import{ addKeyword,EVENTS }from'@builderbot/bot';exportconstflowWelcome=addKeyword(EVENTS.WELCOME).addAction(async(ctx,{flowDynamic,extensions})=>{const{ai}=extensionsconsttalkWithGPT=ai.chat(ctx.body)// Dependency AI from app.tsawaitflowDynamic(talkWithGPT)})``````ts{{ title: 'services/ai.ts' }}// ....exportconstai=newAiService(process.env.OPEN_AI_KEY);```</CodeGroup>"}, {"language": "language-mdx", "code": "<CodeGroup>```ts{{ title: 'app.ts' }}import{ createBot }from'@builderbot/bot';import{ flow }from\"./flow\";import{ database }from\"./database\";import{ provider }from\"./provider\";import{ ai }from\"./services/ai\";constmain=async()=>{awaitcreateBot({flow,provider,database,},extensions: {ai// Dependency AI})provider.initHttpServer(3000)}main()``````ts{{ title: 'provider/index.ts' }}import{ createProvider }from'@builderbot/bot';import{ BaileysProvider }from'@builderbot/provider-baileys';exportconstprovider=createProvider(BaileysProvider)``````ts{{ title: 'database/index.ts' }}exportconstdatabase=newMemoryDB()``````ts{{ title: 'flow/index.ts' }}import{ createFlow }from'@builderbot/bot';import{ flowWelcome }from\"./welcome.flow\";import{ byeFlow }from\"./bye.flow\";import{ mediaFlow }from\"./media.flow\";// other flows....exportconstflow=createFlow([flowWelcome,byeFlow,mediaFlow])``````ts{{ title: 'flow/welcome.flow.ts' }}import{ addKeyword,EVENTS }from'@builderbot/bot';exportconstflowWelcome=addKeyword(EVENTS.WELCOME).addAction(async(ctx,{flowDynamic,extensions})=>{const{ai}=extensionsconsttalkWithGPT=ai.chat(ctx.body)// Dependency AI from app.tsawaitflowDynamic(talkWithGPT)})``````ts{{ title: 'services/ai.ts' }}// ....exportconstai=newAiService(process.env.OPEN_AI_KEY);```</CodeGroup>"}], "success": true, "error_message": ""}, {"url": "https://www.builderbot.app/en/quickstart", "title": "Documentation - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "Create Creating a bot is as simple as running the following command and following the instructions Prerequisites to consider before using this tool, Node v20 or higher and Git pnpm npm pnpm create builderbot@latest Copy Copied! or you can use the following command to create a bot with the default configuration pnpm npm pnpm create builderbot@latest --provider=baileys --database=memory --language=ts Copy Copied! Use the space key to select and the enter key to confirm. The CLI performs a preliminary check of the Node and operating system version, informing you if it meets the requirements or providing you with relevant information. In addition to generating a base project for you to simply start up If you have problems with your terminal try running the command with CMD, PowerShell, GitBash or another console you have installed. Requirements Make sure you have installed Node version 20 or higher , below you can see an example to check the version of node you are using. Node Version node -v v20.10.0 Copy Copied! Download node from its official website It is recommended to have GIT installed for proper operation. If you are using Linux or MacOc you probably already have GIT installed by default. Git Version git -v git version XXXX Copy Copied! Download GIT from its official website Base Example In this example we can see the basis of a simple bot which responds to the keywords sent by a user, the words are: info, hello, hi . You can see how to create the bot and implement the flows . main.ts main.js import { createBot , createProvider , createFlow , addKeyword , MemoryDB } from '@builderbot/bot' import { BaileysProvider } from '@builderbot/provider-baileys' /** send static messages */ const welcomeFlow = addKeyword < BaileysProvider , MemoryDB >([ 'hello' , 'hi' ]) .addAnswer ( 'Ey! welcome' ) /** send dynamic message from db or other sources */ const infoFlow = addKeyword < BaileysProvider , MemoryDB >( 'info' ) .addAction ( async (ctx , { flowDynamic }) => { await flowDynamic ( `Welcome ${ ctx .name } ` ) }) /** send media files */ const mediaFlow = addKeyword < BaileysProvider , MemoryDB >( 'image' ) .addAnswer ( `Send Image A` , { media : 'https://i.imgur.com/AsvWfUX.png' }) .addAction ( async (ctx , { flowDynamic }) => { await flowDynamic ( `Welcome ${ ctx .name } ` ) await flowDynamic ( [ { body : 'Send Image B' , media : 'https://i.imgur.com/w0RtKnN.png' } ] ) }) /** initialization bot */ const main = async () => { const adapterDB = new MemoryDB () const adapterFlow = createFlow ([welcomeFlow , infoFlow , mediaFlow]) const adapterProvider = createProvider (BaileysProvider) adapterProvider .initHttpServer ( 3000 ) await createBot ({ flow : adapterFlow , provider : adapterProvider , database : adapterDB , }) } main () Copy Copied! Resources Modularize Learn how to modularise flows so that you can have a more maintainable bot. Send Message How to send a message via HTTP to start conversations, you can send multimedia as well. Dockerizer A good practice is to dockerise your bots to make them more maintainable and effective. Events Learning about events will make us more fluent when creating chatbots.", "chunks": [{"content": "Create Creating a bot is as simple as running the following command and following the instructions Prerequisites to consider before using this tool, Node v20 or higher and Git pnpm npm pnpm create builderbot@latest Copy Copied! or you can use the following command to create a bot with the default configuration pnpm npm pnpm create builderbot@latest --provider=baileys --database=memory --language=ts Copy Copied! Use the space key to select and the enter key to confirm. The CLI performs a preliminary check of the Node and operating system version, informing you if it meets the requirements or providing you with relevant information. In addition to generating a base project for you to simply start up If you have problems with your terminal try running the command with CMD, PowerShell, GitBash or another console you have installed. Requirements Make sure you have installed Node version 20 or higher , below you can see an example to check the version of node you are using. Node Version node -v v20.10.0 Copy Copied! Download node from its official website It is recommended to have GIT installed for proper operation. If you are using Linux or MacOc you probably already have GIT installed by default. Git Version git -v git version XXXX Copy Copied! Download GIT from its official website Base Example In this example we can see the basis of a simple bot which responds to the keywords sent by a user, the words are: info, hello, hi . You can see how to create the bot and implement the flows . main.ts main.js import { createBot , createProvider , createFlow , addKeyword , MemoryDB } from '@builderbot/bot' import { BaileysProvider } from '@builderbot/provider-baileys' /** send static messages */ const welcomeFlow = addKeyword < BaileysProvider , MemoryDB >([ 'hello' , 'hi' ]) .addAnswer ( 'Ey! welcome' ) /** send dynamic message from db or other sources */ const infoFlow = addKeyword < BaileysProvider , MemoryDB >( 'info' ) .addAction ( async (ctx , { flowDynamic }) => { await flowDynamic ( `Welcome ${ ctx .name } ` ) }) /** send media files */ const mediaFlow = addKeyword < BaileysProvider , MemoryDB >( 'image' ) .addAnswer ( `Send Image A` , { media : 'https://i.imgur.com/AsvWfUX.png' }) .addAction ( async (ctx , { flowDynamic }) => { await flowDynamic ( `Welcome ${ ctx .name } ` ) await flowDynamic ( [ { body : 'Send Image B' , media : 'https://i.imgur.com/w0RtKnN.png' } ] ) }) /** initialization bot */ const main = async () => { const adapterDB = new MemoryDB () const adapterFlow = createFlow ([welcomeFlow , infoFlow , mediaFlow]) const adapterProvider = createProvider (BaileysProvider) adapterProvider .initHttpServer ( 3000 ) await createBot ({ flow : adapterFlow , provider : adapterProvider , database : adapterDB , }) } main () Copy Copied! Resources Modularize Learn how to modularise flows so that you can have a more maintainable bot. Send Message How to send a message via HTTP to start conversations, you can send multimedia as well. Dockerizer A good practice is to dockerise your bots to make them more maintainable and effective. Events Learning about events will make us more fluent when creating chatbots.", "metadata": {"title": "Documentation - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/quickstart", "section": "Sin título"}, "embedding": null}], "headers": [{"level": 4, "text": "BuilderBot", "id": ""}, {"level": 4, "text": "BuilderBot", "id": ""}, {"level": 2, "text": "Start here", "id": ""}, {"level": 2, "text": "Basics", "id": ""}, {"level": 2, "text": "Built-in", "id": ""}, {"level": 2, "text": "Providers", "id": ""}, {"level": 2, "text": "Deploy", "id": ""}, {"level": 2, "text": "Recipes", "id": ""}, {"level": 2, "text": "Tutorials", "id": ""}, {"level": 2, "text": "Community Contribute", "id": ""}, {"level": 2, "text": "Plugins", "id": ""}, {"level": 2, "text": "Create", "id": "create"}, {"level": 2, "text": "Requirements", "id": "requirements"}, {"level": 3, "text": "Node Version", "id": ""}, {"level": 3, "text": "Git Version", "id": ""}, {"level": 2, "text": "Base Example", "id": "base-example"}, {"level": 2, "text": "Resources", "id": "resources"}, {"level": 3, "text": "Modularize", "id": ""}, {"level": 3, "text": "Send Message", "id": ""}, {"level": 3, "text": "Dockerizer", "id": ""}, {"level": 3, "text": "Events", "id": ""}], "code_blocks": [{"language": "overflow-x-auto", "code": "pnpmcreatebuilderbot@latest"}, {"language": "language-bash", "code": "pnpmcreatebuilderbot@latest"}, {"language": "overflow-x-auto", "code": "pnpmcreatebuilderbot@latest--provider=baileys--database=memory--language=ts"}, {"language": "language-bash", "code": "pnpmcreatebuilderbot@latest--provider=baileys--database=memory--language=ts"}, {"language": "overflow-x-auto", "code": "node-vv20.10.0"}, {"language": "language-bash", "code": "node-vv20.10.0"}, {"language": "overflow-x-auto", "code": "git-vgitversionXXXX"}, {"language": "language-bash", "code": "git-vgitversionXXXX"}, {"language": "", "code": "info, hello, hi"}, {"language": "overflow-x-auto", "code": "import{ createBot,createProvider,create<PERSON>low,addKeyword,MemoryDB }from'@builderbot/bot'import{ BaileysProvider }from'@builderbot/provider-baileys'/** send static messages */constwelcomeFlow=addKeyword<BaileysProvider,MemoryDB>(['hello','hi']).addAnswer('Ey! welcome')/** send dynamic message from db or other sources */constinfoFlow=addKeyword<BaileysProvider,MemoryDB>('info').addAction(async(ctx,{ flowDynamic })=>{awaitflowDynamic(`Welcome${ctx.name}`)})/** send media files */constmediaFlow=addKeyword<BaileysProvider,MemoryDB>('image').addAnswer(`Send Image A`,{ media:'https://i.imgur.com/AsvWfUX.png'}).addAction(async(ctx,{ flowDynamic })=>{awaitflowDynamic(`Welcome${ctx.name}`)awaitflowDynamic([{body:'Send Image B',media:'https://i.imgur.com/w0RtKnN.png'}])})/** initialization bot */constmain=async()=>{constadapterDB=newMemoryDB()constadapterFlow=createFlow([welcomeFlow,infoFlow,mediaFlow])constadapterProvider=createProvider(BaileysProvider)adapterProvider.initHttpServer(3000)awaitcreateBot({flow:adapterFlow,provider:adapterProvider,database:adapterDB,})}main()"}, {"language": "language-ts", "code": "import{ createBot,createProvider,create<PERSON>low,addKeyword,MemoryDB }from'@builderbot/bot'import{ BaileysProvider }from'@builderbot/provider-baileys'/** send static messages */constwelcomeFlow=addKeyword<BaileysProvider,MemoryDB>(['hello','hi']).addAnswer('Ey! welcome')/** send dynamic message from db or other sources */constinfoFlow=addKeyword<BaileysProvider,MemoryDB>('info').addAction(async(ctx,{ flowDynamic })=>{awaitflowDynamic(`Welcome${ctx.name}`)})/** send media files */constmediaFlow=addKeyword<BaileysProvider,MemoryDB>('image').addAnswer(`Send Image A`,{ media:'https://i.imgur.com/AsvWfUX.png'}).addAction(async(ctx,{ flowDynamic })=>{awaitflowDynamic(`Welcome${ctx.name}`)awaitflowDynamic([{body:'Send Image B',media:'https://i.imgur.com/w0RtKnN.png'}])})/** initialization bot */constmain=async()=>{constadapterDB=newMemoryDB()constadapterFlow=createFlow([welcomeFlow,infoFlow,mediaFlow])constadapterProvider=createProvider(BaileysProvider)adapterProvider.initHttpServer(3000)awaitcreateBot({flow:adapterFlow,provider:adapterProvider,database:adapterDB,})}main()"}], "success": true, "error_message": ""}, {"url": "https://www.builderbot.app/en/concepts", "title": "Why BuilderBot? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "Why BuilderBot? BuilderBot is the framework for the creation of ChatBots focused on low-frequency communication channels, whatsapp, telegram, etc.\nWe implement an architecture focused on improving the developer experience and the reuse of logic at all times,\nif you need to create chatbots for whatsapp quickly, without limits and easy connection between different providers then BuilderBot is for you. The library is based on three key components for its correct functioning: the Flow, in charge of building the context of the conversation and offering a friendly interface to the developer; the Provider, which acts as a connector allowing to easily switch between WhatsApp providers without the risk of affecting other parts of the bot; and the Database, which in line with this connector philosophy, facilitates changing the data persistence layer without the need to rewrite the workflow. Flow Refers to creating structured sequences of interactions, as in building conversation flows. Two key methods are addKeyword and addAnswer, which allow keywords to be associated with specific responses, providing options for customizing the conversation flow. Keywords are the words you will use to start the flow, you can use a single word or a list of words. Example \"hello\", \"good morning\". app.ts app.js import { addKeyword } from '@builderbot/bot' addKeyword ([ 'hello' , 'hi' ]) .addAnswer ( 'Ey! welcome' ) Copy Copied! Some examples of how to use the addKeyword in which you can place the keyword or a list of keywords that will be used to start a conversational flow // Example with single keyword addKeyword ( 'hello' ) .addAnswer ( 'Ey! welcome' ) // Example with multi keywords addKeyword ([ 'hello' , 'hi' ]) .addAnswer ( 'Ey! welcome' ) Copy Copied! For a quick understanding of the operation we have prepared a basic example of how to implement View more examples Provider It is a key piece used to deliver the message to the chosen supplier. In a case you are building a bot for whatsapp you should use an adapter like Meta , Twilio , Baileys , etc or even if you want to connect to Telegram. app.ts provider.wppconnect.ts provider.meta.ts import { addKeyword , MemoryDB , createProvider , createFlow } from '@builderbot/bot' import { BaileysProvider } from '@builderbot/provider-baileys' // ...stuff code... const main = async () => { await createBot ({ database : new MemoryDB () , provider : createProvider (BaileysProvider) , flow : createFlow ([flowDemo]) }) } main () Copy Copied! More information about the providers Database Just as providers can be easily exchanged between adapters, we can do the same with the database. Now the important thing to understand is how it works. The main purpose of the database inside the bot is to provide the bot with a record of the different events that have occurred between different conversations. It is ready to implement adapters from Mongo , MySQL , Postgres , among others. app.ts provider.wppconnect.ts provider.meta.ts import { addKeyword , MemoryDB , createProvider , createFlow } from '@builderbot/bot' import { BaileysProvider } from '@builderbot/provider-baileys' // ...stuff code... const main = async () => { await createBot ({ database : new MemoryDB () , provider : createProvider (BaileysProvider) , flow : createFlow ([flowDemo]) }) } main () Copy Copied! More information about the databases Guides My first chatbot Learn how build your first chatbot in few minutes Read more Concepts Understand the essential concepts for building bots Read more Add Functions The key to learning how to write flows is add-functions. Read more Plugins Unlimitate and start implementing the community plugins. Read more Resources Modularize Learn how to modularise flows so that you can have a more maintainable bot. Send Message How to send a message via HTTP to start conversations, you can send multimedia as well. Dockerizer A good practice is to dockerise your bots to make them more maintainable and effective. Events Learning about events will make us more fluent when creating chatbots.", "chunks": [{"content": "Why BuilderBot? BuilderBot is the framework for the creation of ChatBots focused on low-frequency communication channels, whatsapp, telegram, etc.\nWe implement an architecture focused on improving the developer experience and the reuse of logic at all times,\nif you need to create chatbots for whatsapp quickly, without limits and easy connection between different providers then BuilderBot is for you. The library is based on three key components for its correct functioning: the Flow, in charge of building the context of the conversation and offering a friendly interface to the developer; the Provider, which acts as a connector allowing to easily switch between WhatsApp providers without the risk of affecting other parts of the bot; and the Database, which in line with this connector philosophy, facilitates changing the data persistence layer without the need to rewrite the workflow. Flow Refers to creating structured sequences of interactions, as in building conversation flows. Two key methods are addKeyword and addAnswer, which allow keywords to be associated with specific responses, providing options for customizing the conversation flow. Keywords are the words you will use to start the flow, you can use a single word or a list of words. Example \"hello\", \"good morning\". app.ts app.js import { addKeyword } from '@builderbot/bot' addKeyword ([ 'hello' , 'hi' ]) .addAnswer ( 'Ey! welcome' ) Copy Copied! Some examples of how to use the addKeyword in which you can place the keyword or a list of keywords that will be used to start a conversational flow // Example with single keyword addKeyword ( 'hello' ) .addAnswer ( 'Ey! welcome' ) // Example with multi keywords addKeyword ([ 'hello' , 'hi' ]) .addAnswer ( 'Ey! welcome' ) Copy Copied! For a quick understanding of the operation we have prepared a basic example of how to implement View more examples Provider It is a key piece used to deliver the message to the chosen supplier. In a case you are building a bot for whatsapp you should use an adapter like Meta , Twilio , Baileys , etc or even if you want to connect to Telegram. app.ts provider.wppconnect.ts provider.meta.ts import { addKeyword , MemoryDB , createProvider , createFlow } from '@builderbot/bot' import { BaileysProvider } from '@builderbot/provider-baileys' // ...stuff code... const main = async () => { await createBot ({ database : new MemoryDB () , provider : createProvider (BaileysProvider) , flow : createFlow ([flowDemo]) }) } main () Copy Copied! More information about the providers Database Just as providers can be easily exchanged between adapters, we can do the same with the database. Now the important thing to understand is how it works. The main purpose of the database inside the bot is to provide the bot with a record of the different events that have occurred between different conversations. It is ready to implement adapters from Mongo , MySQL , Postgres , among others. app.ts provider.wppconnect.ts provider.meta.ts import { addKeyword , MemoryDB , createProvider , createFlow } from '@builderbot/bot' import { BaileysProvider } from '@builderbot/provider-baileys' // ...stuff code... const main = async () => { await createBot ({ database : new MemoryDB () , provider : createProvider (BaileysProvider) , flow : createFlow ([flowDemo]) }) } main () Copy Copied! More information about the databases Guides My first chatbot Learn how build your first chatbot in few minutes Read more Concepts Understand the essential concepts for building bots Read more Add Functions The key to learning how to write flows is add-functions. Read more Plugins Unlimitate and start implementing the community plugins. Read more Resources Modularize Learn how to modularise flows so that you can have a more maintainable bot. Send Message How to send a message via HTTP to start conversations, you can send multimedia as well. Dockerizer A good practice is to dockerise your bots to make them more maintainable and effective. Events Learning about events will make us more fluent when creating chatbots.", "metadata": {"title": "Why BuilderBot? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/concepts", "section": "Sin título"}, "embedding": null}], "headers": [{"level": 4, "text": "BuilderBot", "id": ""}, {"level": 4, "text": "BuilderBot", "id": ""}, {"level": 2, "text": "Start here", "id": ""}, {"level": 2, "text": "Basics", "id": ""}, {"level": 2, "text": "Built-in", "id": ""}, {"level": 2, "text": "Providers", "id": ""}, {"level": 2, "text": "Deploy", "id": ""}, {"level": 2, "text": "Recipes", "id": ""}, {"level": 2, "text": "Tutorials", "id": ""}, {"level": 2, "text": "Community Contribute", "id": ""}, {"level": 2, "text": "Plugins", "id": ""}, {"level": 1, "text": "Why BuilderBot?", "id": ""}, {"level": 2, "text": "Flow", "id": "flow"}, {"level": 2, "text": "Provider", "id": "provider"}, {"level": 2, "text": "Database", "id": "database"}, {"level": 2, "text": "Guides", "id": "guides"}, {"level": 3, "text": "My first chatbot", "id": ""}, {"level": 3, "text": "Concepts", "id": ""}, {"level": 3, "text": "Add Functions", "id": ""}, {"level": 3, "text": "Plugins", "id": ""}, {"level": 2, "text": "Resources", "id": "resources"}, {"level": 3, "text": "Modularize", "id": ""}, {"level": 3, "text": "Send Message", "id": ""}, {"level": 3, "text": "Dockerizer", "id": ""}, {"level": 3, "text": "Events", "id": ""}], "code_blocks": [{"language": "overflow-x-auto", "code": "import{ addKeyword }from'@builderbot/bot'addKeyword(['hello','hi']).addAnswer('Ey! welcome')"}, {"language": "language-ts", "code": "import{ addKeyword }from'@builderbot/bot'addKeyword(['hello','hi']).addAnswer('Ey! welcome')"}, {"language": "overflow-x-auto", "code": "// Example with single keywordaddKeyword('hello').addAnswer('Ey! welcome')// Example with multi keywordsaddKeyword(['hello','hi']).addAnswer('Ey! welcome')"}, {"language": "language-ts", "code": "// Example with single keywordaddKeyword('hello').addAnswer('Ey! welcome')// Example with multi keywordsaddKeyword(['hello','hi']).addAnswer('Ey! welcome')"}, {"language": "overflow-x-auto", "code": "import{ addKeyword,MemoryDB,createProvider,createFlow }from'@builderbot/bot'import{ BaileysProvider }from'@builderbot/provider-baileys'// ...stuff code...constmain=async()=>{awaitcreateBot({database:newMemoryDB(),provider:createProvider(BaileysProvider),flow:createFlow([flowDemo])})}main()"}, {"language": "language-ts", "code": "import{ addKeyword,MemoryDB,createProvider,createFlow }from'@builderbot/bot'import{ BaileysProvider }from'@builderbot/provider-baileys'// ...stuff code...constmain=async()=>{awaitcreateBot({database:newMemoryDB(),provider:createProvider(BaileysProvider),flow:createFlow([flowDemo])})}main()"}, {"language": "overflow-x-auto", "code": "import{ addKeyword,MemoryDB,createProvider,createFlow }from'@builderbot/bot'import{ BaileysProvider }from'@builderbot/provider-baileys'// ...stuff code...constmain=async()=>{awaitcreateBot({database:newMemoryDB(),provider:createProvider(BaileysProvider),flow:createFlow([flowDemo])})}main()"}, {"language": "language-ts", "code": "import{ addKeyword,MemoryDB,createProvider,createFlow }from'@builderbot/bot'import{ BaileysProvider }from'@builderbot/provider-baileys'// ...stuff code...constmain=async()=>{awaitcreateBot({database:newMemoryDB(),provider:createProvider(BaileysProvider),flow:createFlow([flowDemo])})}main()"}], "success": true, "error_message": ""}, {"url": "https://www.builderbot.app/en/uses-cases", "title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "Examples Below you will find different examples showing the implementation in different use cases. These examples have been compiled based on the community, feel free to post an example that you like or that you think would be useful for new people. How to Update to the Latest Version To ensure you're using the most up-to-date features and bug fixes, it's important to keep your BuilderBot installation current. Follow the steps below to update to the latest version.\nTo keep your project up to date, make sure to run the command to update the core and the corresponding provider pnpm install @builderbot/bot@latest pnpm install @builderbot/provider-baileys@latest pnpm install @builderbot/provider-wppconnect@latest pnpm install @builderbot/provider-venom@latest pnpm install @builderbot/provider-meta@latest pnpm install @builderbot/provider-twilio@latest Copy Copied! My first bot The following code represents the quick use of a bot that when you type the word hi , greets you with a welcome message and asks you for your name and then returns a funny image app.ts import { createBot , createProvider , createFlow , addKeyword , MemoryDB } from '@builderbot/bot' import { BaileysProvider } from '@builderbot/provider-baileys' const welcomeFlow = addKeyword < BaileysProvider , MemoryDB >([ 'hi' ]) .addAnswer ( 'Ey! welcome' ) .addAnswer ( 'Your name is?' , { capture : true } , async (ctx , { flowDynamic }) => { await flowDynamic ([ `nice! ${ ctx .body } ` , 'I will send you a funny image' ]) }) .addAction ( async (_ , {flowDynamic}) => { const dataApi = await fetch ( `https://shibe.online/api/shibes?count=1&urls=true&httpsUrls=true` ) const [ imageUrl ] = await dataApi .json () await flowDynamic ([{body : '😜' , media : imageUrl}]) }) const main = async () => { const adapterDB = new MemoryDB () const adapterFlow = createFlow ([welcomeFlow]) const adapterProvider = createProvider (BaileysProvider) adapterProvider .initHttpServer ( 3000 ) await createBot ({ flow : adapterFlow , provider : adapterProvider , database : adapterDB , }) } main () Copy Copied! state Conversational history Often, we will need to manage conversations and keep the context in a memory called state which is volatile and accessible from any function executed in a stream. const welcomeFlow = addKeyword ([ 'hello' ]) .addAnswer ( \"¿What's your name?\" , {capture : true } , async (ctx , { flowDynamic , state }) => { await state .update ({ name : ctx .body }) await flowDynamic ( 'Thanks for giving me your name!' ) } ) .addAnswer ( '¿How old are you?' , {capture : true } , async (ctx , { flowDynamic , state }) => { const name = state .get ( 'name' ) await state .update ({ age : ctx .body }) await flowDynamic ( `Thanks for sharing your age! ${ name } ` ) } ) .addAnswer ( 'Here is your data:' , null , async (_ , { flowDynamic , state }) => { const myState = state .getMyState () await flowDynamic ( `Name: ${ myState .name } Age: ${ myState .age } ` ) }) Copy Copied! flowDynamic Dynamic Messages In other occasions we need to send messages in a dynamic way of data that can be variable, below you can see an example of how you should do it and how you should NOT do it. ❌ Avoid it this, does not work because addAnswer serializes the content at the start of execution. let name = '' const flow = addKeyword ( 'hello' ) .addAnswer ( `What is your name?` , { capture : true } , async (ctx) => { name = ctx .body }) .addAnswer ( `Your name is: ${ name } ` ) Copy Copied! If you want to send a dynamic message use flowDynamic. const flow = addKeyword ( 'hello' ) .addAnswer ( `What is your name?` , { capture : true } , async (ctx , { state }) => { await state .update ({ name : ctx .body }) }) .addAction ( async (ctx , { state , flowDynamic }) => { const name = state .get ( 'name' ) await flowDynamic ( `Your name is: ${ name } ` ) }) Copy Copied! Send File When you want to send an image, audio , file or any other file you can do it this way. It is important to note that the URL must be publicly accessible . const flow = addKeyword ( 'hello' ) .addAnswer ( `Send image from URL` , { media : 'https://i.imgur.com/0HpzsEm.png' } ) .addAnswer ( `Send video from Local` , { media : join ( process .cwd () , 'assets' , 'sample.png' ) } ) .addAnswer ( `Send video from URL` , { media : 'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4' } ) .addAnswer ( `Send file from URL` , { media : 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf' } ) Copy Copied! Other ways to use when the route is coming from a dynamic data source const flow = addKeyword ( 'hello' ) .addAction ( async (_ , {flowDynamic}) => { // ...db get source... await flowDynamic ([ {body : 'This is an image' , media : 'https://i.imgur.com/0HpzsEm.png' } ]) await flowDynamic ([ {body : 'This is a video' , media : 'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4' } ]) }) Copy Copied! If you need to send a file that is stored locally you can do that too. The use of join is recommended to ensure correct directory concatenation. const flow = addKeyword ( 'hello' ) .addAction ( async (_ , {flowDynamic}) => { const pathLocal = join ( 'assets' , 'doc.pdf' ) // pathLocal = c:/doc.pdf await flowDynamic ([ {body : 'This is a video' , media : pathLocal } ]) }) Copy Copied! gotoFlow Switch to another flow If you want to divert a conversational flow to another logic flow based on a response input you can do it in this way: const flowToA = addKeyword ( EVENTS . ACTION ) .addAnswer ( 'Here we have Option A!' ) const flowToB = addKeyword ( EVENTS . ACTION ) .addAnswer ( 'Here we have Option B!' ) const flowToC = addKeyword ( EVENTS . ACTION ) .addAnswer ( 'Here we have Option C!' ) const flowDefault = addKeyword ( EVENTS . ACTION ) .addAnswer ( \"We don't have that Option 🤔\" ) const flow = addKeyword ( 'order' ) .addAnswer ( [ `Which one is the best option for you?` , `Type **A**` , `Type **B**` , `Type **C**` , ] , { capture : true } ) .addAnswer ( `Thanks for you answer` , async (ctx , {gotoFlow}) => { const userAnswer = ctx .body if (userAnswer === 'A' ){ return gotoFlow (flowToA) } if (userAnswer === 'B' ){ return gotoFlow (flowToB) } if (userAnswer === 'C' ){ return gotoFlow (flowToC) } return gotoFlow (flowDefault) }) .addAnswer ( `this message will not be sent` ) Copy Copied! ❌ This does not work, the invocation of the gotoFlow function must necessarily include a return. //...Previous code... ... .addAnswer ( `Thanks for you answer` , async (ctx , {gotoFlow}) => { gotoFlow (flowToA) }) .addAnswer ( `this message will not be sent` ) Copy Copied! This does work //...Previous code... ... .addAnswer ( `Thanks for you answer` , async (ctx , {gotoFlow}) => { return gotoFlow (flowToA) }) .addAnswer ( `this message will not be sent` ) Copy Copied! state Turn off bot a certain user Sometimes we will need to turn off the bot for a certain user, so that we can have a conversation with the client without the bot interfering. const flow = addKeyword < BaileysProvider >( 'magic keyword' ) .addAction ( async (_ , { state , endFlow }) => { const botOffForThisUser = state .get < boolean >( 'botOffForThisUser' ) await state .update ({botOffForThisUser :! botOffForThisUser}) if (botOffForThisUser) return endFlow () }) .addAnswer ( 'Hello!' ) Copy Copied! state Turn off for everyone Sometimes we will need to disable the bot for all people, without the need to shut down the server or stop the script. const flow = addKeyword < BaileysProvider >( 'botoff' ) .addAction ( async (_ , { globalState , endFlow }) => { const botOffForEveryOne = globalState .get < boolean >( 'botOffForEveryOne' ) await globalState .update ({botOffForEveryOne :! botOffForEveryOne}) if (botOffForEveryOne) return endFlow () }) .addAnswer ( 'Hello!' ) Copy Copied! state Bot Self-Interaction In certain scenarios, it is necessary for the bot's phone number (host) to be able to interact within logical flows. To achieve this, we have several configurable options: host: This is used when you want the bot to be able to respond to messages in the same chat with itself. For example, if the bot's number is 0000, it will be able to send and receive messages to/from 0000. both: This option allows both the bot and you (the developer/administrator) to intervene in the chat of a person interacting with the bot. none: (default option) Only allows interaction between the user and the bot, without intervention from the host number. app.ts import { createBot , createProvider , createFlow , addKeyword , MemoryDB } from '@builderbot/bot' import { BaileysProvider } from '@builderbot/provider-baileys' const main = async () => { const adapterDB = new MemoryDB () const adapterFlow = createFlow ([ ... ]) const adapterProvider = createProvider (BaileysProvider , { writeMyself : 'host' as 'none' | 'host' | 'both' }) adapterProvider .initHttpServer ( 3000 ) await createBot ({ flow : adapterFlow , provider : adapterProvider , database : adapterDB , }) } main () Copy Copied! Guides My first chatbot Learn how build your first chatbot in few minutes Read more Concepts Understand the essential concepts for building bots Read more Add Functions The key to learning how to write flows is add-functions. Read more Plugins Unlimitate and start implementing the community plugins. Read more Resources Modularize Learn how to modularise flows so that you can have a more maintainable bot. Send Message How to send a message via HTTP to start conversations, you can send multimedia as well. Dockerizer A good practice is to dockerise your bots to make them more maintainable and effective. Events Learning about events will make us more fluent when creating chatbots.", "chunks": [{"content": "Examples Below you will find different examples showing the implementation in different use cases. These examples have been compiled based on the community, feel free to post an example that you like or that you think would be useful for new people. How to Update to the Latest Version To ensure you're using the most up-to-date features and bug fixes, it's important to keep your BuilderBot installation current. Follow the steps below to update to the latest version.\nTo keep your project up to date, make sure to run the command to update the core and the corresponding provider pnpm install @builderbot/bot@latest pnpm install @builderbot/provider-baileys@latest pnpm install @builderbot/provider-wppconnect@latest pnpm install @builderbot/provider-venom@latest pnpm install @builderbot/provider-meta@latest pnpm install @builderbot/provider-twilio@latest Copy Copied! My first bot The following code represents the quick use of a bot that when you type the word hi , greets you with a welcome message and asks you for your name and then returns a funny image app.ts import { createBot , createProvider , createFlow , addKeyword , MemoryDB } from '@builderbot/bot' import { BaileysProvider } from '@builderbot/provider-baileys' const welcomeFlow = addKeyword < BaileysProvider , MemoryDB >([ 'hi' ]) .addAnswer ( 'Ey! welcome' ) .addAnswer ( 'Your name is?' , { capture : true } , async (ctx , { flowDynamic }) => { await flowDynamic ([ `nice! ${ ctx .body } ` , 'I will send you a funny image' ]) }) .addAction ( async (_ , {flowDynamic}) => { const dataApi = await fetch ( `https://shibe.online/api/shibes?count=1&urls=true&httpsUrls=true` ) const [ imageUrl ] = await dataApi .json () await flowDynamic ([{body : '😜' , media : imageUrl}]) }) const main = async () => { const adapterDB = new MemoryDB () const adapterFlow = createFlow ([welcomeFlow]) const adapterProvider = createProvider (BaileysProvider) adapterProvider .initHttpServer ( 3000 ) await createBot ({ flow : adapterFlow , provider : adapterProvider , database : adapterDB , }) } main () Copy Copied! state Conversational history Often, we will need to manage conversations and keep the context in a memory called state which is volatile and accessible from any function executed in a stream. const welcomeFlow = addKeyword ([ 'hello' ]) .addAnswer ( \"¿What's your name?\" , {capture : true } , async (ctx , { flowDynamic , state }) => { await state .update ({ name : ctx .body }) await flowDynamic ( 'Thanks for giving me your name!' ) } ) .addAnswer ( '¿How old are you?' , {capture : true } , async (ctx , { flowDynamic , state }) => { const name = state .get ( 'name' ) await state .update ({ age : ctx .body }) await flowDynamic ( `Thanks for sharing your age! ${ name } ` ) } ) .addAnswer ( 'Here is your data:' , null , async (_ , { flowDynamic , state }) => { const myState = state .getMyState () await flowDynamic ( `Name: ${ myState .name } Age: ${ myState .age } ` ) }) Copy Copied! flowDynamic Dynamic Messages In other occasions we need to send messages in a dynamic way of data that can be variable, below you can see an example of how you should do it and how you should NOT do it. ❌ Avoid it this, does not work because addAnswer serializes the content at the start of execution. let name = '' const flow = addKeyword ( 'hello' ) .addAnswer ( `What is your name?` , { capture : true } , async (ctx) => { name = ctx .body }) .addAnswer ( `Your name is: ${ name } ` ) Copy Copied! If you want to send a dynamic message use flowDynamic. const flow = addKeyword ( 'hello' ) .addAnswer ( `What is your name?` , { capture : true } , async (ctx , { state }) => { await state .update ({ name : ctx .body }) }) .addAction ( async (ctx , { state , flowDynamic }) => { const name = state .get ( 'name' ) await flowDynamic ( `Your name is: ${ name } ` ) }) Copy Copied! Send File When you want to send an image, audio , file or any other file you can do it this way. It is important to note that the URL must be publicly accessible . const flow = addKeyword ( 'hello' ) .addAnswer ( `Send image from URL` , { media : 'https://i.imgur.com/0HpzsEm.png' } ) .addAnswer ( `Send video from Local` , { media : join ( process .cwd () , 'assets' , 'sample.png' ) } ) .addAnswer ( `Send video from URL` , { media : 'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4' } ) .addAnswer ( `Send file from URL` , { media : 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf' } ) Copy Copied! Other ways to use when the route is coming from a dynamic data source const flow = addKeyword ( 'hello' ) .addAction ( async (_ , {flowDynamic}) => { // ...db get source... await flowDynamic ([ {body : 'This is an image' , media : 'https://i.imgur.com/0HpzsEm.png' } ]) await flowDynamic ([ {body : 'This is a video' , media : 'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4' } ]) }) Copy Copied! If you need to send a file that is stored locally you can do that too. The use of join is recommended to ensure correct directory concatenation. const flow = addKeyword ( 'hello' ) .addAction ( async (_ , {flowDynamic}) => { const pathLocal = join ( 'assets' , 'doc.pdf' ) // pathLocal = c:/doc.pdf await flowDynamic ([ {body : 'This is a video' , media : pathLocal } ]) }) Copy Copied! gotoFlow Switch to another flow If you want to divert a conversational flow to another logic flow based on a response input you can do it in this way: const flowToA = addKeyword ( EVENTS . ACTION ) .addAnswer ( 'Here we have Option A!' ) const flowToB = addKeyword ( EVENTS . ACTION ) .addAnswer ( 'Here we have Option B!' ) const flowToC = addKeyword ( EVENTS . ACTION ) .addAnswer ( 'Here we have Option C!' ) const flowDefault = addKeyword ( EVENTS . ACTION ) .addAnswer ( \"We don't have that Option 🤔\" ) const flow = addKeyword ( 'order' ) .addAnswer ( [ `Which one is the best option for you?` , `Type **A**` , `Type **B**` , `Type **C**` , ] , { capture : true } ) .addAnswer ( `Thanks for you answer` , async (ctx , {gotoFlow}) => { const userAnswer = ctx .body if (userAnswer === 'A' ){ return gotoFlow (flowToA) } if (userAnswer === 'B' ){ return gotoFlow (flowToB) } if (userAnswer === 'C' ){ return gotoFlow (flowToC) } return gotoFlow (flowDefault) }) .addAnswer ( `this message will not be sent` ) Copy Copied! ❌ This does not work, the invocation of the gotoFlow function must necessarily include a return. //...Previous code... ... .addAnswer ( `Thanks for you answer` , async (ctx , {gotoFlow}) => { gotoFlow (flowToA) }) .addAnswer ( `this message will not be sent` ) Copy Copied! This does work //...Previous code... ... .addAnswer ( `Thanks for you answer` , async (ctx , {gotoFlow}) => { return gotoFlow (flowToA) }) .addAnswer ( `this message will not be sent` ) Copy Copied! state Turn off bot a certain user Sometimes we will need to turn off the bot for a certain user, so that we can have a conversation with the client without the bot interfering. const flow = addKeyword < BaileysProvider >( 'magic keyword' ) .addAction ( async (_ , { state , endFlow }) => { const botOffForThisUser = state .get < boolean >( 'botOffForThisUser' ) await state .update ({botOffForThisUser :! botOffForThisUser}) if (botOffForThisUser) return endFlow () }) .addAnswer ( 'Hello!' ) Copy Copied! state Turn off for everyone Sometimes we will need to disable the bot for all people, without the need to shut down the server or stop the script. const flow = addKeyword < BaileysProvider >( 'botoff' ) .addAction ( async (_ , { globalState , endFlow }) => { const botOffForEveryOne = globalState .get < boolean >( 'botOffForEveryOne' ) await globalState .update ({botOffForEveryOne :! botOffForEveryOne}) if (botOffForEveryOne) return endFlow () }) .addAnswer ( 'Hello!' ) Copy Copied! state Bot Self-Interaction In certain scenarios, it is necessary for the bot's phone number (host) to be able to interact within logical flows. To achieve this, we have several configurable options: host: This is used when you want the bot to be able to respond to messages in the same chat with itself. For example, if the bot's number is 0000, it will be able to send and receive messages to/from 0000. both: This option allows both the bot and you (the developer/administrator) to intervene in the chat of a person interacting with the bot. none: (default option) Only allows interaction between the user and the bot, without intervention from the host number. app.ts import { createBot , createProvider , createFlow , addKeyword , MemoryDB } from '@builderbot/bot' import { BaileysProvider } from '@builderbot/provider-baileys' const main = async () => { const adapterDB = new MemoryDB () const adapterFlow = createFlow ([ ... ]) const adapterProvider = createProvider (BaileysProvider , { writeMyself : 'host' as 'none' | 'host' | 'both' }) adapterProvider .initHttpServer ( 3000 ) await createBot ({ flow : adapterFlow , provider : adapterProvider , database : adapterDB , }) } main () Copy Copied! Guides My first chatbot Learn how build your first chatbot in few minutes Read more Concepts Understand the essential concepts for building bots Read more Add Functions The key to learning how to write flows is add-functions. Read more Plugins Unlimitate and start implementing the community plugins. Read more Resources Modularize Learn how to modularise flows so that you can have a more maintainable bot. Send Message How to send a message via HTTP to start conversations, you can send multimedia as well. Dockerizer A good practice is to dockerise your bots to make them more maintainable and effective. Events Learning about events will make us more fluent when creating chatbots.", "metadata": {"title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "url": "https://www.builderbot.app/en/uses-cases", "section": "Sin título"}, "embedding": null}], "headers": [{"level": 4, "text": "BuilderBot", "id": ""}, {"level": 4, "text": "BuilderBot", "id": ""}, {"level": 2, "text": "Start here", "id": ""}, {"level": 2, "text": "Basics", "id": ""}, {"level": 2, "text": "Built-in", "id": ""}, {"level": 2, "text": "Providers", "id": ""}, {"level": 2, "text": "Deploy", "id": ""}, {"level": 2, "text": "Recipes", "id": ""}, {"level": 2, "text": "Tutorials", "id": ""}, {"level": 2, "text": "Community Contribute", "id": ""}, {"level": 2, "text": "Plugins", "id": ""}, {"level": 1, "text": "Examples", "id": ""}, {"level": 2, "text": "How to Update to the Latest Version", "id": "how-to-update-to-the-latest-version"}, {"level": 2, "text": "My first bot", "id": "my-first-bot"}, {"level": 3, "text": "app.ts", "id": ""}, {"level": 2, "text": "Conversational history", "id": "conversational-history"}, {"level": 2, "text": "Dynamic Messages", "id": "dynamic-messages"}, {"level": 2, "text": "Send File", "id": "send-file"}, {"level": 2, "text": "Switch to another flow", "id": "switch-to-another-flow"}, {"level": 2, "text": "Turn off bot a certain user", "id": "turn-off-bot-a-certain-user"}, {"level": 2, "text": "Turn off for everyone", "id": "turn-off-for-everyone"}, {"level": 2, "text": "Bot Self-Interaction", "id": "bot-self-interaction"}, {"level": 3, "text": "app.ts", "id": ""}, {"level": 2, "text": "Guides", "id": "guides"}, {"level": 3, "text": "My first chatbot", "id": ""}, {"level": 3, "text": "Concepts", "id": ""}, {"level": 3, "text": "Add Functions", "id": ""}, {"level": 3, "text": "Plugins", "id": ""}, {"level": 2, "text": "Resources", "id": "resources"}, {"level": 3, "text": "Modularize", "id": ""}, {"level": 3, "text": "Send Message", "id": ""}, {"level": 3, "text": "Dockerizer", "id": ""}, {"level": 3, "text": "Events", "id": ""}], "code_blocks": [{"language": "overflow-x-auto", "code": "pnpminstall@builderbot/bot@latestpnpminstall@builderbot/provider-baileys@latestpnpminstall@builderbot/provider-wppconnect@latestpnpminstall@builderbot/provider-venom@latestpnpminstall@builderbot/provider-meta@latestpnpminstall@builderbot/provider-twilio@latest"}, {"language": "language-bash", "code": "pnpminstall@builderbot/bot@latestpnpminstall@builderbot/provider-baileys@latestpnpminstall@builderbot/provider-wppconnect@latestpnpminstall@builderbot/provider-venom@latestpnpminstall@builderbot/provider-meta@latestpnpminstall@builderbot/provider-twilio@latest"}, {"language": "", "code": "hi"}, {"language": "overflow-x-auto", "code": "import{ createBot,createProvider,create<PERSON>low,addKeyword,MemoryDB }from'@builderbot/bot'import{ BaileysProvider }from'@builderbot/provider-baileys'constwelcomeFlow=addKeyword<BaileysProvider,MemoryDB>(['hi']).addAnswer('Ey! welcome').addAnswer('Your name is?',{ capture:true},async(ctx,{ flowDynamic })=>{awaitflowDynamic([`nice!${ctx.body}`,'I will send you a funny image'])}).addAction(async(_,{flowDynamic})=>{constdataApi=awaitfetch(`https://shibe.online/api/shibes?count=1&urls=true&httpsUrls=true`)const[imageUrl]=awaitdataApi.json()awaitflowDynamic([{body:'😜',media:imageUrl}])})constmain=async()=>{constadapterDB=newMemoryDB()constadapterFlow=createFlow([welcomeFlow])constadapterProvider=createProvider(BaileysProvider)adapterProvider.initHttpServer(3000)awaitcreateBot({flow:adapterFlow,provider:adapterProvider,database:adapterDB,})}main()"}, {"language": "language-ts", "code": "import{ createBot,createProvider,create<PERSON>low,addKeyword,MemoryDB }from'@builderbot/bot'import{ BaileysProvider }from'@builderbot/provider-baileys'constwelcomeFlow=addKeyword<BaileysProvider,MemoryDB>(['hi']).addAnswer('Ey! welcome').addAnswer('Your name is?',{ capture:true},async(ctx,{ flowDynamic })=>{awaitflowDynamic([`nice!${ctx.body}`,'I will send you a funny image'])}).addAction(async(_,{flowDynamic})=>{constdataApi=awaitfetch(`https://shibe.online/api/shibes?count=1&urls=true&httpsUrls=true`)const[imageUrl]=awaitdataApi.json()awaitflowDynamic([{body:'😜',media:imageUrl}])})constmain=async()=>{constadapterDB=newMemoryDB()constadapterFlow=createFlow([welcomeFlow])constadapterProvider=createProvider(BaileysProvider)adapterProvider.initHttpServer(3000)awaitcreateBot({flow:adapterFlow,provider:adapterProvider,database:adapterDB,})}main()"}, {"language": "", "code": "state"}, {"language": "overflow-x-auto", "code": "constwelcomeFlow=addKeyword(['hello']).addAnswer(\"¿What's your name?\",{capture:true},async(ctx,{ flowDynamic,state })=>{awaitstate.update({ name:ctx.body })awaitflowDynamic('Thanks for giving me your name!')}).addAnswer('¿How old are you?',{capture:true},async(ctx,{ flowDynamic,state })=>{constname=state.get('name')awaitstate.update({ age:ctx.body })awaitflowDynamic(`Thanks for sharing your age!${name}`)}).addAnswer('Here is your data:',null,async(_,{ flowDynamic,state })=>{constmyState=state.getMyState()awaitflowDynamic(`Name:${myState.name}Age:${myState.age}`)})"}, {"language": "language-ts", "code": "constwelcomeFlow=addKeyword(['hello']).addAnswer(\"¿What's your name?\",{capture:true},async(ctx,{ flowDynamic,state })=>{awaitstate.update({ name:ctx.body })awaitflowDynamic('Thanks for giving me your name!')}).addAnswer('¿How old are you?',{capture:true},async(ctx,{ flowDynamic,state })=>{constname=state.get('name')awaitstate.update({ age:ctx.body })awaitflowDynamic(`Thanks for sharing your age!${name}`)}).addAnswer('Here is your data:',null,async(_,{ flowDynamic,state })=>{constmyState=state.getMyState()awaitflowDynamic(`Name:${myState.name}Age:${myState.age}`)})"}, {"language": "overflow-x-auto", "code": "letname=''constflow=addKeyword('hello').addAnswer(`What is your name?`,{ capture:true},async(ctx)=>{name=ctx.body}).addAnswer(`Your name is:${name}`)"}, {"language": "language-ts", "code": "letname=''constflow=addKeyword('hello').addAnswer(`What is your name?`,{ capture:true},async(ctx)=>{name=ctx.body}).addAnswer(`Your name is:${name}`)"}, {"language": "overflow-x-auto", "code": "constflow=addKeyword('hello').addAnswer(`What is your name?`,{ capture:true},async(ctx,{ state })=>{awaitstate.update({ name:ctx.body })}).addAction(async(ctx,{ state,flowDynamic })=>{constname=state.get('name')awaitflowDynamic(`Your name is:${name}`)})"}, {"language": "language-ts", "code": "constflow=addKeyword('hello').addAnswer(`What is your name?`,{ capture:true},async(ctx,{ state })=>{awaitstate.update({ name:ctx.body })}).addAction(async(ctx,{ state,flowDynamic })=>{constname=state.get('name')awaitflowDynamic(`Your name is:${name}`)})"}, {"language": "overflow-x-auto", "code": "constflow=addKeyword('hello').addAnswer(`Send image from URL`,{ media:'https://i.imgur.com/0HpzsEm.png'}).addAnswer(`Send video from Local`,{ media:join(process.cwd(),'assets','sample.png') }).addAnswer(`Send video from URL`,{ media:'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4'}).addAnswer(`Send file from URL`,{ media:'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'})"}, {"language": "language-ts", "code": "constflow=addKeyword('hello').addAnswer(`Send image from URL`,{ media:'https://i.imgur.com/0HpzsEm.png'}).addAnswer(`Send video from Local`,{ media:join(process.cwd(),'assets','sample.png') }).addAnswer(`Send video from URL`,{ media:'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4'}).addAnswer(`Send file from URL`,{ media:'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'})"}, {"language": "overflow-x-auto", "code": "constflow=addKeyword('hello').addAction(async(_,{flowDynamic})=>{// ...db get source...awaitflowDynamic([{body:'This is an image',media:'https://i.imgur.com/0HpzsEm.png'}])awaitflowDynamic([{body:'This is a video',media:'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4'}])})"}, {"language": "language-ts", "code": "constflow=addKeyword('hello').addAction(async(_,{flowDynamic})=>{// ...db get source...awaitflowDynamic([{body:'This is an image',media:'https://i.imgur.com/0HpzsEm.png'}])awaitflowDynamic([{body:'This is a video',media:'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4'}])})"}, {"language": "", "code": "join"}, {"language": "overflow-x-auto", "code": "constflow=addKeyword('hello').addAction(async(_,{flowDynamic})=>{constpathLocal=join('assets','doc.pdf')// pathLocal = c:/doc.pdfawaitflowDynamic([{body:'This is a video',media:pathLocal }])})"}, {"language": "language-ts", "code": "constflow=addKeyword('hello').addAction(async(_,{flowDynamic})=>{constpathLocal=join('assets','doc.pdf')// pathLocal = c:/doc.pdfawaitflowDynamic([{body:'This is a video',media:pathLocal }])})"}, {"language": "overflow-x-auto", "code": "constflowToA=addKeyword(EVENTS.ACTION).addAnswer('Here we have Option A!')constflowToB=addKeyword(EVENTS.ACTION).addAnswer('Here we have Option B!')constflowToC=addKeyword(EVENTS.ACTION).addAnswer('Here we have Option C!')constflowDefault=addKeyword(EVENTS.ACTION).addAnswer(\"We don't have that Option 🤔\")constflow=addKeyword('order').addAnswer([`Which one is the best option for you?`,`Type **A**`,`Type **B**`,`Type **C**`,],{ capture:true}).addAnswer(`Thanks for you answer`,async(ctx,{gotoFlow})=>{constuserAnswer=ctx.bodyif(userAnswer==='A'){returngotoFlow(flowToA)}if(userAnswer==='B'){returngotoFlow(flowToB)}if(userAnswer==='C'){returngotoFlow(flowToC)}returngotoFlow(flowDefault)}).addAnswer(`this message will not be sent`)"}, {"language": "language-ts", "code": "constflowToA=addKeyword(EVENTS.ACTION).addAnswer('Here we have Option A!')constflowToB=addKeyword(EVENTS.ACTION).addAnswer('Here we have Option B!')constflowToC=addKeyword(EVENTS.ACTION).addAnswer('Here we have Option C!')constflowDefault=addKeyword(EVENTS.ACTION).addAnswer(\"We don't have that Option 🤔\")constflow=addKeyword('order').addAnswer([`Which one is the best option for you?`,`Type **A**`,`Type **B**`,`Type **C**`,],{ capture:true}).addAnswer(`Thanks for you answer`,async(ctx,{gotoFlow})=>{constuserAnswer=ctx.bodyif(userAnswer==='A'){returngotoFlow(flowToA)}if(userAnswer==='B'){returngotoFlow(flowToB)}if(userAnswer==='C'){returngotoFlow(flowToC)}returngotoFlow(flowDefault)}).addAnswer(`this message will not be sent`)"}, {"language": "overflow-x-auto", "code": "//...Previous code.......addAnswer(`Thanks for you answer`,async(ctx,{gotoFlow})=>{gotoFlow(flowToA)}).addAnswer(`this message will not be sent`)"}, {"language": "language-ts", "code": "//...Previous code.......addAnswer(`Thanks for you answer`,async(ctx,{gotoFlow})=>{gotoFlow(flowToA)}).addAnswer(`this message will not be sent`)"}, {"language": "overflow-x-auto", "code": "//...Previous code.......addAnswer(`Thanks for you answer`,async(ctx,{gotoFlow})=>{returngotoFlow(flowToA)}).addAnswer(`this message will not be sent`)"}, {"language": "language-ts", "code": "//...Previous code.......addAnswer(`Thanks for you answer`,async(ctx,{gotoFlow})=>{returngotoFlow(flowToA)}).addAnswer(`this message will not be sent`)"}, {"language": "overflow-x-auto", "code": "constflow=addKeyword<BaileysProvider>('magic keyword').addAction(async(_,{ state,endFlow })=>{constbotOffForThisUser=state.get<boolean>('botOffForThisUser')awaitstate.update({botOffForThisUser:!botOffForThisUser})if(botOffForThisUser)returnendFlow()}).addAnswer('Hello!')"}, {"language": "language-ts", "code": "constflow=addKeyword<BaileysProvider>('magic keyword').addAction(async(_,{ state,endFlow })=>{constbotOffForThisUser=state.get<boolean>('botOffForThisUser')awaitstate.update({botOffForThisUser:!botOffForThisUser})if(botOffForThisUser)returnendFlow()}).addAnswer('Hello!')"}, {"language": "overflow-x-auto", "code": "constflow=addKeyword<BaileysProvider>('botoff').addAction(async(_,{ globalState,endFlow })=>{constbotOffForEveryOne=globalState.get<boolean>('botOffForEveryOne')awaitglobalState.update({botOffForEveryOne:!botOffForEveryOne})if(botOffForEveryOne)returnendFlow()}).addAnswer('Hello!')"}, {"language": "language-ts", "code": "constflow=addKeyword<BaileysProvider>('botoff').addAction(async(_,{ globalState,endFlow })=>{constbotOffForEveryOne=globalState.get<boolean>('botOffForEveryOne')awaitglobalState.update({botOffForEveryOne:!botOffForEveryOne})if(botOffForEveryOne)returnendFlow()}).addAnswer('Hello!')"}, {"language": "overflow-x-auto", "code": "import{ createBot,createProvider,create<PERSON>low,addKeyword,MemoryDB }from'@builderbot/bot'import{ BaileysProvider }from'@builderbot/provider-baileys'constmain=async()=>{constadapterDB=newMemoryDB()constadapterFlow=createFlow([...])constadapterProvider=createProvider(BaileysProvider,{writeMyself:'host'as'none'|'host'|'both'})adapterProvider.initHttpServer(3000)awaitcreateBot({flow:adapterFlow,provider:adapterProvider,database:adapterDB,})}main()"}, {"language": "language-ts", "code": "import{ createBot,createProvider,create<PERSON>low,addKeyword,MemoryDB }from'@builderbot/bot'import{ BaileysProvider }from'@builderbot/provider-baileys'constmain=async()=>{constadapterDB=newMemoryDB()constadapterFlow=createFlow([...])constadapterProvider=createProvider(BaileysProvider,{writeMyself:'host'as'none'|'host'|'both'})adapterProvider.initHttpServer(3000)awaitcreateBot({flow:adapterFlow,provider:adapterProvider,database:adapterDB,})}main()"}], "success": true, "error_message": ""}]