[{"url": "https://github.com/rcereceda/computrabajo/blob/master/README.md", "title": "computrabajo/README.md at master · rcereceda/computrabajo · GitHub", "description": "A gem to access the Computrabajo api. Contribute to rcereceda/computrabajo development by creating an account on GitHub.", "content": "[Skip to content](https://github.com/rcereceda/computrabajo/blob/master/<#start-of-content>)\n## Navigation Menu\nToggle navigation\n[ ](https://github.com/rcereceda/computrabajo/blob/master/</>)\n[ Sign in ](https://github.com/rcereceda/computrabajo/blob/master/</login?return_to=https%3A%2F%2Fgithub.com%2Frcereceda%2Fcomputrabajo%2Fblob%2Fmaster%2FREADME.md>)\n  * Product \n    * [ GitHub Copilot Write better code with AI  ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/features/copilot>)\n    * [ Security Find and fix vulnerabilities  ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/features/security>)\n    * [ Actions Automate any workflow  ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/features/actions>)\n    * [ Codespaces Instant dev environments  ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/features/codespaces>)\n    * [ Issues Plan and track work  ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/features/issues>)\n    * [ Code Review Manage code changes  ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/features/code-review>)\n    * [ Discussions Collaborate outside of code  ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/features/discussions>)\n    * [ Code Search Find more, search less  ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/features/code-search>)\nExplore\n    * [ All features ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/features>)\n    * [ Documentation ](https://github.com/rcereceda/computrabajo/blob/master/<https:/docs.github.com>)\n    * [ GitHub Skills ](https://github.com/rcereceda/computrabajo/blob/master/<https:/skills.github.com>)\n    * [ Blog ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.blog>)\n  * Solutions \nBy company size\n    * [ Enterprises ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/enterprise>)\n    * [ Small and medium teams ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/team>)\n    * [ Startups ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/enterprise/startups>)\n    * [ Nonprofits ](https://github.com/rcereceda/computrabajo/blob/master/</solutions/industry/nonprofits>)\nBy use case\n    * [ DevSecOps ](https://github.com/rcereceda/computrabajo/blob/master/</solutions/use-case/devsecops>)\n    * [ DevOps ](https://github.com/rcereceda/computrabajo/blob/master/</solutions/use-case/devops>)\n    * [ CI/CD ](https://github.com/rcereceda/computrabajo/blob/master/</solutions/use-case/ci-cd>)\n    * [ View all use cases ](https://github.com/rcereceda/computrabajo/blob/master/</solutions/use-case>)\nBy industry\n    * [ Healthcare ](https://github.com/rcereceda/computrabajo/blob/master/</solutions/industry/healthcare>)\n    * [ Financial services ](https://github.com/rcereceda/computrabajo/blob/master/</solutions/industry/financial-services>)\n    * [ Manufacturing ](https://github.com/rcereceda/computrabajo/blob/master/</solutions/industry/manufacturing>)\n    * [ Government ](https://github.com/rcereceda/computrabajo/blob/master/</solutions/industry/government>)\n    * [ View all industries ](https://github.com/rcereceda/computrabajo/blob/master/</solutions/industry>)\n[ View all solutions ](https://github.com/rcereceda/computrabajo/blob/master/</solutions>)\n  * Resources \nTopics\n    * [ AI ](https://github.com/rcereceda/computrabajo/blob/master/</resources/articles/ai>)\n    * [ DevOps ](https://github.com/rcereceda/computrabajo/blob/master/</resources/articles/devops>)\n    * [ Security ](https://github.com/rcereceda/computrabajo/blob/master/</resources/articles/security>)\n    * [ Software Development ](https://github.com/rcereceda/computrabajo/blob/master/</resources/articles/software-development>)\n    * [ View all ](https://github.com/rcereceda/computrabajo/blob/master/</resources/articles>)\nExplore\n    * [ Learning Pathways ](https://github.com/rcereceda/computrabajo/blob/master/<https:/resources.github.com/learn/pathways>)\n    * [ White papers, Ebooks, Webinars ](https://github.com/rcereceda/computrabajo/blob/master/<https:/resources.github.com>)\n    * [ Customer Stories ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/customer-stories>)\n    * [ Partners ](https://github.com/rcereceda/computrabajo/blob/master/<https:/partner.github.com>)\n    * [ Executive Insights ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/solutions/executive-insights>)\n  * Open Source \n    * [ GitHub Sponsors Fund open source developers  ](https://github.com/rcereceda/computrabajo/blob/master/</sponsors>)\n    * [ The ReadME Project GitHub community articles  ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/readme>)\nRepositories\n    * [ Topics ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/topics>)\n    * [ Trending ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/trending>)\n    * [ Collections ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/collections>)\n  * Enterprise \n    * [ Enterprise platform AI-powered developer platform  ](https://github.com/rcereceda/computrabajo/blob/master/</enterprise>)\nAvailable add-ons\n    * [ Advanced Security Enterprise-grade security features  ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/enterprise/advanced-security>)\n    * [ GitHub Copilot Enterprise-grade AI features  ](https://github.com/rcereceda/computrabajo/blob/master/</features/copilot#enterprise>)\n    * [ Premium Support Enterprise-grade 24/7 support  ](https://github.com/rcereceda/computrabajo/blob/master/</premium-support>)\n  * [Pricing](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/pricing>)\n\n\nSearch or jump to...\n# Search code, repositories, users, issues, pull requests...\nSearch \nClear\n[Search syntax tips](https://github.com/rcereceda/computrabajo/blob/master/<https:/docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax>)\n#  Provide feedback \nWe read every piece of feedback, and take your input very seriously.\nInclude my email address so I can be contacted\nCancel  Submit feedback \n#  Saved searches \n## Use saved searches to filter your results more quickly\nName\nQuery\nTo see all available qualifiers, see our [documentation](https://github.com/rcereceda/computrabajo/blob/master/<https:/docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax>). \nCancel  Create saved search \n[ Sign in ](https://github.com/rcereceda/computrabajo/blob/master/</login?return_to=https%3A%2F%2Fgithub.com%2Frcereceda%2Fcomputrabajo%2Fblob%2Fmaster%2FREADME.md>)\n[ Sign up ](https://github.com/rcereceda/computrabajo/blob/master/</signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fblob%2Fshow&source=header-repo&source_repo=rcereceda%2Fcomputrabajo>) Reseting focus\nYou signed in with another tab or window. [Reload](https://github.com/rcereceda/computrabajo/blob/master/<>) to refresh your session. You signed out in another tab or window. [Reload](https://github.com/rcereceda/computrabajo/blob/master/<>) to refresh your session. You switched accounts on another tab or window. [Reload](https://github.com/rcereceda/computrabajo/blob/master/<>) to refresh your session. Dismiss alert\n{{ message }}\n[ rcereceda ](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda>) / **[computrabajo](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo>) ** Public\n  * [ Notifications ](https://github.com/rcereceda/computrabajo/blob/master/</login?return_to=%2Frcereceda%2Fcomputrabajo>) You must be signed in to change notification settings\n  * [ Fork 0 ](https://github.com/rcereceda/computrabajo/blob/master/</login?return_to=%2Frcereceda%2Fcomputrabajo>)\n  * [ Star  4 ](https://github.com/rcereceda/computrabajo/blob/master/</login?return_to=%2Frcereceda%2Fcomputrabajo>)\n\n\n  * [ Code ](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo>)\n  * [ Issues 0 ](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/issues>)\n  * [ Pull requests 0 ](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/pulls>)\n  * [ Actions ](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/actions>)\n  * [ Projects 0 ](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/projects>)\n  * [ Security ](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/security>)\n  * [ Insights ](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/pulse>)\n\n\nAdditional navigation options\n  * [ Code  ](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo>)\n  * [ Issues  ](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/issues>)\n  * [ Pull requests  ](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/pulls>)\n  * [ Actions  ](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/actions>)\n  * [ Projects  ](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/projects>)\n  * [ Security  ](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/security>)\n  * [ Insights  ](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/pulse>)\n\n\n## Files\nmaster\n## Breadcrumbs\n  1. [computrabajo](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/tree/master>)\n\n\n/\n# README.md\nCopy path\nBlame\nBlame\n## Latest commit\n## History\n[History](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/commits/master/README.md>)\n[](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/commits/master/README.md>)\n71 lines (50 loc) · 1.91 KB\nmaster\n## Breadcrumbs\n  1. [computrabajo](https://github.com/rcereceda/computrabajo/blob/master/</rcereceda/computrabajo/tree/master>)\n\n\n/\n# README.md\nTop\n## File metadata and controls\n  * Preview\n  * Code\n  * Blame\n\n\n71 lines (50 loc) · 1.91 KB\n[Raw](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/rcereceda/computrabajo/raw/refs/heads/master/README.md>)\n# Computrabajo\n[](https://github.com/rcereceda/computrabajo/blob/master/<#computrabajo>)\nThis gem was made to connect to the Computrabajo api: <https://iapi.computrabajo.com>\n## Getting started\n[](https://github.com/rcereceda/computrabajo/blob/master/<#getting-started>)\nComputrabajo works with Rails 3.2 onwards. You can add it to your Gemfile with:\n```\ngem 'computrabajo', :git => '**************:rcereceda/computrabajo.git'\n```\n\n...or can fetch the latest developer version with:\n```\ngem 'computrabajo', :git => '**************:rcereceda/computrabajo.git', :branch => 'develop'\n```\n\n### Configuration\n[](https://github.com/rcereceda/computrabajo/blob/master/<#configuration>)\nAfter you finished the gem installation, you need to configure it with your Computrabajo user information. You can do it filling a file like config/initializers/computrabajo.rb with:\n```\nComputrabajo.setup do |config|\n config.username     = \"\"  # username provided by Computrabajo\n config.password     = \"\"  # password provided by Computrabajo\n config.contact_name   = \"\"  # your contact name\n config.contact_email   = \"\"  # your contact email (if not empty, will be shown on the offer detail for registered candidates)\n config.contact_telephone = \"\"  # your phone number\n config.contact_url    = \"\"  # your company URL\n config.job_reference   = \"\"  # your internal offer reference\n config.environment \"production\" # You can choose between production or development\nend\n```\n\n## How to use\n[](https://github.com/rcereceda/computrabajo/blob/master/<#how-to-use>)\nAll return a json object, and raise an error (401, 403, 500) if there was one.\nOn success there is a \"status\" field on response with the following values:\n  * Pendiente = 1\n  * Activa = 2\n  * Caducada = 3\n  * Eliminada = 4\n  * Rechazada = 5\n  * Archivada = 6\n\n\n### Create (publish) a new publication\n[](https://github.com/rcereceda/computrabajo/blob/master/<#create-publish-a-new-publication>)\n```\npublication = Computrabajo::Publication.new\nComputrabajo.create_aviso(publication.body)\n```\n\n### Get a publication data\n[](https://github.com/rcereceda/computrabajo/blob/master/<#get-a-publication-data>)\n```\nComputrabajo.get_aviso(publication_id)\n```\n\n### Destroy a publication\n[](https://github.com/rcereceda/computrabajo/blob/master/<#destroy-a-publication>)\n```\npublication = { offerId: publication_id }\nComputrabajo.destroy_aviso(publication)\n```\n\n## TODO\n[](https://github.com/rcereceda/computrabajo/blob/master/<#todo>)\n  * Add the missing methods to obtain the list of postulants\n\n\n## Footer\n[ ](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com> \"GitHub\") © 2025 GitHub, Inc. \n### Footer navigation\n  * [Terms](https://github.com/rcereceda/computrabajo/blob/master/<https:/docs.github.com/site-policy/github-terms/github-terms-of-service>)\n  * [Privacy](https://github.com/rcereceda/computrabajo/blob/master/<https:/docs.github.com/site-policy/privacy-policies/github-privacy-statement>)\n  * [Security](https://github.com/rcereceda/computrabajo/blob/master/<https:/github.com/security>)\n  * [Status](https://github.com/rcereceda/computrabajo/blob/master/<https:/www.githubstatus.com/>)\n  * [Docs](https://github.com/rcereceda/computrabajo/blob/master/<https:/docs.github.com/>)\n  * [Contact](https://github.com/rcereceda/computrabajo/blob/master/<https:/support.github.com?tags=dotcom-footer>)\n  * Manage cookies \n  * Do not share my personal information \n\n\nYou can’t perform that action at this time. \n", "links": ["internal", "external"], "images": []}]