import asyncio
from crawl4ai import AsyncWebCrawler

async def main():
    # Create an instance of AsyncWebCrawler with basic configuration
    async with AsyncWebCrawler(
        browser_required=True,  # We need browser-based crawling
        max_concurrent_requests=5,  # Limit concurrent requests
        request_delay=1  # Add delay between requests
    ) as crawler:
        # URL to crawl (you can change this to any website you want to scrape)
        url = "https://example.com"
        
        try:
            # Run the crawler
            result = await crawler.arun(url=url)
            
            # Print the extracted content
            print("\nExtracted content:")
            print("-" * 50)
            print(result.markdown)
            
            # Print metadata
            print("\nMetadata:")
            print("-" * 50)
            if result.metadata:
                print(f"Title: {result.metadata.get('title', 'N/A')}")
                print(f"Description: {result.metadata.get('description', 'N/A')}")
            else:
                print("No metadata available")
            
            # Print any links found
            print("\nLinks found:")
            print("-" * 50)
            if result.links:
                for link in result.links:
                    print(link)
            else:
                print("No links found")
                
        except Exception as e:
            print(f"Error occurred: {e}")

if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
