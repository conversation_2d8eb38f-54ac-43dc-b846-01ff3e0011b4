import json
import os
import sys
from typing import List, Dict, Any
import argparse

# Intenta importar las bibliotecas necesarias para el embedding
try:
    import numpy as np
    from sklearn.metrics.pairwise import cosine_similarity
    from sentence_transformers import SentenceTransformer
    EMBEDDING_AVAILABLE = True
except ImportError:
    EMBEDDING_AVAILABLE = False

class KnowledgeBaseRAG:
    """
    Sistema RAG simple para usar la base de conocimiento generada por docs_knowledge_agent.py
    """
    
    def __init__(self, knowledge_base_path: str):
        """
        Inicializa el sistema RAG con la base de conocimiento.
        
        Args:
            knowledge_base_path: Ruta al archivo JSON de la base de conocimiento
        """
        self.knowledge_base_path = knowledge_base_path
        self.knowledge_base = self._load_knowledge_base()
        self.embedding_model = None
        self.chunk_embeddings = None
        
        # Inicializar el modelo de embedding si está disponible
        if EMBEDDING_AVAILABLE:
            try:
                self.embedding_model = SentenceTransformer('paraphrase-MiniLM-L6-v2')
                print("Modelo de embedding cargado correctamente.")
                # Generar embeddings para todos los chunks
                self._generate_embeddings()
            except Exception as e:
                print(f"Error al cargar el modelo de embedding: {str(e)}")
                self.embedding_model = None
    
    def _load_knowledge_base(self) -> List[Dict[str, Any]]:
        """Carga la base de conocimiento desde el archivo JSON."""
        try:
            with open(self.knowledge_base_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error al cargar la base de conocimiento: {str(e)}")
            return []
    
    def _generate_embeddings(self):
        """Genera embeddings para todos los chunks en la base de conocimiento."""
        if not self.embedding_model:
            return
        
        print("Generando embeddings para la base de conocimiento...")
        all_chunks = []
        chunk_texts = []
        
        # Recopilar todos los chunks
        for page in self.knowledge_base:
            for chunk in page.get('chunks', []):
                all_chunks.append(chunk)
                chunk_texts.append(chunk['content'])
        
        # Generar embeddings
        if chunk_texts:
            self.chunk_embeddings = self.embedding_model.encode(chunk_texts)
            print(f"Embeddings generados para {len(chunk_texts)} chunks.")
        else:
            print("No se encontraron chunks para generar embeddings.")
    
    def search_by_keyword(self, query: str, num_results: int = 3) -> List[Dict[str, Any]]:
        """
        Busca en la base de conocimiento utilizando palabras clave.
        
        Args:
            query: Consulta de búsqueda
            num_results: Número de resultados a devolver
            
        Returns:
            Lista de chunks relevantes
        """
        # Normalizar la consulta
        query_terms = [term.lower() for term in query.split() if len(term) > 2]
        results = []
        
        # Si no hay términos válidos, devolver lista vacía
        if not query_terms:
            return []
        
        # Buscar en cada página y chunk
        for page in self.knowledge_base:
            # Primero buscar en los metadatos de la página
            page_title_lower = page.get('title', '').lower()
            page_relevance = sum(2 for term in query_terms if term in page_title_lower)
            
            # Buscar en cada chunk
            for chunk in page.get('chunks', []):
                content = chunk['content'].lower()
                metadata = chunk.get('metadata', {})
                
                # Calcular puntuación basada en diferentes factores
                # 1. Términos exactos en el contenido
                content_score = sum(1 for term in query_terms if term in content)
                
                # 2. Términos exactos en metadatos (sección, título, etc.) tienen más peso
                section = metadata.get('section', '').lower()
                title = metadata.get('title', '').lower()
                metadata_score = sum(3 for term in query_terms if term in section or term in title)
                
                # 3. Presencia de múltiples términos cercanos (co-ocurrencia)
                co_occurrence_score = 0
                if len(query_terms) > 1:
                    # Contar cuántos términos diferentes aparecen en el contenido
                    terms_present = sum(1 for term in query_terms if term in content)
                    # Bonificación si aparecen múltiples términos
                    if terms_present > 1:
                        co_occurrence_score = terms_present * 2
                
                # 4. Bonificación por código si la consulta parece relacionada con código
                code_score = 0
                code_related_terms = ['function', 'method', 'class', 'import', 'code', 'example', 'syntax', 'def', 'return']
                if any(term in query.lower() for term in code_related_terms) and 'code' in metadata.get('type', '').lower():
                    code_score = 5
                
                # Puntuación total
                total_score = content_score + metadata_score + co_occurrence_score + code_score + page_relevance
                
                if total_score > 0:
                    results.append({
                        'chunk': chunk,
                        'score': total_score,
                        'page_title': page['title'],
                        'url': page['url']
                    })
        
        # Ordenar por puntuación
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:num_results]
    
    def search_by_semantic(self, query: str, num_results: int = 3) -> List[Dict[str, Any]]:
        """
        Busca en la base de conocimiento utilizando similitud semántica.
        
        Args:
            query: Consulta de búsqueda
            num_results: Número de resultados a devolver
            
        Returns:
            Lista de chunks relevantes
        """
        if not EMBEDDING_AVAILABLE or not self.embedding_model or not self.chunk_embeddings:
            print("Búsqueda semántica no disponible. Usando búsqueda por palabras clave.")
            return self.search_by_keyword(query, num_results)
        
        # Generar embedding para la consulta
        query_embedding = self.embedding_model.encode([query])[0]
        
        # Calcular similitud
        similarities = cosine_similarity([query_embedding], self.chunk_embeddings)[0]
        
        # Obtener índices de los chunks más similares
        top_indices = np.argsort(similarities)[-num_results:][::-1]
        
        results = []
        all_chunks = []
        
        # Recopilar todos los chunks
        for page in self.knowledge_base:
            for chunk in page.get('chunks', []):
                all_chunks.append({
                    'chunk': chunk,
                    'page_title': page['title'],
                    'url': page['url']
                })
        
        # Obtener los chunks más similares
        for idx in top_indices:
            if idx < len(all_chunks):
                chunk_info = all_chunks[idx]
                results.append({
                    'chunk': chunk_info['chunk'],
                    'score': float(similarities[idx]),
                    'page_title': chunk_info['page_title'],
                    'url': chunk_info['url']
                })
        
        return results
    
    def answer_question(self, question: str, use_semantic: bool = True, num_results: int = 3) -> str:
        """
        Responde a una pregunta utilizando la base de conocimiento.
        
        Args:
            question: Pregunta a responder
            use_semantic: Si es True, utiliza búsqueda semántica; de lo contrario, usa palabras clave
            num_results: Número de resultados a utilizar
            
        Returns:
            Respuesta generada
        """
        # Buscar chunks relevantes
        if use_semantic and EMBEDDING_AVAILABLE and self.embedding_model:
            relevant_chunks = self.search_by_semantic(question, num_results)
        else:
            relevant_chunks = self.search_by_keyword(question, num_results)
        
        if not relevant_chunks:
            return "No encontré información relevante para responder a tu pregunta."
        
        # Construir respuesta estructurada
        response = f"Basado en la documentación, encontré la siguiente información relevante:\n\n"
        
        # Añadir fragmentos con formato mejorado
        for i, chunk in enumerate(relevant_chunks, 1):
            # Obtener metadatos del chunk para mostrar información más contextual
            metadata = chunk['chunk'].get('metadata', {})
            section = metadata.get('section', '')
            
            # Mostrar encabezado con información contextual
            response += f"### Fragmento {i} (Relevancia: {chunk['score']:.2f})\n"
            if section:
                response += f"**Sección:** {section}\n"
            response += f"**Fuente:** [{chunk['page_title']}]({chunk['url']})\n\n"
            
            # Formatear el contenido para mejor legibilidad
            content = chunk['chunk']['content']
            
            # Detectar si hay bloques de código y formatearlos adecuadamente
            if '```' in content or metadata.get('type') == 'code':
                # Ya tiene formato de código, lo dejamos como está
                response += f"{content}\n\n"
            else:
                # Formatear el texto normal
                # Limitar longitud pero intentar no cortar en medio de una palabra o frase
                max_length = 500
                if len(content) > max_length:
                    # Encontrar el último espacio antes del límite
                    last_space = content[:max_length].rfind(' ')
                    if last_space > 0:
                        content = content[:last_space] + "..."
                    else:
                        content = content[:max_length] + "..."
                
                response += f"{content}\n\n"
        
        # Añadir enlaces a las fuentes originales
        if len(relevant_chunks) > 0:
            response += f"\n## Fuentes para más información\n"
            seen_urls = set()
            for chunk in relevant_chunks:
                url = chunk['url']
                if url not in seen_urls:
                    response += f"- [{chunk['page_title']}]({url})\n"
                    seen_urls.add(url)
        
        return response

def main():
    parser = argparse.ArgumentParser(description='Sistema RAG simple para usar la base de conocimiento')
    parser.add_argument('--kb', default='./knowledge_base/python_docs_knowledge.json', 
                        help='Ruta al archivo JSON de la base de conocimiento')
    parser.add_argument('--semantic', action='store_true', 
                        help='Usar búsqueda semántica (requiere sentence-transformers)')
    parser.add_argument('--question', type=str,
                        help='Pregunta a responder (si no se proporciona, se ejecutará en modo interactivo)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.kb):
        print(f"Error: No se encontró el archivo de base de conocimiento en {args.kb}")
        sys.exit(1)
    
    if args.semantic and not EMBEDDING_AVAILABLE:
        print("Advertencia: La búsqueda semántica requiere las bibliotecas numpy, scikit-learn y sentence-transformers.")
        print("Instálalas con: pip install numpy scikit-learn sentence-transformers")
        print("Usando búsqueda por palabras clave en su lugar.")
    
    rag = KnowledgeBaseRAG(args.kb)
    
    # Si se proporciona una pregunta como argumento, responderla y salir
    if args.question:
        answer = rag.answer_question(args.question, use_semantic=args.semantic)
        print(answer)
        return
    
    print("\n=== Sistema RAG de Documentación ===")
    print(f"Base de conocimiento: {args.kb}")
    print(f"Modo de búsqueda: {'Semántica' if args.semantic and EMBEDDING_AVAILABLE else 'Palabras clave'}")
    print("Escribe 'salir' para terminar.\n")
    
    while True:
        try:
            question = input("\nHaz una pregunta sobre la documentación: ")
            if question.lower() in ('salir', 'exit', 'quit'):
                break
            
            answer = rag.answer_question(question, use_semantic=args.semantic)
            print("\n" + answer)
        except (KeyboardInterrupt, EOFError):
            print("\nSaliendo del programa...")
            break

if __name__ == "__main__":
    main()
