#!/usr/bin/env python3
"""
Script to generate llms.txt file for the whatsapp-python repository
https://github.com/filipporomani/whatsapp-python

This script crawls the repository and creates a comprehensive llms.txt file
containing all the important documentation and code for LLM consumption.
"""

import os
import requests
import time
import json
from typing import List, Dict, Any
from urllib.parse import urljoin
from datetime import datetime

class WhatsAppPythonCrawler:
    def __init__(self):
        self.base_url = "https://api.github.com/repos/filipporomani/whatsapp-python"
        self.raw_base_url = "https://raw.githubusercontent.com/filipporomani/whatsapp-python/main"
        self.session = requests.Session()
        self.content = []
        
    def add_section(self, title: str, content: str, level: int = 1):
        """Add a section to the llms.txt content"""
        header = "#" * level
        self.content.append(f"\n{header} {title}\n")
        self.content.append(content)
        self.content.append("\n")
    
    def fetch_file_content(self, file_path: str) -> str:
        """Fetch content of a file from the repository"""
        try:
            url = f"{self.raw_base_url}/{file_path}"
            response = self.session.get(url)
            response.raise_for_status()
            return response.text
        except Exception as e:
            print(f"Error fetching {file_path}: {e}")
            return f"Error fetching content: {e}"
    
    def fetch_directory_contents(self, path: str = "") -> List[Dict[str, Any]]:
        """Fetch directory contents from GitHub API"""
        try:
            url = f"{self.base_url}/contents/{path}" if path else f"{self.base_url}/contents"
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error fetching directory {path}: {e}")
            return []
    
    def process_wiki_pages(self):
        """Add information about wiki pages"""
        wiki_info = """
The repository has extensive documentation in its GitHub Wiki with the following pages:
- Home: Welcome and getting started information
- Installation & Setup: How to install and configure the library
- App events: Handling incoming messages and events
- Async: Asynchronous programming with the library
- Error handling: How to handle API errors
- Sending messages: Basic message sending functionality
- Sending images, audios, videos, documents: Media handling
- Sending location: Location sharing functionality
- Sending interactive buttons: Interactive UI elements
- Sending templates: Template message functionality
- Message() Object: Working with message objects
- Marking messages as read: Message status management
- Replying to messages: Message threading
- Reacting to messages: Message reactions
- Webhook & Heroku: Deployment and webhook setup
- v1.1.2: Legacy version documentation

Wiki URL: https://github.com/filipporomani/whatsapp-python/wiki
"""
        self.add_section("Wiki Documentation", wiki_info.strip())
    
    def process_main_files(self):
        """Process main repository files"""
        main_files = [
            "README.md",
            "pyproject.toml", 
            "LICENSE",
            "CODE_OF_CONDUCT.md",
            "CONTRIBUTING.md",
            "SECURITY.md",
            ".gitignore"
        ]
        
        for file_path in main_files:
            print(f"Processing {file_path}...")
            content = self.fetch_file_content(file_path)
            self.add_section(f"File: {file_path}", content, 2)
            time.sleep(0.1)  # Rate limiting
    
    def process_source_code(self):
        """Process source code files"""
        # Main whatsapp module files
        whatsapp_files = [
            "whatsapp/__init__.py",
            "whatsapp/constants.py", 
            "whatsapp/errors.py"
        ]
        
        for file_path in whatsapp_files:
            print(f"Processing {file_path}...")
            content = self.fetch_file_content(file_path)
            self.add_section(f"Source: {file_path}", content, 2)
            time.sleep(0.1)
    
    def process_examples(self):
        """Process example files"""
        examples_dir = self.fetch_directory_contents("examples")
        
        self.add_section("Examples Directory", "The following examples demonstrate how to use the library:", 2)
        
        for item in examples_dir:
            if item["type"] == "file" and item["name"].endswith(".py"):
                print(f"Processing example: {item['name']}...")
                content = self.fetch_file_content(f"examples/{item['name']}")
                self.add_section(f"Example: {item['name']}", content, 3)
                time.sleep(0.1)
    
    def process_extensions(self):
        """Process extension directories"""
        # Process async_ext directory
        async_ext_files = self.fetch_directory_contents("whatsapp/async_ext")
        if async_ext_files:
            self.add_section("Async Extensions", "Asynchronous extensions for the library:", 2)
            for item in async_ext_files:
                if item["type"] == "file" and item["name"].endswith(".py"):
                    print(f"Processing async extension: {item['name']}...")
                    content = self.fetch_file_content(f"whatsapp/async_ext/{item['name']}")
                    self.add_section(f"Async Extension: {item['name']}", content, 3)
                    time.sleep(0.1)

        # Process ext directory
        ext_files = self.fetch_directory_contents("whatsapp/ext")
        if ext_files:
            self.add_section("Extensions", "Additional extensions for the library:", 2)
            for item in ext_files:
                if item["type"] == "file" and item["name"].endswith(".py"):
                    print(f"Processing extension: {item['name']}...")
                    content = self.fetch_file_content(f"whatsapp/ext/{item['name']}")
                    self.add_section(f"Extension: {item['name']}", content, 3)
                    time.sleep(0.1)

    def process_tests(self):
        """Process test files"""
        tests_dir = self.fetch_directory_contents("tests")
        if tests_dir:
            self.add_section("Tests", "Test files for the library:", 2)
            for item in tests_dir:
                if item["type"] == "file" and item["name"].endswith(".py"):
                    print(f"Processing test: {item['name']}...")
                    content = self.fetch_file_content(f"tests/{item['name']}")
                    self.add_section(f"Test: {item['name']}", content, 3)
                    time.sleep(0.1)

    def process_github_workflows(self):
        """Process GitHub Actions workflows"""
        workflows_dir = self.fetch_directory_contents(".github/workflows")
        if workflows_dir:
            self.add_section("GitHub Workflows", "CI/CD workflows and automation:", 2)
            for item in workflows_dir:
                if item["type"] == "file" and (item["name"].endswith(".yml") or item["name"].endswith(".yaml")):
                    print(f"Processing workflow: {item['name']}...")
                    content = self.fetch_file_content(f".github/workflows/{item['name']}")
                    self.add_section(f"Workflow: {item['name']}", content, 3)
                    time.sleep(0.1)
    
    def generate_llms_txt(self):
        """Generate the complete llms.txt file"""
        print("Starting whatsapp-python repository crawl...")
        
        # Add header with timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
        header = f"""# whatsapp-python Repository Documentation

Generated on: {timestamp}
Repository: https://github.com/filipporomani/whatsapp-python
Description: Free, open-source Python wrapper for the WhatsApp Cloud API

This file contains comprehensive documentation and source code for the whatsapp-python library,
automatically generated for LLM consumption and understanding.

## Overview

whatsapp-python is a modern Python library that provides:
- Async/await interface for WhatsApp Cloud API
- Full support for Graph API error handling
- Optimized for high-load workflows using asynchronous programming
- All WhatsApp Business chat UI features supported
- Event listening for incoming messages
- Media sending (images, audio, video, documents)
- Interactive buttons and templates
- Message reactions and replies
- Location sharing
- Contact management
- Template message support
- Webhook integration

## Key Features
- Modern interface using async and await
- Always up to date with WhatsApp API changes
- Forked from Neurotech-HQ/heyoo with significant improvements
- Comprehensive error handling
- Extensive documentation and examples
"""
        self.content.append(header)
        
        # Process different sections
        self.process_wiki_pages()
        self.process_main_files()
        self.process_source_code()
        self.process_examples()
        self.process_extensions()
        self.process_tests()
        self.process_github_workflows()
        
        # Write to file
        output_file = "llms.txt"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write("".join(self.content))
        
        print(f"\nllms.txt generated successfully!")
        print(f"File size: {os.path.getsize(output_file)} bytes")
        print(f"Total sections: {len([c for c in self.content if c.startswith('#')])}")

if __name__ == "__main__":
    crawler = WhatsAppPythonCrawler()
    crawler.generate_llms_txt()
