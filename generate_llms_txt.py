#!/usr/bin/env python3
"""
Script to generate llms.txt file for the whatsapp-python repository
https://github.com/filipporomani/whatsapp-python

This script uses Crawl4AI to crawl the repository and creates a comprehensive
llms.txt file containing all the important documentation and code for LLM consumption.
"""

import os
import asyncio
import time
import json
from typing import List, Dict, Any
from datetime import datetime
from crawl4ai import AsyncWebCrawler
from crawl4ai.extraction_strategy import LLMExtractionStrategy

class WhatsAppPythonCrawler:
    def __init__(self):
        self.base_url = "https://github.com/filipporomani/whatsapp-python"
        self.raw_base_url = "https://raw.githubusercontent.com/filipporomani/whatsapp-python/main"
        self.wiki_url = "https://github.com/filipporomani/whatsapp-python/wiki"
        self.content = []
        self.crawler = None
        
    def add_section(self, title: str, content: str, level: int = 1):
        """Add a section to the llms.txt content"""
        header = "#" * level
        self.content.append(f"\n{header} {title}\n")
        self.content.append(content)
        self.content.append("\n")
    
    async def crawl_url(self, url: str, extraction_strategy=None) -> str:
        """Crawl a URL using Crawl4AI"""
        try:
            if not self.crawler:
                self.crawler = AsyncWebCrawler(verbose=True)
                await self.crawler.astart()

            result = await self.crawler.arun(
                url=url,
                extraction_strategy=extraction_strategy,
                bypass_cache=True
            )

            if result.success:
                return result.extracted_content if extraction_strategy else result.markdown
            else:
                print(f"Error crawling {url}: {result.error_message}")
                return f"Error crawling content: {result.error_message}"
        except Exception as e:
            print(f"Error crawling {url}: {e}")
            return f"Error crawling content: {e}"

    async def fetch_raw_file(self, file_path: str) -> str:
        """Fetch raw file content from GitHub"""
        url = f"{self.raw_base_url}/{file_path}"
        return await self.crawl_url(url)
    
    async def process_wiki_pages(self):
        """Crawl and process wiki pages"""
        print("Crawling wiki pages...")

        # First crawl the main wiki page to get the structure
        wiki_content = await self.crawl_url(self.wiki_url)
        self.add_section("Wiki Home Page", wiki_content)

        # Define known wiki pages to crawl
        wiki_pages = [
            "Installation-&-Setup",
            "App-events",
            "Async",
            "Error-handling",
            "Sending-messages",
            "Sending-images",
            "Sending-audios",
            "Sending-videos",
            "Sending-documents",
            "Sending-location",
            "Sending-interactive-button-(list)",
            "Sending-reply-buttons",
            "Sending-templates",
            "Message()-Object",
            "Marking-messages-as-read",
            "Replying-to-message",
            "Reacting-to-messages",
            "Webhook-&-Heroku"
        ]

        for page in wiki_pages:
            try:
                page_url = f"{self.wiki_url}/{page}"
                print(f"Crawling wiki page: {page}")
                content = await self.crawl_url(page_url)
                self.add_section(f"Wiki: {page.replace('-', ' ')}", content, 2)
                await asyncio.sleep(1)  # Rate limiting
            except Exception as e:
                print(f"Error crawling wiki page {page}: {e}")
                continue
    
    async def process_main_files(self):
        """Process main repository files"""
        main_files = [
            "README.md",
            "pyproject.toml",
            "LICENSE",
            "CODE_OF_CONDUCT.md",
            "CONTRIBUTING.md",
            "SECURITY.md",
            ".gitignore"
        ]

        for file_path in main_files:
            print(f"Processing {file_path}...")
            content = await self.fetch_raw_file(file_path)
            self.add_section(f"File: {file_path}", content, 2)
            await asyncio.sleep(0.5)  # Rate limiting
    
    async def process_source_code(self):
        """Process source code files"""
        # Main whatsapp module files
        whatsapp_files = [
            "whatsapp/__init__.py",
            "whatsapp/constants.py",
            "whatsapp/errors.py"
        ]

        for file_path in whatsapp_files:
            print(f"Processing {file_path}...")
            content = await self.fetch_raw_file(file_path)
            self.add_section(f"Source: {file_path}", content, 2)
            await asyncio.sleep(0.5)
    
    async def process_examples(self):
        """Process example files"""
        # Known example files from the repository
        example_files = [
            "reply_to_message_obj.py",
            "sending_audio.py",
            "sending_button.py",
            "sending_button_async.py",
            "sending_document.py",
            "sending_image.py",
            "sending_location.py",
            "sending_message.py",
            "sending_message_async.py",
            "sending_template_message.py",
            "sending_video.py",
            "standalone_hook.py"
        ]

        self.add_section("Examples Directory", "The following examples demonstrate how to use the library:", 2)

        for filename in example_files:
            print(f"Processing example: {filename}...")
            content = await self.fetch_raw_file(f"examples/{filename}")
            self.add_section(f"Example: {filename}", content, 3)
            await asyncio.sleep(0.5)
    
    async def process_extensions(self):
        """Process extension directories by crawling GitHub tree pages"""
        print("Processing extensions...")

        # Crawl the async_ext directory page
        async_ext_url = f"{self.base_url}/tree/main/whatsapp/async_ext"
        async_ext_content = await self.crawl_url(async_ext_url)
        if "404" not in async_ext_content:
            self.add_section("Async Extensions Directory", async_ext_content, 2)

        # Crawl the ext directory page
        ext_url = f"{self.base_url}/tree/main/whatsapp/ext"
        ext_content = await self.crawl_url(ext_url)
        if "404" not in ext_content:
            self.add_section("Extensions Directory", ext_content, 2)

        await asyncio.sleep(1)

    async def process_tests(self):
        """Process test files by crawling tests directory"""
        print("Processing tests...")
        tests_url = f"{self.base_url}/tree/main/tests"
        tests_content = await self.crawl_url(tests_url)
        if "404" not in tests_content:
            self.add_section("Tests Directory", tests_content, 2)
        await asyncio.sleep(1)

    async def process_github_workflows(self):
        """Process GitHub Actions workflows"""
        print("Processing GitHub workflows...")
        workflows_url = f"{self.base_url}/tree/main/.github/workflows"
        workflows_content = await self.crawl_url(workflows_url)
        if "404" not in workflows_content:
            self.add_section("GitHub Workflows", workflows_content, 2)
        await asyncio.sleep(1)
    
    async def generate_llms_txt(self):
        """Generate the complete llms.txt file"""
        print("Starting whatsapp-python repository crawl with Crawl4AI...")

        try:
            # Add header with timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
            header = f"""# whatsapp-python Repository Documentation

Generated on: {timestamp}
Repository: https://github.com/filipporomani/whatsapp-python
Description: Free, open-source Python wrapper for the WhatsApp Cloud API

This file contains comprehensive documentation and source code for the whatsapp-python library,
automatically generated using Crawl4AI for LLM consumption and understanding.

## Overview

whatsapp-python is a modern Python library that provides:
- Async/await interface for WhatsApp Cloud API
- Full support for Graph API error handling
- Optimized for high-load workflows using asynchronous programming
- All WhatsApp Business chat UI features supported
- Event listening for incoming messages
- Media sending (images, audio, video, documents)
- Interactive buttons and templates
- Message reactions and replies
- Location sharing
- Contact management
- Template message support
- Webhook integration

## Key Features
- Modern interface using async and await
- Always up to date with WhatsApp API changes
- Forked from Neurotech-HQ/heyoo with significant improvements
- Comprehensive error handling
- Extensive documentation and examples
"""
            self.content.append(header)

            # Process different sections
            await self.process_wiki_pages()
            await self.process_main_files()
            await self.process_source_code()
            await self.process_examples()
            await self.process_extensions()
            await self.process_tests()
            await self.process_github_workflows()

        finally:
            # Clean up crawler
            if self.crawler:
                await self.crawler.aclose()

        # Write to file
        output_file = "llms.txt"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write("".join(self.content))

        print(f"\nllms.txt generated successfully!")
        print(f"File size: {os.path.getsize(output_file)} bytes")
        print(f"Total sections: {len([c for c in self.content if c.startswith('#')])}")

async def main():
    crawler = WhatsAppPythonCrawler()
    await crawler.generate_llms_txt()

if __name__ == "__main__":
    asyncio.run(main())
