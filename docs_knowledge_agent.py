import asyncio
import os
import json
import re
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from rich import print
from rich.console import Console
from rich.table import Table
from rich.progress import Progress
from bs4 import BeautifulSoup
from crawl4ai import As<PERSON><PERSON>eb<PERSON>raw<PERSON>, <PERSON><PERSON>er<PERSON>onfig, <PERSON><PERSON>lerRunConfig, CacheMode
from crawl4ai.extraction_strategy import LLMExtractionStrategy
import argparse

# Modelos de datos
class DocumentChunk(BaseModel):
    """Representa un fragmento de documento procesado."""
    content: str
    metadata: Dict[str, Any] = Field(default_factory=dict)
    embedding: Optional[List[float]] = None

class DocumentPage(BaseModel):
    """Representa una página de documentación completa."""
    url: str
    title: str
    content: str
    chunks: List[DocumentChunk] = Field(default_factory=list)
    headers: List[Dict[str, Any]] = Field(default_factory=list)
    code_blocks: List[Dict[str, Any]] = Field(default_factory=list)
    success: bool = True
    error_message: str = ""

class DocsKnowledgeAgent:
    """Agente para extraer documentación de sitios web y prepararla para RAG."""
    
    def __init__(
        self, 
        cache_dir: str = "./cache", 
        output_dir: str = "./knowledge_base",
        llm_provider: str = None,
        llm_api_key: str = None
    ):
        self.cache_dir = cache_dir
        self.output_dir = output_dir
        self.llm_provider = llm_provider
        self.llm_api_key = llm_api_key
        self.console = Console()
        
        # Crear directorios si no existen
        os.makedirs(cache_dir, exist_ok=True)
        os.makedirs(output_dir, exist_ok=True)
        
        # Configuración del navegador
        self.browser_config = BrowserConfig(
            headless=True,
            java_script_enabled=True
        )
    
    async def crawl_documentation(self, urls: List[str], max_depth: int = 0) -> List[DocumentPage]:
        """
        Extrae documentación de una lista de URLs.
        
        Args:
            urls: Lista de URLs a extraer
            max_depth: Profundidad máxima para seguir enlaces (0 = solo las URLs proporcionadas)
            
        Returns:
            Lista de páginas de documentación procesadas
        """
        results = []
        
        # Configuración del crawler
        crawler_config = CrawlerRunConfig(
            cache_mode=CacheMode.ENABLED,
            word_count_threshold=1,
            page_timeout=60000,
            wait_until="networkidle"
        )
        
        with Progress() as progress:
            task = progress.add_task("[cyan]Extrayendo documentación...", total=len(urls))
            
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                for url in urls:
                    try:
                        print(f"[bold blue]Procesando: {url}[/bold blue]")
                        result = await crawler.arun(url=url, config=crawler_config)
                        
                        if result and result.html:
                            # Procesar la página
                            page = await self._process_page(result, url)
                            results.append(page)
                            
                            # Si hay profundidad máxima, extraer enlaces y seguirlos
                            if max_depth > 0:
                                # Implementar lógica para seguir enlaces
                                pass
                    except Exception as e:
                        print(f"[bold red]Error procesando {url}: {str(e)}[/bold red]")
                        results.append(DocumentPage(
                            url=url,
                            title="Error",
                            content="",
                            success=False,
                            error_message=str(e)
                        ))
                    
                    progress.update(task, advance=1)
        
        return results
    
    async def _process_page(self, result, url: str) -> DocumentPage:
        """Procesa una página extraída."""
        if not result.success:
            return DocumentPage(
                url=url,
                title="Error",
                content="",
                success=False,
                error_message=f"Error al extraer la página: {result.error}"
            )
        
        # Extraer título y contenido
        soup = BeautifulSoup(result.html, 'html.parser')
        
        # Obtener el título
        title_tag = soup.find('title')
        title = title_tag.text if title_tag else "Sin título"
        
        # Intentar encontrar el contenido principal
        main_content = None
        
        # Intentar diferentes selectores comunes para el contenido principal
        content_selectors = [
            'main', 
            'article', 
            '.content', 
            '#content',
            '.main-content',
            '#main-content',
            '.documentation',
            '.doc-content',
            '.markdown-body',
            '.post-content',
            'div[role="main"]',
            '.container'
        ]
        
        for selector in content_selectors:
            elements = soup.select(selector)
            if elements:
                # Usar el elemento más grande como contenido principal
                main_content = max(elements, key=lambda x: len(str(x)))
                break
        
        # Si no se encuentra un contenido específico, usar el body
        if not main_content:
            main_content = soup.find('body')
        
        # Si aún no hay contenido, usar todo el HTML
        if not main_content:
            main_content = soup
        
        # Eliminar elementos no deseados
        for element in main_content.select('nav, footer, header, script, style, .sidebar, .navigation, .menu, .ads, .banner'):
            element.decompose()
        
        # Extraer el contenido como texto
        content = main_content.get_text(separator=' ', strip=True)
        
        # Extraer encabezados
        headers = []
        for h_tag in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
            headers.append({
                'level': int(h_tag.name[1]),
                'text': h_tag.get_text(strip=True),
                'id': h_tag.get('id', '')
            })
        
        # Extraer bloques de código
        code_blocks = []
        for code_tag in soup.find_all(['pre', 'code']):
            code_blocks.append({
                'language': code_tag.get('class', [''])[0] if code_tag.get('class') else '',
                'code': code_tag.get_text(strip=True)
            })
        
        # Crear la página
        page = DocumentPage(
            url=url,
            title=title,
            content=content,
            headers=headers,
            code_blocks=code_blocks
        )
        
        # Dividir en chunks
        page.chunks = self._chunk_content(content, title, url)
        
        return page
    
    def _chunk_content(self, content: str, title: str, url: str, chunk_size: int = 1000) -> List[DocumentChunk]:
        """
        Divide el contenido en chunks para RAG.
        
        Args:
            content: Contenido a dividir
            title: Título de la página
            url: URL de la página
            chunk_size: Tamaño aproximado de cada chunk
            
        Returns:
            Lista de chunks de documento
        """
        chunks = []
        
        # Dividir por encabezados principales (# y ##)
        sections = re.split(r'(?=^#{1,2}\s)', content, flags=re.MULTILINE)
        
        for section in sections:
            if not section.strip():
                continue
                
            # Si la sección es muy grande, dividirla en párrafos
            if len(section) > chunk_size:
                paragraphs = re.split(r'\n\n+', section)
                current_chunk = ""
                
                for paragraph in paragraphs:
                    if len(current_chunk) + len(paragraph) > chunk_size and current_chunk:
                        # Guardar el chunk actual y empezar uno nuevo
                        chunks.append(DocumentChunk(
                            content=current_chunk,
                            metadata={
                                "title": title,
                                "url": url,
                                "section": self._extract_section_title(section)
                            }
                        ))
                        current_chunk = paragraph
                    else:
                        if current_chunk:
                            current_chunk += "\n\n" + paragraph
                        else:
                            current_chunk = paragraph
                
                # Guardar el último chunk si existe
                if current_chunk:
                    chunks.append(DocumentChunk(
                        content=current_chunk,
                        metadata={
                            "title": title,
                            "url": url,
                            "section": self._extract_section_title(section)
                        }
                    ))
            else:
                # La sección es lo suficientemente pequeña para ser un chunk
                chunks.append(DocumentChunk(
                    content=section,
                    metadata={
                        "title": title,
                        "url": url,
                        "section": self._extract_section_title(section)
                    }
                ))
        
        return chunks
    
    def _extract_section_title(self, section: str) -> str:
        """Extrae el título de una sección."""
        match = re.match(r'^(#{1,6})\s+(.+)$', section, re.MULTILINE)
        if match:
            return match.group(2).strip()
        return "Sin título"
    
    async def extract_structured_data(self, url: str, schema: Dict[str, Any], instruction: str) -> Any:
        """
        Extrae datos estructurados de una página usando LLM.
        
        Args:
            url: URL a extraer
            schema: Esquema JSON para la extracción
            instruction: Instrucción para el LLM
            
        Returns:
            Datos estructurados extraídos
        """
        if not self.llm_provider or not self.llm_api_key:
            print("[bold yellow]Advertencia: No se ha configurado un proveedor LLM. No se puede realizar extracción estructurada.[/bold yellow]")
            return None
        
        crawler_config = CrawlerRunConfig(
            cache_mode=CacheMode.ENABLED,
            word_count_threshold=1,
            page_timeout=60000,
            extraction_strategy=LLMExtractionStrategy(
                provider=self.llm_provider,
                api_token=self.llm_api_key,
                schema=schema,
                extraction_type="schema",
                instruction=instruction,
                extra_args={"temperature": 0, "top_p": 0.9, "max_tokens": 2000},
            ),
        )
        
        async with AsyncWebCrawler(config=self.browser_config) as crawler:
            result = await crawler.arun(url=url, config=crawler_config)
            return result.extracted_content if result else None
    
    def save_knowledge_base(self, pages: List[DocumentPage], filename: str = "knowledge_base.json"):
        """Guarda la base de conocimiento en un archivo JSON."""
        output_path = os.path.join(self.output_dir, filename)
        
        # Convertir a diccionario para JSON
        data = [page.model_dump() for page in pages]
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"[bold green]Base de conocimiento guardada en: {output_path}[/bold green]")
        
        # Mostrar estadísticas
        total_chunks = sum(len(page.chunks) for page in pages)
        self.console.print(f"\n[bold]Estadísticas de la base de conocimiento:[/bold]")
        
        table = Table(show_header=True)
        table.add_column("Métrica", style="cyan")
        table.add_column("Valor", style="green")
        
        table.add_row("Páginas procesadas", str(len(pages)))
        table.add_row("Chunks totales", str(total_chunks))
        table.add_row("Páginas con error", str(sum(1 for page in pages if not page.success)))
        
        self.console.print(table)

async def main():
    import argparse
    
    # Configurar argumentos de línea de comando
    parser = argparse.ArgumentParser(description='Extraer documentación web para RAG')
    parser.add_argument('--urls', nargs='+', help='URLs de documentación a extraer')
    parser.add_argument('--urls-file', help='Archivo con lista de URLs (una por línea)')
    parser.add_argument('--output', default='knowledge_base.json', help='Nombre del archivo de salida')
    parser.add_argument('--cache-dir', default='./.cache', help='Directorio de caché')
    parser.add_argument('--output-dir', default='./knowledge_base', help='Directorio de salida')
    parser.add_argument('--llm-provider', help='Proveedor de LLM (ej: openai/gpt-4)')
    parser.add_argument('--llm-api-key', help='API key para el LLM')
    parser.add_argument('--max-urls', type=int, default=10, help='Número máximo de URLs a procesar')
    
    args = parser.parse_args()
    
    # URLs por defecto si no se especifican
    default_urls = [
        "https://www.builderbot.app/en",
        "https://www.builderbot.app/en/contribute",
        "https://www.builderbot.app/en/quickstart",
        "https://www.builderbot.app/en/concepts",
        "https://www.builderbot.app/en/uses-cases",
        "https://www.builderbot.app/en/add-functions",
        "https://www.builderbot.app/en/context",
        "https://www.builderbot.app/en/methods",
        "https://www.builderbot.app/en/events",
        "https://www.builderbot.app/en/databases",
        "https://www.builderbot.app/en/providers/meta",
        "https://www.builderbot.app/en/providers/twilio",
        "https://www.builderbot.app/en/providers/baileys",
        "https://www.builderbot.app/en/providers",
        "https://www.builderbot.app/en/deploy",
        "https://www.builderbot.app/en/deploy/railway",
        "https://www.builderbot.app/en/deploy/docker",
        "https://www.builderbot.app/en/deploy/vps",
        "https://www.builderbot.app/en/showcases/queue-limit",
        "https://www.builderbot.app/en/showcases/modularize",
        "https://www.builderbot.app/en/showcases/fast-deploy",
        "https://www.builderbot.app/en/showcases/idle-optional",
        "https://www.builderbot.app/en/showcases/docker-pm2",
        "https://www.builderbot.app/en/showcases/agent-messages",
        "https://www.builderbot.app/en/showcases/cron-reminder",
        "https://www.builderbot.app/en/showcases/forward-conversation-to-human",
        "https://www.builderbot.app/en/showcases/gotoflow-use",
        "https://www.builderbot.app/en/showcases/multiple-messages",
        "https://www.builderbot.app/en/tutorials/migrate-to-builderbot",
        "https://www.builderbot.app/en/tutorials/api-use",
        "https://www.builderbot.app/en/tutorials/chatbot-with-gemini",
        "https://www.builderbot.app/en/tutorials/langchain",
        "https://www.builderbot.app/en/contribute/core",
        "https://www.builderbot.app/en/resources",
        "https://www.builderbot.app/en/plugins/telegram",
        "https://www.builderbot.app/en/plugins/shopify",
        "https://www.builderbot.app/en/plugins/agents",
        "https://www.builderbot.app/en/plugins/langchain",
        "https://www.builderbot.app/en/plugins",
        "https://www.builderbot.app/en/databases/postgres/uses-cases",
        "https://www.builderbot.app/en/deploy/meta",
        "https://www.builderbot.app/en/providers/meta/uses-cases",
        "https://www.builderbot.app/en/providers/twilio/deploy",
        "https://www.builderbot.app/en/providers/twilio/uses-cases",
        "https://www.builderbot.app/en/providers/twilio/delete-bot-message",
        "https://www.builderbot.app/en/providers/baileys/blocked-users",
        "https://www.builderbot.app/en/providers/baileys/fetchStatus",
        "https://www.builderbot.app/en/databases/postgres/uses-cases/uses-cases-supabase"

    ]
    
    # Obtener URLs del archivo si se especifica
    if args.urls_file and os.path.exists(args.urls_file):
        with open(args.urls_file, 'r') as f:
            file_urls = [line.strip() for line in f if line.strip()]
        # Limitar el número de URLs si es necesario
        urls = file_urls[:args.max_urls]
    else:
        # Usar las URLs de la línea de comandos o las predeterminadas
        urls = args.urls if args.urls else default_urls
    
    # Crear el agente
    agent = DocsKnowledgeAgent(
        cache_dir=args.cache_dir,
        output_dir=args.output_dir,
        llm_provider=args.llm_provider,
        llm_api_key=args.llm_api_key
    )
    
    print(f"[bold green]Extrayendo documentación de {len(urls)} URLs...[/bold green]")
    for url in urls:
        print(f"[cyan]- {url}[/cyan]")
    
    # Extraer documentación
    results = await agent.crawl_documentation(urls)
    
    # Guardar base de conocimiento
    agent.save_knowledge_base(results, args.output)
    
    # Ejemplo de extracción estructurada con LLM (requiere configuración de LLM)
    if agent.llm_provider and agent.llm_api_key and len(urls) > 0:
        print("\n[bold]Realizando extracción estructurada con LLM...[/bold]")
        schema = {
            "title": "Documentation Section",
            "type": "object",
            "properties": {
                "section_title": {"type": "string"},
                "key_concepts": {"type": "array", "items": {"type": "string"}},
                "code_examples": {"type": "array", "items": {"type": "string"}},
                "summary": {"type": "string"}
            }
        }
        
        structured_data = await agent.extract_structured_data(
            url=urls[0],
            schema=schema,
            instruction="Extract the main sections, key concepts, code examples, and a brief summary from this documentation."
        )
        
        print("\n[bold]Datos estructurados extraídos:[/bold]")
        print(json.dumps(structured_data, indent=2))

if __name__ == "__main__":
    asyncio.run(main())