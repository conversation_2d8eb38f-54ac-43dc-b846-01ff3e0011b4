[{"url": "https://docs.python.org/3/tutorial/datastructures.html", "title": "5. Data Structures — Python 3.13.2 documentation", "content": "[ ![Python logo](https://docs.python.org/3/_static/py.svg) ](https://docs.python.org/3/tutorial/<https:/www.python.org/>) dev (3.14)***********.*************.***********.***********.72.6\nEnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\nTheme  Auto Light Dark\n### [Table of Contents](https://docs.python.org/3/tutorial/<../contents.html>)\n  * [5. Data Structures](https://docs.python.org/3/tutorial/<#>)\n    * [5.1. More on Lists](https://docs.python.org/3/tutorial/<#more-on-lists>)\n      * [5.1.1. Using Lists as Stacks](https://docs.python.org/3/tutorial/<#using-lists-as-stacks>)\n      * [5.1.2. Using Lists as Queues](https://docs.python.org/3/tutorial/<#using-lists-as-queues>)\n      * [5.1.3. List Comprehensions](https://docs.python.org/3/tutorial/<#list-comprehensions>)\n      * [5.1.4. Nested List Comprehensions](https://docs.python.org/3/tutorial/<#nested-list-comprehensions>)\n    * [5.2. The `del` statement](https://docs.python.org/3/tutorial/<#the-del-statement>)\n    * [5.3. Tuples and Sequences](https://docs.python.org/3/tutorial/<#tuples-and-sequences>)\n    * [5.4. Sets](https://docs.python.org/3/tutorial/<#sets>)\n    * [5.5. Dictionaries](https://docs.python.org/3/tutorial/<#dictionaries>)\n    * [5.6. Looping Techniques](https://docs.python.org/3/tutorial/<#looping-techniques>)\n    * [5.7. More on Conditions](https://docs.python.org/3/tutorial/<#more-on-conditions>)\n    * [5.8. Comparing Sequences and Other Types](https://docs.python.org/3/tutorial/<#comparing-sequences-and-other-types>)\n\n\n#### Previous topic\n[4. More Control Flow Tools](https://docs.python.org/3/tutorial/<controlflow.html> \"previous chapter\")\n#### Next topic\n[6. Modules](https://docs.python.org/3/tutorial/<modules.html> \"next chapter\")\n### This Page\n  * [Report a Bug](https://docs.python.org/3/tutorial/<../bugs.html>)\n  * [Show Source ](https://docs.python.org/3/tutorial/<https:/github.com/python/cpython/blob/main/Doc/tutorial/datastructures.rst>)\n\n\n### Navigation\n  * [index](https://docs.python.org/3/tutorial/<../genindex.html> \"General Index\")\n  * [modules](https://docs.python.org/3/tutorial/<../py-modindex.html> \"Python Module Index\") |\n  * [next](https://docs.python.org/3/tutorial/<modules.html> \"6. Modules\") |\n  * [previous](https://docs.python.org/3/tutorial/<controlflow.html> \"4. More Control Flow Tools\") |\n  * ![Python logo](https://docs.python.org/3/_static/py.svg)\n  * [Python](https://docs.python.org/3/tutorial/<https:/www.python.org/>) »\n  * EnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\ndev (3.14)***********.*************.***********.***********.72.6\n  * [3.13.2 Documentation](https://docs.python.org/3/tutorial/<../index.html>) » \n  * [The Python Tutorial](https://docs.python.org/3/tutorial/<index.html>) »\n  * [5. Data Structures](https://docs.python.org/3/tutorial/<>)\n  * | \n  * Theme  Auto Light Dark |\n\n\n# 5. Data Structures[¶](https://docs.python.org/3/tutorial/<#data-structures> \"Link to this heading\")\nThis chapter describes some things you’ve learned about already in more detail, and adds some new things as well.\n## 5.1. More on Lists[¶](https://docs.python.org/3/tutorial/<#more-on-lists> \"Link to this heading\")\nThe list data type has some more methods. Here are all of the methods of list objects:\nlist.append(_x_)\n    \nAdd an item to the end of the list. Similar to `a[len(a):] = [x]`.\nlist.extend(_iterable_)\n    \nExtend the list by appending all the items from the iterable. Similar to `a[len(a):] = iterable`.\nlist.insert(_i_ , _x_)\n    \nInsert an item at a given position. The first argument is the index of the element before which to insert, so `a.insert(0, x)` inserts at the front of the list, and `a.insert(len(a), x)` is equivalent to `a.append(x)`.\nlist.remove(_x_)\n    \nRemove the first item from the list whose value is equal to _x_. It raises a `[ValueError`](https://docs.python.org/3/tutorial/<../library/exceptions.html#ValueError> \"ValueError\") if there is no such item.\nlist.pop([_i_])\n    \nRemove the item at the given position in the list, and return it. If no index is specified, `a.pop()` removes and returns the last item in the list. It raises an `[IndexError`](https://docs.python.org/3/tutorial/<../library/exceptions.html#IndexError> \"IndexError\") if the list is empty or the index is outside the list range.\nlist.clear()\n    \nRemove all items from the list. Similar to `del a[:]`.\nlist.index(_x_[, _start_[, _end_]])\n    \nReturn zero-based index in the list of the first item whose value is equal to _x_. Raises a `[ValueError`](https://docs.python.org/3/tutorial/<../library/exceptions.html#ValueError> \"ValueError\") if there is no such item.\nThe optional arguments _start_ and _end_ are interpreted as in the slice notation and are used to limit the search to a particular subsequence of the list. The returned index is computed relative to the beginning of the full sequence rather than the _start_ argument.\nlist.count(_x_)\n    \nReturn the number of times _x_ appears in the list.\nlist.sort(_*_ , _key =None_, _reverse =False_)\n    \nSort the items of the list in place (the arguments can be used for sort customization, see `[sorted()`](https://docs.python.org/3/tutorial/<../library/functions.html#sorted> \"sorted\") for their explanation).\nlist.reverse()\n    \nReverse the elements of the list in place.\nlist.copy()\n    \nReturn a shallow copy of the list. Similar to `a[:]`.\nAn example that uses most of the list methods:\n>>>```\n>>> fruits = ['orange', 'apple', 'pear', 'banana', 'kiwi', 'apple', 'banana']\n>>> fruits.count('apple')\n2\n>>> fruits.count('tangerine')\n0\n>>> fruits.index('banana')\n3\n>>> fruits.index('banana', 4) # Find next banana starting at position 4\n6\n>>> fruits.reverse()\n>>> fruits\n['banana', 'apple', 'kiwi', 'banana', 'pear', 'apple', 'orange']\n>>> fruits.append('grape')\n>>> fruits\n['banana', 'apple', 'kiwi', 'banana', 'pear', 'apple', 'orange', 'grape']\n>>> fruits.sort()\n>>> fruits\n['apple', 'apple', 'banana', 'banana', 'grape', 'kiwi', 'orange', 'pear']\n>>> fruits.pop()\n'pear'\n\n```\n\nYou might have noticed that methods like `insert`, `remove` or `sort` that only modify the list have no return value printed – they return the default `None`. [[1]](https://docs.python.org/3/tutorial/<#id2>) This is a design principle for all mutable data structures in Python.\nAnother thing you might notice is that not all data can be sorted or compared. For instance, `[None, 'hello', 10]` doesn’t sort because integers can’t be compared to strings and `None` can’t be compared to other types. Also, there are some types that don’t have a defined ordering relation. For example, `3+4j < 5+7j` isn’t a valid comparison.\n### 5.1.1. Using Lists as Stacks[¶](https://docs.python.org/3/tutorial/<#using-lists-as-stacks> \"Link to this heading\")\nThe list methods make it very easy to use a list as a stack, where the last element added is the first element retrieved (“last-in, first-out”). To add an item to the top of the stack, use `append()`. To retrieve an item from the top of the stack, use `pop()` without an explicit index. For example:\n>>>```\n>>> stack = [3, 4, 5]\n>>> stack.append(6)\n>>> stack.append(7)\n>>> stack\n[3, 4, 5, 6, 7]\n>>> stack.pop()\n7\n>>> stack\n[3, 4, 5, 6]\n>>> stack.pop()\n6\n>>> stack.pop()\n5\n>>> stack\n[3, 4]\n\n```\n\n### 5.1.2. Using Lists as Queues[¶](https://docs.python.org/3/tutorial/<#using-lists-as-queues> \"Link to this heading\")\nIt is also possible to use a list as a queue, where the first element added is the first element retrieved (“first-in, first-out”); however, lists are not efficient for this purpose. While appends and pops from the end of list are fast, doing inserts or pops from the beginning of a list is slow (because all of the other elements have to be shifted by one).\nTo implement a queue, use `[collections.deque`](https://docs.python.org/3/tutorial/<../library/collections.html#collections.deque> \"collections.deque\") which was designed to have fast appends and pops from both ends. For example:\n>>>```\n>>> fromcollectionsimport deque\n>>> queue = deque([\"Eric\", \"John\", \"Michael\"])\n>>> queue.append(\"Terry\")      # Terry arrives\n>>> queue.append(\"Graham\")     # Graham arrives\n>>> queue.popleft()         # The first to arrive now leaves\n'Eric'\n>>> queue.popleft()         # The second to arrive now leaves\n'John'\n>>> queue              # Remaining queue in order of arrival\ndeque(['Michael', 'Terry', 'Graham'])\n\n```\n\n### 5.1.3. List Comprehensions[¶](https://docs.python.org/3/tutorial/<#list-comprehensions> \"Link to this heading\")\nList comprehensions provide a concise way to create lists. Common applications are to make new lists where each element is the result of some operations applied to each member of another sequence or iterable, or to create a subsequence of those elements that satisfy a certain condition.\nFor example, assume we want to create a list of squares, like:\n>>>```\n>>> squares = []\n>>> for x in range(10):\n...   squares.append(x**2)\n...\n>>> squares\n[0, 1, 4, 9, 16, 25, 36, 49, 64, 81]\n\n```\n\nNote that this creates (or overwrites) a variable named `x` that still exists after the loop completes. We can calculate the list of squares without any side effects using:\n```\nsquares = list(map(lambda x: x**2, range(10)))\n\n```\n\nor, equivalently:\n```\nsquares = [x**2 for x in range(10)]\n\n```\n\nwhich is more concise and readable.\nA list comprehension consists of brackets containing an expression followed by a `for` clause, then zero or more `for` or `if` clauses. The result will be a new list resulting from evaluating the expression in the context of the `for` and `if` clauses which follow it. For example, this listcomp combines the elements of two lists if they are not equal:\n>>>```\n>>> [(x, y) for x in [1,2,3] for y in [3,1,4] if x != y]\n[(1, 3), (1, 4), (2, 3), (2, 1), (2, 4), (3, 1), (3, 4)]\n\n```\n\nand it’s equivalent to:\n>>>```\n>>> combs = []\n>>> for x in [1,2,3]:\n...   for y in [3,1,4]:\n...     if x != y:\n...       combs.append((x, y))\n...\n>>> combs\n[(1, 3), (1, 4), (2, 3), (2, 1), (2, 4), (3, 1), (3, 4)]\n\n```\n\nNote how the order of the `[for`](https://docs.python.org/3/tutorial/<../reference/compound_stmts.html#for>) and `[if`](https://docs.python.org/3/tutorial/<../reference/compound_stmts.html#if>) statements is the same in both these snippets.\nIf the expression is a tuple (e.g. the `(x, y)` in the previous example), it must be parenthesized.\n>>>```\n>>> vec = [-4, -2, 0, 2, 4]\n>>> # create a new list with the values doubled\n>>> [x*2 for x in vec]\n[-8, -4, 0, 4, 8]\n>>> # filter the list to exclude negative numbers\n>>> [x for x in vec if x >= 0]\n[0, 2, 4]\n>>> # apply a function to all the elements\n>>> [abs(x) for x in vec]\n[4, 2, 0, 2, 4]\n>>> # call a method on each element\n>>> freshfruit = [' banana', ' loganberry ', 'passion fruit ']\n>>> [weapon.strip() for weapon in freshfruit]\n['banana', 'loganberry', 'passion fruit']\n>>> # create a list of 2-tuples like (number, square)\n>>> [(x, x**2) for x in range(6)]\n[(0, 0), (1, 1), (2, 4), (3, 9), (4, 16), (5, 25)]\n>>> # the tuple must be parenthesized, otherwise an error is raised\n>>> [x, x**2 for x in range(6)]\n File \"<stdin>\", line 1\n[x, x**2 for x in range(6)]\n^^^^^^^\nSyntaxError: did you forget parentheses around the comprehension target?\n>>> # flatten a list using a listcomp with two 'for'\n>>> vec = [[1,2,3], [4,5,6], [7,8,9]]\n>>> [num for elem in vec for num in elem]\n[1, 2, 3, 4, 5, 6, 7, 8, 9]\n\n```\n\nList comprehensions can contain complex expressions and nested functions:\n>>>```\n>>> frommathimport pi\n>>> [str(round(pi, i)) for i in range(1, 6)]\n['3.1', '3.14', '3.142', '3.1416', '3.14159']\n\n```\n\n### 5.1.4. Nested List Comprehensions[¶](https://docs.python.org/3/tutorial/<#nested-list-comprehensions> \"Link to this heading\")\nThe initial expression in a list comprehension can be any arbitrary expression, including another list comprehension.\nConsider the following example of a 3x4 matrix implemented as a list of 3 lists of length 4:\n>>>```\n>>> matrix = [\n...   [1, 2, 3, 4],\n...   [5, 6, 7, 8],\n...   [9, 10, 11, 12],\n... ]\n\n```\n\nThe following list comprehension will transpose rows and columns:\n>>>```\n>>> [[row[i] for row in matrix] for i in range(4)]\n[[1, 5, 9], [2, 6, 10], [3, 7, 11], [4, 8, 12]]\n\n```\n\nAs we saw in the previous section, the inner list comprehension is evaluated in the context of the `[for`](https://docs.python.org/3/tutorial/<../reference/compound_stmts.html#for>) that follows it, so this example is equivalent to:\n>>>```\n>>> transposed = []\n>>> for i in range(4):\n...   transposed.append([row[i] for row in matrix])\n...\n>>> transposed\n[[1, 5, 9], [2, 6, 10], [3, 7, 11], [4, 8, 12]]\n\n```\n\nwhich, in turn, is the same as:\n>>>```\n>>> transposed = []\n>>> for i in range(4):\n...   # the following 3 lines implement the nested listcomp\n...   transposed_row = []\n...   for row in matrix:\n...     transposed_row.append(row[i])\n...   transposed.append(transposed_row)\n...\n>>> transposed\n[[1, 5, 9], [2, 6, 10], [3, 7, 11], [4, 8, 12]]\n\n```\n\nIn the real world, you should prefer built-in functions to complex flow statements. The `[zip()`](https://docs.python.org/3/tutorial/<../library/functions.html#zip> \"zip\") function would do a great job for this use case:\n>>>```\n>>> list(zip(*matrix))\n[(1, 5, 9), (2, 6, 10), (3, 7, 11), (4, 8, 12)]\n\n```\n\nSee [Unpacking Argument Lists](https://docs.python.org/3/tutorial/<controlflow.html#tut-unpacking-arguments>) for details on the asterisk in this line.\n## 5.2. The `del` statement[¶](https://docs.python.org/3/tutorial/<#the-del-statement> \"Link to this heading\")\nThere is a way to remove an item from a list given its index instead of its value: the `[del`](https://docs.python.org/3/tutorial/<../reference/simple_stmts.html#del>) statement. This differs from the `pop()` method which returns a value. The `del` statement can also be used to remove slices from a list or clear the entire list (which we did earlier by assignment of an empty list to the slice). For example:\n>>>```\n>>> a = [-1, 1, 66.25, 333, 333, 1234.5]\n>>> del a[0]\n>>> a\n[1, 66.25, 333, 333, 1234.5]\n>>> del a[2:4]\n>>> a\n[1, 66.25, 1234.5]\n>>> del a[:]\n>>> a\n[]\n\n```\n\n`[del`](https://docs.python.org/3/tutorial/<../reference/simple_stmts.html#del>) can also be used to delete entire variables:\n>>>```\n>>> del a\n\n```\n\nReferencing the name `a` hereafter is an error (at least until another value is assigned to it). We’ll find other uses for `[del`](https://docs.python.org/3/tutorial/<../reference/simple_stmts.html#del>) later.\n## 5.3. Tuples and Sequences[¶](https://docs.python.org/3/tutorial/<#tuples-and-sequences> \"Link to this heading\")\nWe saw that lists and strings have many common properties, such as indexing and slicing operations. They are two examples of _sequence_ data types (see [Sequence Types — list, tuple, range](https://docs.python.org/3/tutorial/<../library/stdtypes.html#typesseq>)). Since Python is an evolving language, other sequence data types may be added. There is also another standard sequence data type: the _tuple_.\nA tuple consists of a number of values separated by commas, for instance:\n>>>```\n>>> t = 12345, 54321, 'hello!'\n>>> t[0]\n12345\n>>> t\n(12345, 54321, 'hello!')\n>>> # Tuples may be nested:\n>>> u = t, (1, 2, 3, 4, 5)\n>>> u\n((12345, 54321, 'hello!'), (1, 2, 3, 4, 5))\n>>> # Tuples are immutable:\n>>> t[0] = 88888\nTraceback (most recent call last):\n File \"<stdin>\", line 1, in <module>\nTypeError: 'tuple' object does not support item assignment\n>>> # but they can contain mutable objects:\n>>> v = ([1, 2, 3], [3, 2, 1])\n>>> v\n([1, 2, 3], [3, 2, 1])\n\n```\n\nAs you see, on output tuples are always enclosed in parentheses, so that nested tuples are interpreted correctly; they may be input with or without surrounding parentheses, although often parentheses are necessary anyway (if the tuple is part of a larger expression). It is not possible to assign to the individual items of a tuple, however it is possible to create tuples which contain mutable objects, such as lists.\nThough tuples may seem similar to lists, they are often used in different situations and for different purposes. Tuples are [immutable](https://docs.python.org/3/tutorial/<../glossary.html#term-immutable>), and usually contain a heterogeneous sequence of elements that are accessed via unpacking (see later in this section) or indexing (or even by attribute in the case of `[namedtuples`](https://docs.python.org/3/tutorial/<../library/collections.html#collections.namedtuple> \"collections.namedtuple\")). Lists are [mutable](https://docs.python.org/3/tutorial/<../glossary.html#term-mutable>), and their elements are usually homogeneous and are accessed by iterating over the list.\nA special problem is the construction of tuples containing 0 or 1 items: the syntax has some extra quirks to accommodate these. Empty tuples are constructed by an empty pair of parentheses; a tuple with one item is constructed by following a value with a comma (it is not sufficient to enclose a single value in parentheses). Ugly, but effective. For example:\n>>>```\n>>> empty = ()\n>>> singleton = 'hello',  # <-- note trailing comma\n>>> len(empty)\n0\n>>> len(singleton)\n1\n>>> singleton\n('hello',)\n\n```\n\nThe statement `t = 12345, 54321, 'hello!'` is an example of _tuple packing_ : the values `12345`, `54321` and `'hello!'` are packed together in a tuple. The reverse operation is also possible:\n>>>```\n>>> x, y, z = t\n\n```\n\nThis is called, appropriately enough, _sequence unpacking_ and works for any sequence on the right-hand side. Sequence unpacking requires that there are as many variables on the left side of the equals sign as there are elements in the sequence. Note that multiple assignment is really just a combination of tuple packing and sequence unpacking.\n## 5.4. Sets[¶](https://docs.python.org/3/tutorial/<#sets> \"Link to this heading\")\nPython also includes a data type for _sets_. A set is an unordered collection with no duplicate elements. Basic uses include membership testing and eliminating duplicate entries. Set objects also support mathematical operations like union, intersection, difference, and symmetric difference.\nCurly braces or the `[set()`](https://docs.python.org/3/tutorial/<../library/stdtypes.html#set> \"set\") function can be used to create sets. Note: to create an empty set you have to use `set()`, not `{}`; the latter creates an empty dictionary, a data structure that we discuss in the next section.\nHere is a brief demonstration:\n>>>```\n>>> basket = {'apple', 'orange', 'apple', 'pear', 'orange', 'banana'}\n>>> print(basket)           # show that duplicates have been removed\n{'orange', 'banana', 'pear', 'apple'}\n>>> 'orange' in basket         # fast membership testing\nTrue\n>>> 'crabgrass' in basket\nFalse\n>>> # Demonstrate set operations on unique letters from two words\n>>>\n>>> a = set('abracadabra')\n>>> b = set('alacazam')\n>>> a                 # unique letters in a\n{'a', 'r', 'b', 'c', 'd'}\n>>> a - b               # letters in a but not in b\n{'r', 'd', 'b'}\n>>> a | b               # letters in a or b or both\n{'a', 'c', 'r', 'd', 'b', 'm', 'z', 'l'}\n>>> a & b               # letters in both a and b\n{'a', 'c'}\n>>> a ^ b               # letters in a or b but not both\n{'r', 'd', 'b', 'm', 'z', 'l'}\n\n```\n\nSimilarly to [list comprehensions](https://docs.python.org/3/tutorial/<#tut-listcomps>), set comprehensions are also supported:\n>>>```\n>>> a = {x for x in 'abracadabra' if x not in 'abc'}\n>>> a\n{'r', 'd'}\n\n```\n\n## 5.5. Dictionaries[¶](https://docs.python.org/3/tutorial/<#dictionaries> \"Link to this heading\")\nAnother useful data type built into Python is the _dictionary_ (see [Mapping Types — dict](https://docs.python.org/3/tutorial/<../library/stdtypes.html#typesmapping>)). Dictionaries are sometimes found in other languages as “associative memories” or “associative arrays”. Unlike sequences, which are indexed by a range of numbers, dictionaries are indexed by _keys_ , which can be any immutable type; strings and numbers can always be keys. Tuples can be used as keys if they contain only strings, numbers, or tuples; if a tuple contains any mutable object either directly or indirectly, it cannot be used as a key. You can’t use lists as keys, since lists can be modified in place using index assignments, slice assignments, or methods like `append()` and `extend()`.\nIt is best to think of a dictionary as a set of _key: value_ pairs, with the requirement that the keys are unique (within one dictionary). A pair of braces creates an empty dictionary: `{}`. Placing a comma-separated list of key:value pairs within the braces adds initial key:value pairs to the dictionary; this is also the way dictionaries are written on output.\nThe main operations on a dictionary are storing a value with some key and extracting the value given the key. It is also possible to delete a key:value pair with `del`. If you store using a key that is already in use, the old value associated with that key is forgotten. It is an error to extract a value using a non-existent key.\nPerforming `list(d)` on a dictionary returns a list of all the keys used in the dictionary, in insertion order (if you want it sorted, just use `sorted(d)` instead). To check whether a single key is in the dictionary, use the `[in`](https://docs.python.org/3/tutorial/<../reference/expressions.html#in>) keyword.\nHere is a small example using a dictionary:\n>>>```\n>>> tel = {'jack': 4098, 'sape': 4139}\n>>> tel['guido'] = 4127\n>>> tel\n{'jack': 4098, 'sape': 4139, 'guido': 4127}\n>>> tel['jack']\n4098\n>>> del tel['sape']\n>>> tel['irv'] = 4127\n>>> tel\n{'jack': 4098, 'guido': 4127, 'irv': 4127}\n>>> list(tel)\n['jack', 'guido', 'irv']\n>>> sorted(tel)\n['guido', 'irv', 'jack']\n>>> 'guido' in tel\nTrue\n>>> 'jack' not in tel\nFalse\n\n```\n\nThe `[dict()`](https://docs.python.org/3/tutorial/<../library/stdtypes.html#dict> \"dict\") constructor builds dictionaries directly from sequences of key-value pairs:\n>>>```\n>>> dict([('sape', 4139), ('guido', 4127), ('jack', 4098)])\n{'sape': 4139, 'guido': 4127, 'jack': 4098}\n\n```\n\nIn addition, dict comprehensions can be used to create dictionaries from arbitrary key and value expressions:\n>>>```\n>>> {x: x**2 for x in (2, 4, 6)}\n{2: 4, 4: 16, 6: 36}\n\n```\n\nWhen the keys are simple strings, it is sometimes easier to specify pairs using keyword arguments:\n>>>```\n>>> dict(sape=4139, guido=4127, jack=4098)\n{'sape': 4139, 'guido': 4127, 'jack': 4098}\n\n```\n\n## 5.6. Looping Techniques[¶](https://docs.python.org/3/tutorial/<#looping-techniques> \"Link to this heading\")\nWhen looping through dictionaries, the key and corresponding value can be retrieved at the same time using the `[items()`](https://docs.python.org/3/tutorial/<../library/stdtypes.html#dict.items> \"dict.items\") method.\n>>>```\n>>> knights = {'gallahad': 'the pure', 'robin': 'the brave'}\n>>> for k, v in knights.items():\n...   print(k, v)\n...\ngallahad the pure\nrobin the brave\n\n```\n\nWhen looping through a sequence, the position index and corresponding value can be retrieved at the same time using the `[enumerate()`](https://docs.python.org/3/tutorial/<../library/functions.html#enumerate> \"enumerate\") function.\n>>>```\n>>> for i, v in enumerate(['tic', 'tac', 'toe']):\n...   print(i, v)\n...\n0 tic\n1 tac\n2 toe\n\n```\n\nTo loop over two or more sequences at the same time, the entries can be paired with the `[zip()`](https://docs.python.org/3/tutorial/<../library/functions.html#zip> \"zip\") function.\n>>>```\n>>> questions = ['name', 'quest', 'favorite color']\n>>> answers = ['lancelot', 'the holy grail', 'blue']\n>>> for q, a in zip(questions, answers):\n...   print('What is your {0}? It is {1}.'.format(q, a))\n...\nWhat is your name? It is lancelot.\nWhat is your quest? It is the holy grail.\nWhat is your favorite color? It is blue.\n\n```\n\nTo loop over a sequence in reverse, first specify the sequence in a forward direction and then call the `[reversed()`](https://docs.python.org/3/tutorial/<../library/functions.html#reversed> \"reversed\") function.\n>>>```\n>>> for i in reversed(range(1, 10, 2)):\n...   print(i)\n...\n9\n7\n5\n3\n1\n\n```\n\nTo loop over a sequence in sorted order, use the `[sorted()`](https://docs.python.org/3/tutorial/<../library/functions.html#sorted> \"sorted\") function which returns a new sorted list while leaving the source unaltered.\n>>>```\n>>> basket = ['apple', 'orange', 'apple', 'pear', 'orange', 'banana']\n>>> for i in sorted(basket):\n...   print(i)\n...\napple\napple\nbanana\norange\norange\npear\n\n```\n\nUsing `[set()`](https://docs.python.org/3/tutorial/<../library/stdtypes.html#set> \"set\") on a sequence eliminates duplicate elements. The use of `[sorted()`](https://docs.python.org/3/tutorial/<../library/functions.html#sorted> \"sorted\") in combination with `[set()`](https://docs.python.org/3/tutorial/<../library/stdtypes.html#set> \"set\") over a sequence is an idiomatic way to loop over unique elements of the sequence in sorted order.\n>>>```\n>>> basket = ['apple', 'orange', 'apple', 'pear', 'orange', 'banana']\n>>> for f in sorted(set(basket)):\n...   print(f)\n...\napple\nbanana\norange\npear\n\n```\n\nIt is sometimes tempting to change a list while you are looping over it; however, it is often simpler and safer to create a new list instead.\n>>>```\n>>> importmath\n>>> raw_data = [56.2, float('NaN'), 51.7, 55.3, 52.5, float('NaN'), 47.8]\n>>> filtered_data = []\n>>> for value in raw_data:\n...   if not math.isnan(value):\n...     filtered_data.append(value)\n...\n>>> filtered_data\n[56.2, 51.7, 55.3, 52.5, 47.8]\n\n```\n\n## 5.7. More on Conditions[¶](https://docs.python.org/3/tutorial/<#more-on-conditions> \"Link to this heading\")\nThe conditions used in `while` and `if` statements can contain any operators, not just comparisons.\nThe comparison operators `in` and `not in` are membership tests that determine whether a value is in (or not in) a container. The operators `is` and `is not` compare whether two objects are really the same object. All comparison operators have the same priority, which is lower than that of all numerical operators.\nComparisons can be chained. For example, `a < b == c` tests whether `a` is less than `b` and moreover `b` equals `c`.\nComparisons may be combined using the Boolean operators `and` and `or`, and the outcome of a comparison (or of any other Boolean expression) may be negated with `not`. These have lower priorities than comparison operators; between them, `not` has the highest priority and `or` the lowest, so that `A and not B or C` is equivalent to `(A and (not B)) or C`. As always, parentheses can be used to express the desired composition.\nThe Boolean operators `and` and `or` are so-called _short-circuit_ operators: their arguments are evaluated from left to right, and evaluation stops as soon as the outcome is determined. For example, if `A` and `C` are true but `B` is false, `A and B and C` does not evaluate the expression `C`. When used as a general value and not as a Boolean, the return value of a short-circuit operator is the last evaluated argument.\nIt is possible to assign the result of a comparison or other Boolean expression to a variable. For example,\n>>>```\n>>> string1, string2, string3 = '', 'Trondheim', 'Hammer Dance'\n>>> non_null = string1 or string2 or string3\n>>> non_null\n'Trondheim'\n\n```\n\nNote that in Python, unlike C, assignment inside expressions must be done explicitly with the [walrus operator](https://docs.python.org/3/tutorial/<../faq/design.html#why-can-t-i-use-an-assignment-in-an-expression>) `:=`. This avoids a common class of problems encountered in C programs: typing `=` in an expression when `==` was intended.\n## 5.8. Comparing Sequences and Other Types[¶](https://docs.python.org/3/tutorial/<#comparing-sequences-and-other-types> \"Link to this heading\")\nSequence objects typically may be compared to other objects with the same sequence type. The comparison uses _lexicographical_ ordering: first the first two items are compared, and if they differ this determines the outcome of the comparison; if they are equal, the next two items are compared, and so on, until either sequence is exhausted. If two items to be compared are themselves sequences of the same type, the lexicographical comparison is carried out recursively. If all items of two sequences compare equal, the sequences are considered equal. If one sequence is an initial sub-sequence of the other, the shorter sequence is the smaller (lesser) one. Lexicographical ordering for strings uses the Unicode code point number to order individual characters. Some examples of comparisons between sequences of the same type:\n```\n(1, 2, 3)       < (1, 2, 4)\n[1, 2, 3]       < [1, 2, 4]\n'ABC' < 'C' < 'Pascal' < 'Python'\n(1, 2, 3, 4)      < (1, 2, 4)\n(1, 2)         < (1, 2, -1)\n(1, 2, 3)       == (1.0, 2.0, 3.0)\n(1, 2, ('aa', 'ab'))  < (1, 2, ('abc', 'a'), 4)\n\n```\n\nNote that comparing objects of different types with `<` or `>` is legal provided that the objects have appropriate comparison methods. For example, mixed numeric types are compared according to their numeric value, so 0 equals 0.0, etc. Otherwise, rather than providing an arbitrary ordering, the interpreter will raise a `[TypeError`](https://docs.python.org/3/tutorial/<../library/exceptions.html#TypeError> \"TypeError\") exception.\nFootnotes\n[[1](https://docs.python.org/3/tutorial/<#id1>)]\nOther languages may return the mutated object, which allows method chaining, such as `d->insert(\"a\")->remove(\"b\")->sort();`.\n### [Table of Contents](https://docs.python.org/3/tutorial/<../contents.html>)\n  * [5. Data Structures](https://docs.python.org/3/tutorial/<#>)\n    * [5.1. More on Lists](https://docs.python.org/3/tutorial/<#more-on-lists>)\n      * [5.1.1. Using Lists as Stacks](https://docs.python.org/3/tutorial/<#using-lists-as-stacks>)\n      * [5.1.2. Using Lists as Queues](https://docs.python.org/3/tutorial/<#using-lists-as-queues>)\n      * [5.1.3. List Comprehensions](https://docs.python.org/3/tutorial/<#list-comprehensions>)\n      * [5.1.4. Nested List Comprehensions](https://docs.python.org/3/tutorial/<#nested-list-comprehensions>)\n    * [5.2. The `del` statement](https://docs.python.org/3/tutorial/<#the-del-statement>)\n    * [5.3. Tuples and Sequences](https://docs.python.org/3/tutorial/<#tuples-and-sequences>)\n    * [5.4. Sets](https://docs.python.org/3/tutorial/<#sets>)\n    * [5.5. Dictionaries](https://docs.python.org/3/tutorial/<#dictionaries>)\n    * [5.6. Looping Techniques](https://docs.python.org/3/tutorial/<#looping-techniques>)\n    * [5.7. More on Conditions](https://docs.python.org/3/tutorial/<#more-on-conditions>)\n    * [5.8. Comparing Sequences and Other Types](https://docs.python.org/3/tutorial/<#comparing-sequences-and-other-types>)\n\n\n#### Previous topic\n[4. More Control Flow Tools](https://docs.python.org/3/tutorial/<controlflow.html> \"previous chapter\")\n#### Next topic\n[6. Modules](https://docs.python.org/3/tutorial/<modules.html> \"next chapter\")\n### This Page\n  * [Report a Bug](https://docs.python.org/3/tutorial/<../bugs.html>)\n  * [Show Source ](https://docs.python.org/3/tutorial/<https:/github.com/python/cpython/blob/main/Doc/tutorial/datastructures.rst>)\n\n\n«\n### Navigation\n  * [index](https://docs.python.org/3/tutorial/<../genindex.html> \"General Index\")\n  * [modules](https://docs.python.org/3/tutorial/<../py-modindex.html> \"Python Module Index\") |\n  * [next](https://docs.python.org/3/tutorial/<modules.html> \"6. Modules\") |\n  * [previous](https://docs.python.org/3/tutorial/<controlflow.html> \"4. More Control Flow Tools\") |\n  * ![Python logo](https://docs.python.org/3/_static/py.svg)\n  * [Python](https://docs.python.org/3/tutorial/<https:/www.python.org/>) »\n  * EnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\ndev (3.14)***********.*************.***********.***********.72.6\n  * [3.13.2 Documentation](https://docs.python.org/3/tutorial/<../index.html>) » \n  * [The Python Tutorial](https://docs.python.org/3/tutorial/<index.html>) »\n  * [5. Data Structures](https://docs.python.org/3/tutorial/<>)\n  * | \n  * Theme  Auto Light Dark |\n\n\n© [ Copyright ](https://docs.python.org/3/tutorial/<../copyright.html>) 2001-2025, Python Software Foundation. This page is licensed under the Python Software Foundation License Version 2. Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License. See [History and License](https://docs.python.org/3/tutorial/</license.html>) for more information. The Python Software Foundation is a non-profit corporation. [Please donate.](https://docs.python.org/3/tutorial/<https:/www.python.org/psf/donations/>) Last updated on Mar 02, 2025 (11:16 UTC). [Found a bug](https://docs.python.org/3/tutorial/</bugs.html>)? Created using [Sphinx](https://docs.python.org/3/tutorial/<https:/www.sphinx-doc.org/>) 8.2.1. \n  *[*]: Keyword-only parameters separator (PEP 3102)\n", "chunks": [{"content": "[ ![Python logo](https://docs.python.org/3/_static/py.svg) ](https://docs.python.org/3/tutorial/<https:/www.python.org/>) dev (3.14)***********.*************.***********.***********.72.6\nEnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\nTheme  Auto Light Dark\n### [Table of Contents](https://docs.python.org/3/tutorial/<../contents.html>)\n  * [5. Data Structures](https://docs.python.org/3/tutorial/<#>)\n    * [5.1. More on Lists](https://docs.python.org/3/tutorial/<#more-on-lists>)\n      * [5.1.1. Using Lists as Stacks](https://docs.python.org/3/tutorial/<#using-lists-as-stacks>)\n      * [5.1.2. Using Lists as Queues](https://docs.python.org/3/tutorial/<#using-lists-as-queues>)\n      * [5.1.3. List Comprehensions](https://docs.python.org/3/tutorial/<#list-comprehensions>)\n      * [5.1.4. Nested List Comprehensions](https://docs.python.org/3/tutorial/<#nested-list-comprehensions>)\n    * [5.2. The `del` statement](https://docs.python.org/3/tutorial/<#the-del-statement>)\n    * [5.3. Tuples and Sequences](https://docs.python.org/3/tutorial/<#tuples-and-sequences>)\n    * [5.4. Sets](https://docs.python.org/3/tutorial/<#sets>)\n    * [5.5. Dictionaries](https://docs.python.org/3/tutorial/<#dictionaries>)\n    * [5.6. Looping Techniques](https://docs.python.org/3/tutorial/<#looping-techniques>)\n    * [5.7. More on Conditions](https://docs.python.org/3/tutorial/<#more-on-conditions>)\n    * [5.8. Comparing Sequences and Other Types](https://docs.python.org/3/tutorial/<#comparing-sequences-and-other-types>)", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "Sin título"}, "embedding": null}, {"content": "#### Previous topic\n[4. More Control Flow Tools](https://docs.python.org/3/tutorial/<controlflow.html> \"previous chapter\")\n#### Next topic\n[6. Modules](https://docs.python.org/3/tutorial/<modules.html> \"next chapter\")\n### This Page\n  * [Report a Bug](https://docs.python.org/3/tutorial/<../bugs.html>)\n  * [Show Source ](https://docs.python.org/3/tutorial/<https:/github.com/python/cpython/blob/main/Doc/tutorial/datastructures.rst>)", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "Sin título"}, "embedding": null}, {"content": "### Navigation\n  * [index](https://docs.python.org/3/tutorial/<../genindex.html> \"General Index\")\n  * [modules](https://docs.python.org/3/tutorial/<../py-modindex.html> \"Python Module Index\") |\n  * [next](https://docs.python.org/3/tutorial/<modules.html> \"6. Modules\") |\n  * [previous](https://docs.python.org/3/tutorial/<controlflow.html> \"4. More Control Flow Tools\") |\n  * ![Python logo](https://docs.python.org/3/_static/py.svg)\n  * [Python](https://docs.python.org/3/tutorial/<https:/www.python.org/>) »\n  * EnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\ndev (3.14)***********.*************.***********.***********.72.6\n  * [3.13.2 Documentation](https://docs.python.org/3/tutorial/<../index.html>) » \n  * [The Python Tutorial](https://docs.python.org/3/tutorial/<index.html>) »\n  * [5. Data Structures](https://docs.python.org/3/tutorial/<>)\n  * | \n  * Theme  Auto Light Dark |", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "Sin título"}, "embedding": null}, {"content": "# 5. Data Structures[¶](https://docs.python.org/3/tutorial/<#data-structures> \"Link to this heading\")\nThis chapter describes some things you’ve learned about already in more detail, and adds some new things as well.\n", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5. Data Structures[¶](https://docs.python.org/3/tutorial/<#data-structures> \"Link to this heading\")"}, "embedding": null}, {"content": "## 5.1. More on Lists[¶](https://docs.python.org/3/tutorial/<#more-on-lists> \"Link to this heading\")\nThe list data type has some more methods. Here are all of the methods of list objects:\nlist.append(_x_)\n    \nAdd an item to the end of the list. Similar to `a[len(a):] = [x]`.\nlist.extend(_iterable_)\n    \nExtend the list by appending all the items from the iterable. Similar to `a[len(a):] = iterable`.\nlist.insert(_i_ , _x_)\n    \nInsert an item at a given position. The first argument is the index of the element before which to insert, so `a.insert(0, x)` inserts at the front of the list, and `a.insert(len(a), x)` is equivalent to `a.append(x)`.\nlist.remove(_x_)\n    \nRemove the first item from the list whose value is equal to _x_. It raises a `[ValueError`](https://docs.python.org/3/tutorial/<../library/exceptions.html#ValueError> \"ValueError\") if there is no such item.\nlist.pop([_i_])\n    \nRemove the item at the given position in the list, and return it. If no index is specified, `a.pop()` removes and returns the last item in the list. It raises an `[IndexError`](https://docs.python.org/3/tutorial/<../library/exceptions.html#IndexError> \"IndexError\") if the list is empty or the index is outside the list range.\nlist.clear()\n    \nRemove all items from the list. Similar to `del a[:]`.\nlist.index(_x_[, _start_[, _end_]])\n    \nReturn zero-based index in the list of the first item whose value is equal to _x_. Raises a `[ValueError`](https://docs.python.org/3/tutorial/<../library/exceptions.html#ValueError> \"ValueError\") if there is no such item.\nThe optional arguments _start_ and _end_ are interpreted as in the slice notation and are used to limit the search to a particular subsequence of the list. The returned index is computed relative to the beginning of the full sequence rather than the _start_ argument.\nlist.count(_x_)\n    \nReturn the number of times _x_ appears in the list.\nlist.sort(_*_ , _key =None_, _reverse =False_)\n    \nSort the items of the list in place (the arguments can be used for sort customization, see `[sorted()`](https://docs.python.org/3/tutorial/<../library/functions.html#sorted> \"sorted\") for their explanation).\nlist.reverse()\n    \nReverse the elements of the list in place.\nlist.copy()\n    \nReturn a shallow copy of the list. Similar to `a[:]`.\nAn example that uses most of the list methods:\n>>>```\n>>> fruits = ['orange', 'apple', 'pear', 'banana', 'kiwi', 'apple', 'banana']\n>>> fruits.count('apple')\n2\n>>> fruits.count('tangerine')\n0\n>>> fruits.index('banana')\n3\n>>> fruits.index('banana', 4) # Find next banana starting at position 4\n6\n>>> fruits.reverse()\n>>> fruits\n['banana', 'apple', 'kiwi', 'banana', 'pear', 'apple', 'orange']\n>>> fruits.append('grape')\n>>> fruits\n['banana', 'apple', 'kiwi', 'banana', 'pear', 'apple', 'orange', 'grape']\n>>> fruits.sort()\n>>> fruits\n['apple', 'apple', 'banana', 'banana', 'grape', 'kiwi', 'orange', 'pear']\n>>> fruits.pop()\n'pear'", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.1. More on Lists[¶](https://docs.python.org/3/tutorial/<#more-on-lists> \"Link to this heading\")"}, "embedding": null}, {"content": "```", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.1. More on Lists[¶](https://docs.python.org/3/tutorial/<#more-on-lists> \"Link to this heading\")"}, "embedding": null}, {"content": "You might have noticed that methods like `insert`, `remove` or `sort` that only modify the list have no return value printed – they return the default `None`. [[1]](https://docs.python.org/3/tutorial/<#id2>) This is a design principle for all mutable data structures in Python.\nAnother thing you might notice is that not all data can be sorted or compared. For instance, `[None, 'hello', 10]` doesn’t sort because integers can’t be compared to strings and `None` can’t be compared to other types. Also, there are some types that don’t have a defined ordering relation. For example, `3+4j < 5+7j` isn’t a valid comparison.\n### 5.1.1. Using Lists as Stacks[¶](https://docs.python.org/3/tutorial/<#using-lists-as-stacks> \"Link to this heading\")\nThe list methods make it very easy to use a list as a stack, where the last element added is the first element retrieved (“last-in, first-out”). To add an item to the top of the stack, use `append()`. To retrieve an item from the top of the stack, use `pop()` without an explicit index. For example:\n>>>```\n>>> stack = [3, 4, 5]\n>>> stack.append(6)\n>>> stack.append(7)\n>>> stack\n[3, 4, 5, 6, 7]\n>>> stack.pop()\n7\n>>> stack\n[3, 4, 5, 6]\n>>> stack.pop()\n6\n>>> stack.pop()\n5\n>>> stack\n[3, 4]", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.1. More on Lists[¶](https://docs.python.org/3/tutorial/<#more-on-lists> \"Link to this heading\")"}, "embedding": null}, {"content": "```", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.1. More on Lists[¶](https://docs.python.org/3/tutorial/<#more-on-lists> \"Link to this heading\")"}, "embedding": null}, {"content": "### 5.1.2. Using Lists as Queues[¶](https://docs.python.org/3/tutorial/<#using-lists-as-queues> \"Link to this heading\")\nIt is also possible to use a list as a queue, where the first element added is the first element retrieved (“first-in, first-out”); however, lists are not efficient for this purpose. While appends and pops from the end of list are fast, doing inserts or pops from the beginning of a list is slow (because all of the other elements have to be shifted by one).\nTo implement a queue, use `[collections.deque`](https://docs.python.org/3/tutorial/<../library/collections.html#collections.deque> \"collections.deque\") which was designed to have fast appends and pops from both ends. For example:\n>>>```\n>>> fromcollectionsimport deque\n>>> queue = deque([\"<PERSON>\", \"<PERSON>\", \"<PERSON>\"])\n>>> queue.append(\"<PERSON>\")      # <PERSON> arrives\n>>> queue.append(\"<PERSON>\")     # <PERSON> arrives\n>>> queue.popleft()         # The first to arrive now leaves\n'<PERSON>'\n>>> queue.popleft()         # The second to arrive now leaves\n'<PERSON>'\n>>> queue              # Remaining queue in order of arrival\ndeque(['<PERSON>', '<PERSON>', '<PERSON>'])", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.1. More on Lists[¶](https://docs.python.org/3/tutorial/<#more-on-lists> \"Link to this heading\")"}, "embedding": null}, {"content": "```\n\n### 5.1.3. List Comprehensions[¶](https://docs.python.org/3/tutorial/<#list-comprehensions> \"Link to this heading\")\nList comprehensions provide a concise way to create lists. Common applications are to make new lists where each element is the result of some operations applied to each member of another sequence or iterable, or to create a subsequence of those elements that satisfy a certain condition.\nFor example, assume we want to create a list of squares, like:\n>>>```\n>>> squares = []\n>>> for x in range(10):\n...   squares.append(x**2)\n...\n>>> squares\n[0, 1, 4, 9, 16, 25, 36, 49, 64, 81]\n\n```\n\nNote that this creates (or overwrites) a variable named `x` that still exists after the loop completes. We can calculate the list of squares without any side effects using:\n```\nsquares = list(map(lambda x: x**2, range(10)))\n\n```\n\nor, equivalently:\n```\nsquares = [x**2 for x in range(10)]\n\n```", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.1. More on Lists[¶](https://docs.python.org/3/tutorial/<#more-on-lists> \"Link to this heading\")"}, "embedding": null}, {"content": "which is more concise and readable.\nA list comprehension consists of brackets containing an expression followed by a `for` clause, then zero or more `for` or `if` clauses. The result will be a new list resulting from evaluating the expression in the context of the `for` and `if` clauses which follow it. For example, this listcomp combines the elements of two lists if they are not equal:\n>>>```\n>>> [(x, y) for x in [1,2,3] for y in [3,1,4] if x != y]\n[(1, 3), (1, 4), (2, 3), (2, 1), (2, 4), (3, 1), (3, 4)]\n\n```\n\nand it’s equivalent to:\n>>>```\n>>> combs = []\n>>> for x in [1,2,3]:\n...   for y in [3,1,4]:\n...     if x != y:\n...       combs.append((x, y))\n...\n>>> combs\n[(1, 3), (1, 4), (2, 3), (2, 1), (2, 4), (3, 1), (3, 4)]\n\n```", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.1. More on Lists[¶](https://docs.python.org/3/tutorial/<#more-on-lists> \"Link to this heading\")"}, "embedding": null}, {"content": "Note how the order of the `[for`](https://docs.python.org/3/tutorial/<../reference/compound_stmts.html#for>) and `[if`](https://docs.python.org/3/tutorial/<../reference/compound_stmts.html#if>) statements is the same in both these snippets.\nIf the expression is a tuple (e.g. the `(x, y)` in the previous example), it must be parenthesized.\n>>>```\n>>> vec = [-4, -2, 0, 2, 4]\n>>> # create a new list with the values doubled\n>>> [x*2 for x in vec]\n[-8, -4, 0, 4, 8]\n>>> # filter the list to exclude negative numbers\n>>> [x for x in vec if x >= 0]\n[0, 2, 4]\n>>> # apply a function to all the elements\n>>> [abs(x) for x in vec]\n[4, 2, 0, 2, 4]\n>>> # call a method on each element\n>>> freshfruit = [' banana', ' loganberry ', 'passion fruit ']\n>>> [weapon.strip() for weapon in freshfruit]\n['banana', 'loganberry', 'passion fruit']\n>>> # create a list of 2-tuples like (number, square)\n>>> [(x, x**2) for x in range(6)]\n[(0, 0), (1, 1), (2, 4), (3, 9), (4, 16), (5, 25)]\n>>> # the tuple must be parenthesized, otherwise an error is raised\n>>> [x, x**2 for x in range(6)]\n File \"<stdin>\", line 1\n[x, x**2 for x in range(6)]\n^^^^^^^\nSyntaxError: did you forget parentheses around the comprehension target?\n>>> # flatten a list using a listcomp with two 'for'\n>>> vec = [[1,2,3], [4,5,6], [7,8,9]]\n>>> [num for elem in vec for num in elem]\n[1, 2, 3, 4, 5, 6, 7, 8, 9]", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.1. More on Lists[¶](https://docs.python.org/3/tutorial/<#more-on-lists> \"Link to this heading\")"}, "embedding": null}, {"content": "```\n\nList comprehensions can contain complex expressions and nested functions:\n>>>```\n>>> frommathimport pi\n>>> [str(round(pi, i)) for i in range(1, 6)]\n['3.1', '3.14', '3.142', '3.1416', '3.14159']\n\n```\n\n### 5.1.4. Nested List Comprehensions[¶](https://docs.python.org/3/tutorial/<#nested-list-comprehensions> \"Link to this heading\")\nThe initial expression in a list comprehension can be any arbitrary expression, including another list comprehension.\nConsider the following example of a 3x4 matrix implemented as a list of 3 lists of length 4:\n>>>```\n>>> matrix = [\n...   [1, 2, 3, 4],\n...   [5, 6, 7, 8],\n...   [9, 10, 11, 12],\n... ]\n\n```\n\nThe following list comprehension will transpose rows and columns:\n>>>```\n>>> [[row[i] for row in matrix] for i in range(4)]\n[[1, 5, 9], [2, 6, 10], [3, 7, 11], [4, 8, 12]]\n\n```", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.1. More on Lists[¶](https://docs.python.org/3/tutorial/<#more-on-lists> \"Link to this heading\")"}, "embedding": null}, {"content": "As we saw in the previous section, the inner list comprehension is evaluated in the context of the `[for`](https://docs.python.org/3/tutorial/<../reference/compound_stmts.html#for>) that follows it, so this example is equivalent to:\n>>>```\n>>> transposed = []\n>>> for i in range(4):\n...   transposed.append([row[i] for row in matrix])\n...\n>>> transposed\n[[1, 5, 9], [2, 6, 10], [3, 7, 11], [4, 8, 12]]\n\n```\n\nwhich, in turn, is the same as:\n>>>```\n>>> transposed = []\n>>> for i in range(4):\n...   # the following 3 lines implement the nested listcomp\n...   transposed_row = []\n...   for row in matrix:\n...     transposed_row.append(row[i])\n...   transposed.append(transposed_row)\n...\n>>> transposed\n[[1, 5, 9], [2, 6, 10], [3, 7, 11], [4, 8, 12]]\n\n```", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.1. More on Lists[¶](https://docs.python.org/3/tutorial/<#more-on-lists> \"Link to this heading\")"}, "embedding": null}, {"content": "In the real world, you should prefer built-in functions to complex flow statements. The `[zip()`](https://docs.python.org/3/tutorial/<../library/functions.html#zip> \"zip\") function would do a great job for this use case:\n>>>```\n>>> list(zip(*matrix))\n[(1, 5, 9), (2, 6, 10), (3, 7, 11), (4, 8, 12)]\n\n```\n\nSee [Unpacking Argument Lists](https://docs.python.org/3/tutorial/<controlflow.html#tut-unpacking-arguments>) for details on the asterisk in this line.\n", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.1. More on Lists[¶](https://docs.python.org/3/tutorial/<#more-on-lists> \"Link to this heading\")"}, "embedding": null}, {"content": "## 5.2. The `del` statement[¶](https://docs.python.org/3/tutorial/<#the-del-statement> \"Link to this heading\")\nThere is a way to remove an item from a list given its index instead of its value: the `[del`](https://docs.python.org/3/tutorial/<../reference/simple_stmts.html#del>) statement. This differs from the `pop()` method which returns a value. The `del` statement can also be used to remove slices from a list or clear the entire list (which we did earlier by assignment of an empty list to the slice). For example:\n>>>```\n>>> a = [-1, 1, 66.25, 333, 333, 1234.5]\n>>> del a[0]\n>>> a\n[1, 66.25, 333, 333, 1234.5]\n>>> del a[2:4]\n>>> a\n[1, 66.25, 1234.5]\n>>> del a[:]\n>>> a\n[]\n\n```\n\n`[del`](https://docs.python.org/3/tutorial/<../reference/simple_stmts.html#del>) can also be used to delete entire variables:\n>>>```\n>>> del a\n\n```", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.2. The `del` statement[¶](https://docs.python.org/3/tutorial/<#the-del-statement> \"Link to this heading\")"}, "embedding": null}, {"content": "Referencing the name `a` hereafter is an error (at least until another value is assigned to it). We’ll find other uses for `[del`](https://docs.python.org/3/tutorial/<../reference/simple_stmts.html#del>) later.\n", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.2. The `del` statement[¶](https://docs.python.org/3/tutorial/<#the-del-statement> \"Link to this heading\")"}, "embedding": null}, {"content": "## 5.3. Tuples and Sequences[¶](https://docs.python.org/3/tutorial/<#tuples-and-sequences> \"Link to this heading\")\nWe saw that lists and strings have many common properties, such as indexing and slicing operations. They are two examples of _sequence_ data types (see [Sequence Types — list, tuple, range](https://docs.python.org/3/tutorial/<../library/stdtypes.html#typesseq>)). Since Python is an evolving language, other sequence data types may be added. There is also another standard sequence data type: the _tuple_.\nA tuple consists of a number of values separated by commas, for instance:\n>>>```\n>>> t = 12345, 54321, 'hello!'\n>>> t[0]\n12345\n>>> t\n(12345, 54321, 'hello!')\n>>> # Tuples may be nested:\n>>> u = t, (1, 2, 3, 4, 5)\n>>> u\n((12345, 54321, 'hello!'), (1, 2, 3, 4, 5))\n>>> # Tuples are immutable:\n>>> t[0] = 88888\nTraceback (most recent call last):\n File \"<stdin>\", line 1, in <module>\nTypeError: 'tuple' object does not support item assignment\n>>> # but they can contain mutable objects:\n>>> v = ([1, 2, 3], [3, 2, 1])\n>>> v\n([1, 2, 3], [3, 2, 1])", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.3. Tuples and Sequences[¶](https://docs.python.org/3/tutorial/<#tuples-and-sequences> \"Link to this heading\")"}, "embedding": null}, {"content": "```", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.3. Tuples and Sequences[¶](https://docs.python.org/3/tutorial/<#tuples-and-sequences> \"Link to this heading\")"}, "embedding": null}, {"content": "As you see, on output tuples are always enclosed in parentheses, so that nested tuples are interpreted correctly; they may be input with or without surrounding parentheses, although often parentheses are necessary anyway (if the tuple is part of a larger expression). It is not possible to assign to the individual items of a tuple, however it is possible to create tuples which contain mutable objects, such as lists.\nThough tuples may seem similar to lists, they are often used in different situations and for different purposes. Tuples are [immutable](https://docs.python.org/3/tutorial/<../glossary.html#term-immutable>), and usually contain a heterogeneous sequence of elements that are accessed via unpacking (see later in this section) or indexing (or even by attribute in the case of `[namedtuples`](https://docs.python.org/3/tutorial/<../library/collections.html#collections.namedtuple> \"collections.namedtuple\")). Lists are [mutable](https://docs.python.org/3/tutorial/<../glossary.html#term-mutable>), and their elements are usually homogeneous and are accessed by iterating over the list.\nA special problem is the construction of tuples containing 0 or 1 items: the syntax has some extra quirks to accommodate these. Empty tuples are constructed by an empty pair of parentheses; a tuple with one item is constructed by following a value with a comma (it is not sufficient to enclose a single value in parentheses). Ugly, but effective. For example:\n>>>```\n>>> empty = ()\n>>> singleton = 'hello',  # <-- note trailing comma\n>>> len(empty)\n0\n>>> len(singleton)\n1\n>>> singleton\n('hello',)", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.3. Tuples and Sequences[¶](https://docs.python.org/3/tutorial/<#tuples-and-sequences> \"Link to this heading\")"}, "embedding": null}, {"content": "```\n\nThe statement `t = 12345, 54321, 'hello!'` is an example of _tuple packing_ : the values `12345`, `54321` and `'hello!'` are packed together in a tuple. The reverse operation is also possible:\n>>>```\n>>> x, y, z = t\n\n```\n\nThis is called, appropriately enough, _sequence unpacking_ and works for any sequence on the right-hand side. Sequence unpacking requires that there are as many variables on the left side of the equals sign as there are elements in the sequence. Note that multiple assignment is really just a combination of tuple packing and sequence unpacking.\n", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.3. Tuples and Sequences[¶](https://docs.python.org/3/tutorial/<#tuples-and-sequences> \"Link to this heading\")"}, "embedding": null}, {"content": "## 5.4. Sets[¶](https://docs.python.org/3/tutorial/<#sets> \"Link to this heading\")\nPython also includes a data type for _sets_. A set is an unordered collection with no duplicate elements. Basic uses include membership testing and eliminating duplicate entries. Set objects also support mathematical operations like union, intersection, difference, and symmetric difference.\nCurly braces or the `[set()`](https://docs.python.org/3/tutorial/<../library/stdtypes.html#set> \"set\") function can be used to create sets. Note: to create an empty set you have to use `set()`, not `{}`; the latter creates an empty dictionary, a data structure that we discuss in the next section.\nHere is a brief demonstration:\n>>>```\n>>> basket = {'apple', 'orange', 'apple', 'pear', 'orange', 'banana'}\n>>> print(basket)           # show that duplicates have been removed\n{'orange', 'banana', 'pear', 'apple'}\n>>> 'orange' in basket         # fast membership testing\nTrue\n>>> 'crabgrass' in basket\nFalse\n>>> # Demonstrate set operations on unique letters from two words\n>>>\n>>> a = set('abracadabra')\n>>> b = set('alacazam')\n>>> a                 # unique letters in a\n{'a', 'r', 'b', 'c', 'd'}\n>>> a - b               # letters in a but not in b\n{'r', 'd', 'b'}\n>>> a | b               # letters in a or b or both\n{'a', 'c', 'r', 'd', 'b', 'm', 'z', 'l'}\n>>> a & b               # letters in both a and b\n{'a', 'c'}\n>>> a ^ b               # letters in a or b but not both\n{'r', 'd', 'b', 'm', 'z', 'l'}", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.4. Sets[¶](https://docs.python.org/3/tutorial/<#sets> \"Link to this heading\")"}, "embedding": null}, {"content": "```\n\nSimilarly to [list comprehensions](https://docs.python.org/3/tutorial/<#tut-listcomps>), set comprehensions are also supported:\n>>>```\n>>> a = {x for x in 'abracadabra' if x not in 'abc'}\n>>> a\n{'r', 'd'}\n\n```\n\n", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.4. Sets[¶](https://docs.python.org/3/tutorial/<#sets> \"Link to this heading\")"}, "embedding": null}, {"content": "## 5.5. Dictionaries[¶](https://docs.python.org/3/tutorial/<#dictionaries> \"Link to this heading\")\nAnother useful data type built into Python is the _dictionary_ (see [Mapping Types — dict](https://docs.python.org/3/tutorial/<../library/stdtypes.html#typesmapping>)). Dictionaries are sometimes found in other languages as “associative memories” or “associative arrays”. Unlike sequences, which are indexed by a range of numbers, dictionaries are indexed by _keys_ , which can be any immutable type; strings and numbers can always be keys. Tuples can be used as keys if they contain only strings, numbers, or tuples; if a tuple contains any mutable object either directly or indirectly, it cannot be used as a key. You can’t use lists as keys, since lists can be modified in place using index assignments, slice assignments, or methods like `append()` and `extend()`.\nIt is best to think of a dictionary as a set of _key: value_ pairs, with the requirement that the keys are unique (within one dictionary). A pair of braces creates an empty dictionary: `{}`. Placing a comma-separated list of key:value pairs within the braces adds initial key:value pairs to the dictionary; this is also the way dictionaries are written on output.\nThe main operations on a dictionary are storing a value with some key and extracting the value given the key. It is also possible to delete a key:value pair with `del`. If you store using a key that is already in use, the old value associated with that key is forgotten. It is an error to extract a value using a non-existent key.\nPerforming `list(d)` on a dictionary returns a list of all the keys used in the dictionary, in insertion order (if you want it sorted, just use `sorted(d)` instead). To check whether a single key is in the dictionary, use the `[in`](https://docs.python.org/3/tutorial/<../reference/expressions.html#in>) keyword.\nHere is a small example using a dictionary:\n>>>```\n>>> tel = {'jack': 4098, 'sape': 4139}\n>>> tel['guido'] = 4127\n>>> tel\n{'jack': 4098, 'sape': 4139, 'guido': 4127}\n>>> tel['jack']\n4098\n>>> del tel['sape']\n>>> tel['irv'] = 4127\n>>> tel\n{'jack': 4098, 'guido': 4127, 'irv': 4127}\n>>> list(tel)\n['jack', 'guido', 'irv']\n>>> sorted(tel)\n['guido', 'irv', 'jack']\n>>> 'guido' in tel\nTrue\n>>> 'jack' not in tel\nFalse", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.5. Dictionaries[¶](https://docs.python.org/3/tutorial/<#dictionaries> \"Link to this heading\")"}, "embedding": null}, {"content": "```\n\nThe `[dict()`](https://docs.python.org/3/tutorial/<../library/stdtypes.html#dict> \"dict\") constructor builds dictionaries directly from sequences of key-value pairs:\n>>>```\n>>> dict([('sape', 4139), ('guido', 4127), ('jack', 4098)])\n{'sape': 4139, 'guido': 4127, 'jack': 4098}\n\n```\n\nIn addition, dict comprehensions can be used to create dictionaries from arbitrary key and value expressions:\n>>>```\n>>> {x: x**2 for x in (2, 4, 6)}\n{2: 4, 4: 16, 6: 36}\n\n```\n\nWhen the keys are simple strings, it is sometimes easier to specify pairs using keyword arguments:\n>>>```\n>>> dict(sape=4139, guido=4127, jack=4098)\n{'sape': 4139, 'guido': 4127, 'jack': 4098}\n\n```\n\n", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.5. Dictionaries[¶](https://docs.python.org/3/tutorial/<#dictionaries> \"Link to this heading\")"}, "embedding": null}, {"content": "## 5.6. Looping Techniques[¶](https://docs.python.org/3/tutorial/<#looping-techniques> \"Link to this heading\")\nWhen looping through dictionaries, the key and corresponding value can be retrieved at the same time using the `[items()`](https://docs.python.org/3/tutorial/<../library/stdtypes.html#dict.items> \"dict.items\") method.\n>>>```\n>>> knights = {'gallahad': 'the pure', 'robin': 'the brave'}\n>>> for k, v in knights.items():\n...   print(k, v)\n...\ngallahad the pure\nrobin the brave\n\n```\n\nWhen looping through a sequence, the position index and corresponding value can be retrieved at the same time using the `[enumerate()`](https://docs.python.org/3/tutorial/<../library/functions.html#enumerate> \"enumerate\") function.\n>>>```\n>>> for i, v in enumerate(['tic', 'tac', 'toe']):\n...   print(i, v)\n...\n0 tic\n1 tac\n2 toe\n\n```", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.6. Looping Techniques[¶](https://docs.python.org/3/tutorial/<#looping-techniques> \"Link to this heading\")"}, "embedding": null}, {"content": "To loop over two or more sequences at the same time, the entries can be paired with the `[zip()`](https://docs.python.org/3/tutorial/<../library/functions.html#zip> \"zip\") function.\n>>>```\n>>> questions = ['name', 'quest', 'favorite color']\n>>> answers = ['lancelot', 'the holy grail', 'blue']\n>>> for q, a in zip(questions, answers):\n...   print('What is your {0}? It is {1}.'.format(q, a))\n...\nWhat is your name? It is lancelot.\nWhat is your quest? It is the holy grail.\nWhat is your favorite color? It is blue.\n\n```\n\nTo loop over a sequence in reverse, first specify the sequence in a forward direction and then call the `[reversed()`](https://docs.python.org/3/tutorial/<../library/functions.html#reversed> \"reversed\") function.\n>>>```\n>>> for i in reversed(range(1, 10, 2)):\n...   print(i)\n...\n9\n7\n5\n3\n1\n\n```", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.6. Looping Techniques[¶](https://docs.python.org/3/tutorial/<#looping-techniques> \"Link to this heading\")"}, "embedding": null}, {"content": "To loop over a sequence in sorted order, use the `[sorted()`](https://docs.python.org/3/tutorial/<../library/functions.html#sorted> \"sorted\") function which returns a new sorted list while leaving the source unaltered.\n>>>```\n>>> basket = ['apple', 'orange', 'apple', 'pear', 'orange', 'banana']\n>>> for i in sorted(basket):\n...   print(i)\n...\napple\napple\nbanana\norange\norange\npear\n\n```\n\nUsing `[set()`](https://docs.python.org/3/tutorial/<../library/stdtypes.html#set> \"set\") on a sequence eliminates duplicate elements. The use of `[sorted()`](https://docs.python.org/3/tutorial/<../library/functions.html#sorted> \"sorted\") in combination with `[set()`](https://docs.python.org/3/tutorial/<../library/stdtypes.html#set> \"set\") over a sequence is an idiomatic way to loop over unique elements of the sequence in sorted order.\n>>>```\n>>> basket = ['apple', 'orange', 'apple', 'pear', 'orange', 'banana']\n>>> for f in sorted(set(basket)):\n...   print(f)\n...\napple\nbanana\norange\npear\n\n```", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.6. Looping Techniques[¶](https://docs.python.org/3/tutorial/<#looping-techniques> \"Link to this heading\")"}, "embedding": null}, {"content": "It is sometimes tempting to change a list while you are looping over it; however, it is often simpler and safer to create a new list instead.\n>>>```\n>>> importmath\n>>> raw_data = [56.2, float('NaN'), 51.7, 55.3, 52.5, float('NaN'), 47.8]\n>>> filtered_data = []\n>>> for value in raw_data:\n...   if not math.isnan(value):\n...     filtered_data.append(value)\n...\n>>> filtered_data\n[56.2, 51.7, 55.3, 52.5, 47.8]\n\n```\n\n", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.6. Looping Techniques[¶](https://docs.python.org/3/tutorial/<#looping-techniques> \"Link to this heading\")"}, "embedding": null}, {"content": "## 5.7. More on Conditions[¶](https://docs.python.org/3/tutorial/<#more-on-conditions> \"Link to this heading\")\nThe conditions used in `while` and `if` statements can contain any operators, not just comparisons.\nThe comparison operators `in` and `not in` are membership tests that determine whether a value is in (or not in) a container. The operators `is` and `is not` compare whether two objects are really the same object. All comparison operators have the same priority, which is lower than that of all numerical operators.\nComparisons can be chained. For example, `a < b == c` tests whether `a` is less than `b` and moreover `b` equals `c`.\nComparisons may be combined using the Boolean operators `and` and `or`, and the outcome of a comparison (or of any other Boolean expression) may be negated with `not`. These have lower priorities than comparison operators; between them, `not` has the highest priority and `or` the lowest, so that `A and not B or C` is equivalent to `(A and (not B)) or C`. As always, parentheses can be used to express the desired composition.\nThe Boolean operators `and` and `or` are so-called _short-circuit_ operators: their arguments are evaluated from left to right, and evaluation stops as soon as the outcome is determined. For example, if `A` and `C` are true but `B` is false, `A and B and C` does not evaluate the expression `C`. When used as a general value and not as a Boolean, the return value of a short-circuit operator is the last evaluated argument.\nIt is possible to assign the result of a comparison or other Boolean expression to a variable. For example,\n>>>```\n>>> string1, string2, string3 = '', 'Trondheim', 'Hammer Dance'\n>>> non_null = string1 or string2 or string3\n>>> non_null\n'Trondheim'", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.7. More on Conditions[¶](https://docs.python.org/3/tutorial/<#more-on-conditions> \"Link to this heading\")"}, "embedding": null}, {"content": "```\n\nNote that in Python, unlike C, assignment inside expressions must be done explicitly with the [walrus operator](https://docs.python.org/3/tutorial/<../faq/design.html#why-can-t-i-use-an-assignment-in-an-expression>) `:=`. This avoids a common class of problems encountered in C programs: typing `=` in an expression when `==` was intended.\n", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.7. More on Conditions[¶](https://docs.python.org/3/tutorial/<#more-on-conditions> \"Link to this heading\")"}, "embedding": null}, {"content": "## 5.8. Comparing Sequences and Other Types[¶](https://docs.python.org/3/tutorial/<#comparing-sequences-and-other-types> \"Link to this heading\")\nSequence objects typically may be compared to other objects with the same sequence type. The comparison uses _lexicographical_ ordering: first the first two items are compared, and if they differ this determines the outcome of the comparison; if they are equal, the next two items are compared, and so on, until either sequence is exhausted. If two items to be compared are themselves sequences of the same type, the lexicographical comparison is carried out recursively. If all items of two sequences compare equal, the sequences are considered equal. If one sequence is an initial sub-sequence of the other, the shorter sequence is the smaller (lesser) one. Lexicographical ordering for strings uses the Unicode code point number to order individual characters. Some examples of comparisons between sequences of the same type:\n```\n(1, 2, 3)       < (1, 2, 4)\n[1, 2, 3]       < [1, 2, 4]\n'ABC' < 'C' < 'Pascal' < 'Python'\n(1, 2, 3, 4)      < (1, 2, 4)\n(1, 2)         < (1, 2, -1)\n(1, 2, 3)       == (1.0, 2.0, 3.0)\n(1, 2, ('aa', 'ab'))  < (1, 2, ('abc', 'a'), 4)", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.8. Comparing Sequences and Other Types[¶](https://docs.python.org/3/tutorial/<#comparing-sequences-and-other-types> \"Link to this heading\")"}, "embedding": null}, {"content": "```", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.8. Comparing Sequences and Other Types[¶](https://docs.python.org/3/tutorial/<#comparing-sequences-and-other-types> \"Link to this heading\")"}, "embedding": null}, {"content": "Note that comparing objects of different types with `<` or `>` is legal provided that the objects have appropriate comparison methods. For example, mixed numeric types are compared according to their numeric value, so 0 equals 0.0, etc. Otherwise, rather than providing an arbitrary ordering, the interpreter will raise a `[TypeError`](https://docs.python.org/3/tutorial/<../library/exceptions.html#TypeError> \"TypeError\") exception.\nFootnotes\n[[1](https://docs.python.org/3/tutorial/<#id1>)]\nOther languages may return the mutated object, which allows method chaining, such as `d->insert(\"a\")->remove(\"b\")->sort();`.\n### [Table of Contents](https://docs.python.org/3/tutorial/<../contents.html>)\n  * [5. Data Structures](https://docs.python.org/3/tutorial/<#>)\n    * [5.1. More on Lists](https://docs.python.org/3/tutorial/<#more-on-lists>)\n      * [5.1.1. Using Lists as Stacks](https://docs.python.org/3/tutorial/<#using-lists-as-stacks>)\n      * [5.1.2. Using Lists as Queues](https://docs.python.org/3/tutorial/<#using-lists-as-queues>)\n      * [5.1.3. List Comprehensions](https://docs.python.org/3/tutorial/<#list-comprehensions>)\n      * [5.1.4. Nested List Comprehensions](https://docs.python.org/3/tutorial/<#nested-list-comprehensions>)\n    * [5.2. The `del` statement](https://docs.python.org/3/tutorial/<#the-del-statement>)\n    * [5.3. Tuples and Sequences](https://docs.python.org/3/tutorial/<#tuples-and-sequences>)\n    * [5.4. Sets](https://docs.python.org/3/tutorial/<#sets>)\n    * [5.5. Dictionaries](https://docs.python.org/3/tutorial/<#dictionaries>)\n    * [5.6. Looping Techniques](https://docs.python.org/3/tutorial/<#looping-techniques>)\n    * [5.7. More on Conditions](https://docs.python.org/3/tutorial/<#more-on-conditions>)\n    * [5.8. Comparing Sequences and Other Types](https://docs.python.org/3/tutorial/<#comparing-sequences-and-other-types>)", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.8. Comparing Sequences and Other Types[¶](https://docs.python.org/3/tutorial/<#comparing-sequences-and-other-types> \"Link to this heading\")"}, "embedding": null}, {"content": "#### Previous topic\n[4. More Control Flow Tools](https://docs.python.org/3/tutorial/<controlflow.html> \"previous chapter\")\n#### Next topic\n[6. Modules](https://docs.python.org/3/tutorial/<modules.html> \"next chapter\")\n### This Page\n  * [Report a Bug](https://docs.python.org/3/tutorial/<../bugs.html>)\n  * [Show Source ](https://docs.python.org/3/tutorial/<https:/github.com/python/cpython/blob/main/Doc/tutorial/datastructures.rst>)", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.8. Comparing Sequences and Other Types[¶](https://docs.python.org/3/tutorial/<#comparing-sequences-and-other-types> \"Link to this heading\")"}, "embedding": null}, {"content": "«\n### Navigation\n  * [index](https://docs.python.org/3/tutorial/<../genindex.html> \"General Index\")\n  * [modules](https://docs.python.org/3/tutorial/<../py-modindex.html> \"Python Module Index\") |\n  * [next](https://docs.python.org/3/tutorial/<modules.html> \"6. Modules\") |\n  * [previous](https://docs.python.org/3/tutorial/<controlflow.html> \"4. More Control Flow Tools\") |\n  * ![Python logo](https://docs.python.org/3/_static/py.svg)\n  * [Python](https://docs.python.org/3/tutorial/<https:/www.python.org/>) »\n  * EnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\ndev (3.14)***********.*************.***********.***********.72.6\n  * [3.13.2 Documentation](https://docs.python.org/3/tutorial/<../index.html>) » \n  * [The Python Tutorial](https://docs.python.org/3/tutorial/<index.html>) »\n  * [5. Data Structures](https://docs.python.org/3/tutorial/<>)\n  * | \n  * Theme  Auto Light Dark |", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.8. Comparing Sequences and Other Types[¶](https://docs.python.org/3/tutorial/<#comparing-sequences-and-other-types> \"Link to this heading\")"}, "embedding": null}, {"content": "© [ Copyright ](https://docs.python.org/3/tutorial/<../copyright.html>) 2001-2025, Python Software Foundation. This page is licensed under the Python Software Foundation License Version 2. Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License. See [History and License](https://docs.python.org/3/tutorial/</license.html>) for more information. The Python Software Foundation is a non-profit corporation. [Please donate.](https://docs.python.org/3/tutorial/<https:/www.python.org/psf/donations/>) Last updated on Mar 02, 2025 (11:16 UTC). [Found a bug](https://docs.python.org/3/tutorial/</bugs.html>)? Created using [Sphinx](https://docs.python.org/3/tutorial/<https:/www.sphinx-doc.org/>) 8.2.1. \n  *[*]: Keyword-only parameters separator (PEP 3102)\n", "metadata": {"title": "5. Data Structures — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/datastructures.html", "section": "5.8. Comparing Sequences and Other Types[¶](https://docs.python.org/3/tutorial/<#comparing-sequences-and-other-types> \"Link to this heading\")"}, "embedding": null}], "headers": [{"text": "Table of Contents", "level": 3, "id": ""}, {"text": "Previous topic", "level": 4, "id": ""}, {"text": "Next topic", "level": 4, "id": ""}, {"text": "This Page", "level": 3, "id": ""}, {"text": "Navigation", "level": 3, "id": ""}, {"text": "5.Data Structures¶", "level": 1, "id": ""}, {"text": "5.1.More on Lists¶", "level": 2, "id": ""}, {"text": "5.1.1.Using Lists as Stacks¶", "level": 3, "id": ""}, {"text": "5.1.2.Using Lists as Que<PERSON>¶", "level": 3, "id": ""}, {"text": "5.1.3.List Comprehensions¶", "level": 3, "id": ""}, {"text": "5.1.4.Nested List Comprehensions¶", "level": 3, "id": ""}, {"text": "5.2.<PERSON><PERSON><PERSON><PERSON>¶", "level": 2, "id": ""}, {"text": "5.3.<PERSON><PERSON> and Sequences¶", "level": 2, "id": ""}, {"text": "5.4.<PERSON><PERSON>¶", "level": 2, "id": ""}, {"text": "5.5.Dictionaries¶", "level": 2, "id": ""}, {"text": "5.6.Looping Techniques¶", "level": 2, "id": ""}, {"text": "5.7.More on Conditions¶", "level": 2, "id": ""}, {"text": "5.8.Comparing Sequences and Other Types¶", "level": 2, "id": ""}, {"text": "Table of Contents", "level": 3, "id": ""}, {"text": "Previous topic", "level": 4, "id": ""}, {"text": "Next topic", "level": 4, "id": ""}, {"text": "This Page", "level": 3, "id": ""}, {"text": "Navigation", "level": 3, "id": ""}], "code_blocks": [], "success": true, "error_message": ""}]