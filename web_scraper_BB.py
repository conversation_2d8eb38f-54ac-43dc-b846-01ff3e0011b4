import asyncio
from crawl4ai import AsyncWebCrawler
from rich import print
import json
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import re
import os

async def extract_page_content(crawler, url):
    try:
        # Add query parameters to ensure we get the full page content
        full_url = url + ('?' if '?' not in url else '&') + 'full=1'
        print(f"[cyan]Fetching: {full_url}[/cyan]")
        result = await crawler.arun(url=full_url)
        
        if result and result.html:
            # Extract all content sections including code blocks
            content_data = {
                'url': url,
                'title': result.metadata.get('title', 'N/A') if result.metadata else 'N/A',
                'content': result.markdown,
                'links': [],
                'code_blocks': [],
                'sections': []
            }
            
            # Extract code blocks and their descriptions
            if result.html:
                soup = BeautifulSoup(result.html, 'html.parser')
                code_blocks = soup.select('pre code')
                for block in code_blocks:
                    content_data['code_blocks'].append({
                        'language': block.get('class', ['text'])[0] if block.get('class') else 'text',
                        'code': block.get_text()
                    })
                
                # Extract section headers and their content
                sections = soup.select('.nextra-content h1, .nextra-content h2, .nextra-content h3')
                for section in sections:
                    content_data['sections'].append({
                        'title': section.get_text(),
                        'level': int(section.name[1])
                    })
                
                # Extract links properly from the HTML
                all_links = soup.select('.nextra-sidebar-container a, .nextra-content a, .nextra-nav-container a')
                for link_elem in all_links:
                    href = link_elem.get('href')
                    if href:
                        # Clean and normalize the link
                        normalized_link = normalize_builderbot_link(href, url)
                        if normalized_link:
                            content_data['links'].append(normalized_link)
            
            # Log links extracted
            if content_data['links']:
                print(f"[blue]Found {len(content_data['links'])} links on {url}[/blue]")
            
            return content_data
    except Exception as e:
        print(f"[red]Error extracting content from {url}: {str(e)}[/red]")
    return None

def normalize_builderbot_link(href, base_url):
    """Normalizes the unusual BuilderBot link format to standard URLs with detailed logging"""
    original_href = href
    result = None
    
    # Base domain for the documentation
    domain = "https://www.builderbot.app"
    
    # Skip external links, anchors, or javascript
    if href.startswith(('javascript:', 'mailto:', '#')) or href == '':
        result = None
    # Handle the special format: </path>
    elif href.startswith('</') and href.endswith('>'):
        path = href[2:-1]
        if '#' in path:  # Handle fragment identifiers
            path = path.split('#')[0]
        result = f"{domain}/en/{path}"
    # Handle other format: <https://example.com>
    elif href.startswith('<') and href.endswith('>'):
        url = href[1:-1]
        if url.startswith(('http://', 'https://')):
            result = url
        else:
            if not url.startswith('/'):
                url = '/' + url
            result = f"{domain}{url}"
    # Handle normal relative links
    elif href.startswith('/'):
        result = urljoin(domain, href)
    # Handle other relative links
    else:
        result = urljoin(base_url, href)
    
    # Additional log for debugging link normalization
    if result and original_href != result:
        print(f"  Normalized: {original_href} -> {result}")
    
    return result

def is_valid_doc_link(url):
    """Check if a URL is a valid documentation page to crawl with detailed logging"""
    parsed = urlparse(url)
    
    # Only process builderbot.app domain
    if parsed.netloc != 'www.builderbot.app':
        print(f"  [red]Skip external: {url}[/red]")
        return False
    
    # Skip media files
    if re.search(r'\.(png|jpg|jpeg|gif|css|js|svg|ico)$', parsed.path):
        print(f"  [red]Skip media: {url}[/red]")
        return False
    
    # Skip anchor links on the same page
    if '#' in url and url.split('#')[0] in processed_urls:
        print(f"  [red]Skip anchor: {url}[/red]")
        return False
    
    # Skip certain patterns that might lead to infinite crawling
    skip_patterns = [
        '/signin', '/login', '/logout', '/search', 
        'twitter.com', 'github.com', 'discord.com'
    ]
    
    for pattern in skip_patterns:
        if pattern in url:
            print(f"  [red]Skip pattern '{pattern}': {url}[/red]")
            return False
    
    print(f"  [green]Valid link: {url}[/green]")
    return True

async def main():
    base_url = "https://www.builderbot.app/en"
    global processed_urls
    processed_urls = set()
    docs_data = []
    queue = [base_url]
    output_file = 'builderbot_docs.json'
    
    # Track pages to visit
    visited_pages = 0
    max_pages = 200  # Increased limit to ensure we get the full documentation
    
    # Try to load existing data if we're resuming
    if os.path.exists(output_file):
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                docs_data = json.load(f)
                processed_urls = set(item['url'] for item in docs_data)
                print(f"[bold green]Loaded {len(docs_data)} pages from existing file[/bold green]")
                visited_pages = len(docs_data)
        except Exception as e:
            print(f"[red]Error loading existing data: {str(e)}[/red]")

    async with AsyncWebCrawler(
        browser_required=True,
        javascript_enabled=True,
        wait_until='networkidle',
        css_selectors={
            'title': '.nextra-content h1, .nextra-content h2',
            'content': '.nextra-content article',
            'links': '.nextra-sidebar-container a, .nextra-content a, .nextra-nav-container a'
        },
        text_mode=True,
        extract_metadata=True,
        extract_links=True,
        scroll_to_load=True,
        wait_for_navigation=10000,  # Increased to 10 seconds
        max_concurrent_requests=1,  # Reduced to 1 for gentler crawling
        request_delay=3  # Increased delay to 3 seconds
    ) as crawler:
        while queue and visited_pages < max_pages:
            current_url = queue.pop(0)
            
            # Skip if already processed
            if current_url in processed_urls:
                continue
                
            print(f"\n[bold blue]Processing: {current_url} ({visited_pages+1}/{max_pages})[/bold blue]")
            page_data = await extract_page_content(crawler, current_url)
            visited_pages += 1
            
            if page_data:
                docs_data.append(page_data)
                processed_urls.add(current_url)
                print(f"[green]✓[/green] Successfully extracted content from: {current_url}")
                
                # Process new links from this page
                link_count = 0
                for link in page_data['links']:
                    # Clean up URLs by removing any fragments
                    clean_link = link.split('#')[0]
                    
                    if clean_link and clean_link not in processed_urls and clean_link not in queue:
                        if is_valid_doc_link(clean_link):
                            queue.append(clean_link)
                            link_count += 1
                            print(f"[yellow]Added to queue: {clean_link}[/yellow]")
                
                print(f"[blue]Added {link_count} new links to the queue. Queue size: {len(queue)}[/blue]")
                
                # Save progress periodically
                if visited_pages % 10 == 0:
                    try:
                        with open(f'builderbot_docs_partial_{visited_pages}.json', 'w', encoding='utf-8') as f:
                            json.dump(docs_data, f, indent=2, ensure_ascii=False)
                        print(f"\n[bold green]✓ Partial documentation saved ({visited_pages} pages)[/bold green]")
                    except Exception as e:
                        print(f"[red]Error saving partial documentation: {str(e)}[/red]")

        # Save the complete documentation
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(docs_data, f, indent=2, ensure_ascii=False)
            print(f"\n[bold green]✓ Documentation saved to {output_file}[/bold green]")
            print(f"[green]Total pages processed: {len(docs_data)}[/green]")
        except Exception as e:
            print(f"[red]Error saving documentation: {str(e)}[/red]")

if __name__ == "__main__":
    asyncio.run(main())