[{"url": "https://docs.python.org/3/tutorial/index.html", "title": "The Python Tutorial — Python 3.13.2 documentation", "content": "[ ![Python logo](https://docs.python.org/3/_static/py.svg) ](https://docs.python.org/3/tutorial/<https:/www.python.org/>) dev (3.14)***********.*************.***********.***********.72.6\nEnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\nTheme  Auto Light Dark\n#### Previous topic\n[Changelog](https://docs.python.org/3/tutorial/<../whatsnew/changelog.html> \"previous chapter\")\n#### Next topic\n[1. Whetting Your Appetite](https://docs.python.org/3/tutorial/<appetite.html> \"next chapter\")\n### This Page\n  * [Report a Bug](https://docs.python.org/3/tutorial/<../bugs.html>)\n  * [Show Source ](https://docs.python.org/3/tutorial/<https:/github.com/python/cpython/blob/main/Doc/tutorial/index.rst>)\n\n\n### Navigation\n  * [index](https://docs.python.org/3/tutorial/<../genindex.html> \"General Index\")\n  * [modules](https://docs.python.org/3/tutorial/<../py-modindex.html> \"Python Module Index\") |\n  * [next](https://docs.python.org/3/tutorial/<appetite.html> \"1. Whetting Your Appetite\") |\n  * [previous](https://docs.python.org/3/tutorial/<../whatsnew/changelog.html> \"Changelog\") |\n  * ![Python logo](https://docs.python.org/3/_static/py.svg)\n  * [Python](https://docs.python.org/3/tutorial/<https:/www.python.org/>) »\n  * EnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\ndev (3.14)***********.*************.***********.***********.72.6\n  * [3.13.2 Documentation](https://docs.python.org/3/tutorial/<../index.html>) » \n  * [The Python Tutorial](https://docs.python.org/3/tutorial/<>)\n  * | \n  * Theme  Auto Light Dark |\n\n\n# The Python Tutorial[¶](https://docs.python.org/3/tutorial/<#the-python-tutorial> \"Link to this heading\")\nPython is an easy to learn, powerful programming language. It has efficient high-level data structures and a simple but effective approach to object-oriented programming. Python’s elegant syntax and dynamic typing, together with its interpreted nature, make it an ideal language for scripting and rapid application development in many areas on most platforms.\nThe Python interpreter and the extensive standard library are freely available in source or binary form for all major platforms from the Python web site, <https://www.python.org/>, and may be freely distributed. The same site also contains distributions of and pointers to many free third party Python modules, programs and tools, and additional documentation.\nThe Python interpreter is easily extended with new functions and data types implemented in C or C++ (or other languages callable from C). Python is also suitable as an extension language for customizable applications.\nThis tutorial introduces the reader informally to the basic concepts and features of the Python language and system. It helps to have a Python interpreter handy for hands-on experience, but all examples are self-contained, so the tutorial can be read off-line as well.\nFor a description of standard objects and modules, see [The Python Standard Library](https://docs.python.org/3/tutorial/<../library/index.html#library-index>). [The Python Language Reference](https://docs.python.org/3/tutorial/<../reference/index.html#reference-index>) gives a more formal definition of the language. To write extensions in C or C++, read [Extending and Embedding the Python Interpreter](https://docs.python.org/3/tutorial/<../extending/index.html#extending-index>) and [Python/C API Reference Manual](https://docs.python.org/3/tutorial/<../c-api/index.html#c-api-index>). There are also several books covering Python in depth.\nThis tutorial does not attempt to be comprehensive and cover every single feature, or even every commonly used feature. Instead, it introduces many of Python’s most noteworthy features, and will give you a good idea of the language’s flavor and style. After reading it, you will be able to read and write Python modules and programs, and you will be ready to learn more about the various Python library modules described in [The Python Standard Library](https://docs.python.org/3/tutorial/<../library/index.html#library-index>).\nThe [Glossary](https://docs.python.org/3/tutorial/<../glossary.html#glossary>) is also worth going through.\n  * [1. Whetting Your Appetite](https://docs.python.org/3/tutorial/<appetite.html>)\n  * [2. Using the Python Interpreter](https://docs.python.org/3/tutorial/<interpreter.html>)\n    * [2.1. Invoking the Interpreter](https://docs.python.org/3/tutorial/<interpreter.html#invoking-the-interpreter>)\n      * [2.1.1. Argument Passing](https://docs.python.org/3/tutorial/<interpreter.html#argument-passing>)\n      * [2.1.2. Interactive Mode](https://docs.python.org/3/tutorial/<interpreter.html#interactive-mode>)\n    * [2.2. The Interpreter and Its Environment](https://docs.python.org/3/tutorial/<interpreter.html#the-interpreter-and-its-environment>)\n      * [2.2.1. Source Code Encoding](https://docs.python.org/3/tutorial/<interpreter.html#source-code-encoding>)\n  * [3. An Informal Introduction to Python](https://docs.python.org/3/tutorial/<introduction.html>)\n    * [3.1. Using Python as a Calculator](https://docs.python.org/3/tutorial/<introduction.html#using-python-as-a-calculator>)\n      * [3.1.1. Numbers](https://docs.python.org/3/tutorial/<introduction.html#numbers>)\n      * [3.1.2. Text](https://docs.python.org/3/tutorial/<introduction.html#text>)\n      * [3.1.3. Lists](https://docs.python.org/3/tutorial/<introduction.html#lists>)\n    * [3.2. First Steps Towards Programming](https://docs.python.org/3/tutorial/<introduction.html#first-steps-towards-programming>)\n  * [4. More Control Flow Tools](https://docs.python.org/3/tutorial/<controlflow.html>)\n    * [4.1. `if` Statements](https://docs.python.org/3/tutorial/<controlflow.html#if-statements>)\n    * [4.2. `for` Statements](https://docs.python.org/3/tutorial/<controlflow.html#for-statements>)\n    * [4.3. The `range()` Function](https://docs.python.org/3/tutorial/<controlflow.html#the-range-function>)\n    * [4.4. `break` and `continue` Statements](https://docs.python.org/3/tutorial/<controlflow.html#break-and-continue-statements>)\n    * [4.5. `else` Clauses on Loops](https://docs.python.org/3/tutorial/<controlflow.html#else-clauses-on-loops>)\n    * [4.6. `pass` Statements](https://docs.python.org/3/tutorial/<controlflow.html#pass-statements>)\n    * [4.7. `match` Statements](https://docs.python.org/3/tutorial/<controlflow.html#match-statements>)\n    * [4.8. Defining Functions](https://docs.python.org/3/tutorial/<controlflow.html#defining-functions>)\n    * [4.9. More on Defining Functions](https://docs.python.org/3/tutorial/<controlflow.html#more-on-defining-functions>)\n      * [4.9.1. Default Argument Values](https://docs.python.org/3/tutorial/<controlflow.html#default-argument-values>)\n      * [4.9.2. Keyword Arguments](https://docs.python.org/3/tutorial/<controlflow.html#keyword-arguments>)\n      * [4.9.3. Special parameters](https://docs.python.org/3/tutorial/<controlflow.html#special-parameters>)\n        * [4.9.3.1. Positional-or-Keyword Arguments](https://docs.python.org/3/tutorial/<controlflow.html#positional-or-keyword-arguments>)\n        * [4.9.3.2. Positional-Only Parameters](https://docs.python.org/3/tutorial/<controlflow.html#positional-only-parameters>)\n        * [4.9.3.3. Keyword-Only Arguments](https://docs.python.org/3/tutorial/<controlflow.html#keyword-only-arguments>)\n        * [4.9.3.4. Function Examples](https://docs.python.org/3/tutorial/<controlflow.html#function-examples>)\n        * [4.9.3.5. Recap](https://docs.python.org/3/tutorial/<controlflow.html#recap>)\n      * [4.9.4. Arbitrary Argument Lists](https://docs.python.org/3/tutorial/<controlflow.html#arbitrary-argument-lists>)\n      * [4.9.5. Unpacking Argument Lists](https://docs.python.org/3/tutorial/<controlflow.html#unpacking-argument-lists>)\n      * [4.9.6. Lambda Expressions](https://docs.python.org/3/tutorial/<controlflow.html#lambda-expressions>)\n      * [4.9.7. Documentation Strings](https://docs.python.org/3/tutorial/<controlflow.html#documentation-strings>)\n      * [4.9.8. Function Annotations](https://docs.python.org/3/tutorial/<controlflow.html#function-annotations>)\n    * [4.10. Intermezzo: Coding Style](https://docs.python.org/3/tutorial/<controlflow.html#intermezzo-coding-style>)\n  * [5. Data Structures](https://docs.python.org/3/tutorial/<datastructures.html>)\n    * [5.1. More on Lists](https://docs.python.org/3/tutorial/<datastructures.html#more-on-lists>)\n      * [5.1.1. Using Lists as Stacks](https://docs.python.org/3/tutorial/<datastructures.html#using-lists-as-stacks>)\n      * [5.1.2. Using Lists as Queues](https://docs.python.org/3/tutorial/<datastructures.html#using-lists-as-queues>)\n      * [5.1.3. List Comprehensions](https://docs.python.org/3/tutorial/<datastructures.html#list-comprehensions>)\n      * [5.1.4. Nested List Comprehensions](https://docs.python.org/3/tutorial/<datastructures.html#nested-list-comprehensions>)\n    * [5.2. The `del` statement](https://docs.python.org/3/tutorial/<datastructures.html#the-del-statement>)\n    * [5.3. Tuples and Sequences](https://docs.python.org/3/tutorial/<datastructures.html#tuples-and-sequences>)\n    * [5.4. Sets](https://docs.python.org/3/tutorial/<datastructures.html#sets>)\n    * [5.5. Dictionaries](https://docs.python.org/3/tutorial/<datastructures.html#dictionaries>)\n    * [5.6. Looping Techniques](https://docs.python.org/3/tutorial/<datastructures.html#looping-techniques>)\n    * [5.7. More on Conditions](https://docs.python.org/3/tutorial/<datastructures.html#more-on-conditions>)\n    * [5.8. Comparing Sequences and Other Types](https://docs.python.org/3/tutorial/<datastructures.html#comparing-sequences-and-other-types>)\n  * [6. Modules](https://docs.python.org/3/tutorial/<modules.html>)\n    * [6.1. More on Modules](https://docs.python.org/3/tutorial/<modules.html#more-on-modules>)\n      * [6.1.1. Executing modules as scripts](https://docs.python.org/3/tutorial/<modules.html#executing-modules-as-scripts>)\n      * [6.1.2. The Module Search Path](https://docs.python.org/3/tutorial/<modules.html#the-module-search-path>)\n      * [6.1.3. “Compiled” Python files](https://docs.python.org/3/tutorial/<modules.html#compiled-python-files>)\n    * [6.2. Standard Modules](https://docs.python.org/3/tutorial/<modules.html#standard-modules>)\n    * [6.3. The `dir()` Function](https://docs.python.org/3/tutorial/<modules.html#the-dir-function>)\n    * [6.4. Packages](https://docs.python.org/3/tutorial/<modules.html#packages>)\n      * [6.4.1. Importing * From a Package](https://docs.python.org/3/tutorial/<modules.html#importing-from-a-package>)\n      * [6.4.2. Intra-package References](https://docs.python.org/3/tutorial/<modules.html#intra-package-references>)\n      * [6.4.3. Packages in Multiple Directories](https://docs.python.org/3/tutorial/<modules.html#packages-in-multiple-directories>)\n  * [7. Input and Output](https://docs.python.org/3/tutorial/<inputoutput.html>)\n    * [7.1. Fancier Output Formatting](https://docs.python.org/3/tutorial/<inputoutput.html#fancier-output-formatting>)\n      * [7.1.1. Formatted String Literals](https://docs.python.org/3/tutorial/<inputoutput.html#formatted-string-literals>)\n      * [7.1.2. The String format() Method](https://docs.python.org/3/tutorial/<inputoutput.html#the-string-format-method>)\n      * [7.1.3. Manual String Formatting](https://docs.python.org/3/tutorial/<inputoutput.html#manual-string-formatting>)\n      * [7.1.4. Old string formatting](https://docs.python.org/3/tutorial/<inputoutput.html#old-string-formatting>)\n    * [7.2. Reading and Writing Files](https://docs.python.org/3/tutorial/<inputoutput.html#reading-and-writing-files>)\n      * [7.2.1. Methods of File Objects](https://docs.python.org/3/tutorial/<inputoutput.html#methods-of-file-objects>)\n      * [7.2.2. Saving structured data with `json`](https://docs.python.org/3/tutorial/<inputoutput.html#saving-structured-data-with-json>)\n  * [8. Errors and Exceptions](https://docs.python.org/3/tutorial/<errors.html>)\n    * [8.1. Syntax Errors](https://docs.python.org/3/tutorial/<errors.html#syntax-errors>)\n    * [8.2. Exceptions](https://docs.python.org/3/tutorial/<errors.html#exceptions>)\n    * [8.3. Handling Exceptions](https://docs.python.org/3/tutorial/<errors.html#handling-exceptions>)\n    * [8.4. Raising Exceptions](https://docs.python.org/3/tutorial/<errors.html#raising-exceptions>)\n    * [8.5. Exception Chaining](https://docs.python.org/3/tutorial/<errors.html#exception-chaining>)\n    * [8.6. User-defined Exceptions](https://docs.python.org/3/tutorial/<errors.html#user-defined-exceptions>)\n    * [8.7. Defining Clean-up Actions](https://docs.python.org/3/tutorial/<errors.html#defining-clean-up-actions>)\n    * [8.8. Predefined Clean-up Actions](https://docs.python.org/3/tutorial/<errors.html#predefined-clean-up-actions>)\n    * [8.9. Raising and Handling Multiple Unrelated Exceptions](https://docs.python.org/3/tutorial/<errors.html#raising-and-handling-multiple-unrelated-exceptions>)\n    * [8.10. Enriching Exceptions with Notes](https://docs.python.org/3/tutorial/<errors.html#enriching-exceptions-with-notes>)\n  * [9. Classes](https://docs.python.org/3/tutorial/<classes.html>)\n    * [9.1. A Word About Names and Objects](https://docs.python.org/3/tutorial/<classes.html#a-word-about-names-and-objects>)\n    * [9.2. Python Scopes and Namespaces](https://docs.python.org/3/tutorial/<classes.html#python-scopes-and-namespaces>)\n      * [9.2.1. Scopes and Namespaces Example](https://docs.python.org/3/tutorial/<classes.html#scopes-and-namespaces-example>)\n    * [9.3. A First Look at Classes](https://docs.python.org/3/tutorial/<classes.html#a-first-look-at-classes>)\n      * [9.3.1. Class Definition Syntax](https://docs.python.org/3/tutorial/<classes.html#class-definition-syntax>)\n      * [9.3.2. Class Objects](https://docs.python.org/3/tutorial/<classes.html#class-objects>)\n      * [9.3.3. Instance Objects](https://docs.python.org/3/tutorial/<classes.html#instance-objects>)\n      * [9.3.4. Method Objects](https://docs.python.org/3/tutorial/<classes.html#method-objects>)\n      * [9.3.5. Class and Instance Variables](https://docs.python.org/3/tutorial/<classes.html#class-and-instance-variables>)\n    * [9.4. Random Remarks](https://docs.python.org/3/tutorial/<classes.html#random-remarks>)\n    * [9.5. Inheritance](https://docs.python.org/3/tutorial/<classes.html#inheritance>)\n      * [9.5.1. Multiple Inheritance](https://docs.python.org/3/tutorial/<classes.html#multiple-inheritance>)\n    * [9.6. Private Variables](https://docs.python.org/3/tutorial/<classes.html#private-variables>)\n    * [9.7. Odds and Ends](https://docs.python.org/3/tutorial/<classes.html#odds-and-ends>)\n    * [9.8. Iterators](https://docs.python.org/3/tutorial/<classes.html#iterators>)\n    * [9.9. Generators](https://docs.python.org/3/tutorial/<classes.html#generators>)\n    * [9.10. Generator Expressions](https://docs.python.org/3/tutorial/<classes.html#generator-expressions>)\n  * [10. Brief Tour of the Standard Library](https://docs.python.org/3/tutorial/<stdlib.html>)\n    * [10.1. Operating System Interface](https://docs.python.org/3/tutorial/<stdlib.html#operating-system-interface>)\n    * [10.2. File Wildcards](https://docs.python.org/3/tutorial/<stdlib.html#file-wildcards>)\n    * [10.3. Command Line Arguments](https://docs.python.org/3/tutorial/<stdlib.html#command-line-arguments>)\n    * [10.4. Error Output Redirection and Program Termination](https://docs.python.org/3/tutorial/<stdlib.html#error-output-redirection-and-program-termination>)\n    * [10.5. String Pattern Matching](https://docs.python.org/3/tutorial/<stdlib.html#string-pattern-matching>)\n    * [10.6. Mathematics](https://docs.python.org/3/tutorial/<stdlib.html#mathematics>)\n    * [10.7. Internet Access](https://docs.python.org/3/tutorial/<stdlib.html#internet-access>)\n    * [10.8. Dates and Times](https://docs.python.org/3/tutorial/<stdlib.html#dates-and-times>)\n    * [10.9. Data Compression](https://docs.python.org/3/tutorial/<stdlib.html#data-compression>)\n    * [10.10. Performance Measurement](https://docs.python.org/3/tutorial/<stdlib.html#performance-measurement>)\n    * [10.11. Quality Control](https://docs.python.org/3/tutorial/<stdlib.html#quality-control>)\n    * [10.12. Batteries Included](https://docs.python.org/3/tutorial/<stdlib.html#batteries-included>)\n  * [11. Brief Tour of the Standard Library — Part II](https://docs.python.org/3/tutorial/<stdlib2.html>)\n    * [11.1. Output Formatting](https://docs.python.org/3/tutorial/<stdlib2.html#output-formatting>)\n    * [11.2. Templating](https://docs.python.org/3/tutorial/<stdlib2.html#templating>)\n    * [11.3. Working with Binary Data Record Layouts](https://docs.python.org/3/tutorial/<stdlib2.html#working-with-binary-data-record-layouts>)\n    * [11.4. Multi-threading](https://docs.python.org/3/tutorial/<stdlib2.html#multi-threading>)\n    * [11.5. Logging](https://docs.python.org/3/tutorial/<stdlib2.html#logging>)\n    * [11.6. Weak References](https://docs.python.org/3/tutorial/<stdlib2.html#weak-references>)\n    * [11.7. Tools for Working with Lists](https://docs.python.org/3/tutorial/<stdlib2.html#tools-for-working-with-lists>)\n    * [11.8. Decimal Floating-Point Arithmetic](https://docs.python.org/3/tutorial/<stdlib2.html#decimal-floating-point-arithmetic>)\n  * [12. Virtual Environments and Packages](https://docs.python.org/3/tutorial/<venv.html>)\n    * [12.1. Introduction](https://docs.python.org/3/tutorial/<venv.html#introduction>)\n    * [12.2. Creating Virtual Environments](https://docs.python.org/3/tutorial/<venv.html#creating-virtual-environments>)\n    * [12.3. Managing Packages with pip](https://docs.python.org/3/tutorial/<venv.html#managing-packages-with-pip>)\n  * [13. What Now?](https://docs.python.org/3/tutorial/<whatnow.html>)\n  * [14. Interactive Input Editing and History Substitution](https://docs.python.org/3/tutorial/<interactive.html>)\n    * [14.1. Tab Completion and History Editing](https://docs.python.org/3/tutorial/<interactive.html#tab-completion-and-history-editing>)\n    * [14.2. Alternatives to the Interactive Interpreter](https://docs.python.org/3/tutorial/<interactive.html#alternatives-to-the-interactive-interpreter>)\n  * [15. Floating-Point Arithmetic: Issues and Limitations](https://docs.python.org/3/tutorial/<floatingpoint.html>)\n    * [15.1. Representation Error](https://docs.python.org/3/tutorial/<floatingpoint.html#representation-error>)\n  * [16. Appendix](https://docs.python.org/3/tutorial/<appendix.html>)\n    * [16.1. Interactive Mode](https://docs.python.org/3/tutorial/<appendix.html#interactive-mode>)\n      * [16.1.1. Error Handling](https://docs.python.org/3/tutorial/<appendix.html#error-handling>)\n      * [16.1.2. Executable Python Scripts](https://docs.python.org/3/tutorial/<appendix.html#executable-python-scripts>)\n      * [16.1.3. The Interactive Startup File](https://docs.python.org/3/tutorial/<appendix.html#the-interactive-startup-file>)\n      * [16.1.4. The Customization Modules](https://docs.python.org/3/tutorial/<appendix.html#the-customization-modules>)\n\n\n#### Previous topic\n[Changelog](https://docs.python.org/3/tutorial/<../whatsnew/changelog.html> \"previous chapter\")\n#### Next topic\n[1. Whetting Your Appetite](https://docs.python.org/3/tutorial/<appetite.html> \"next chapter\")\n### This Page\n  * [Report a Bug](https://docs.python.org/3/tutorial/<../bugs.html>)\n  * [Show Source ](https://docs.python.org/3/tutorial/<https:/github.com/python/cpython/blob/main/Doc/tutorial/index.rst>)\n\n\n«\n### Navigation\n  * [index](https://docs.python.org/3/tutorial/<../genindex.html> \"General Index\")\n  * [modules](https://docs.python.org/3/tutorial/<../py-modindex.html> \"Python Module Index\") |\n  * [next](https://docs.python.org/3/tutorial/<appetite.html> \"1. Whetting Your Appetite\") |\n  * [previous](https://docs.python.org/3/tutorial/<../whatsnew/changelog.html> \"Changelog\") |\n  * ![Python logo](https://docs.python.org/3/_static/py.svg)\n  * [Python](https://docs.python.org/3/tutorial/<https:/www.python.org/>) »\n  * EnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\ndev (3.14)***********.*************.***********.***********.72.6\n  * [3.13.2 Documentation](https://docs.python.org/3/tutorial/<../index.html>) » \n  * [The Python Tutorial](https://docs.python.org/3/tutorial/<>)\n  * | \n  * Theme  Auto Light Dark |\n\n\n© [ Copyright ](https://docs.python.org/3/tutorial/<../copyright.html>) 2001-2025, Python Software Foundation. This page is licensed under the Python Software Foundation License Version 2. Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License. See [History and License](https://docs.python.org/3/tutorial/</license.html>) for more information. The Python Software Foundation is a non-profit corporation. [Please donate.](https://docs.python.org/3/tutorial/<https:/www.python.org/psf/donations/>) Last updated on Mar 02, 2025 (11:16 UTC). [Found a bug](https://docs.python.org/3/tutorial/</bugs.html>)? Created using [Sphinx](https://docs.python.org/3/tutorial/<https:/www.sphinx-doc.org/>) 8.2.1. \n", "chunks": [{"content": "[ ![Python logo](https://docs.python.org/3/_static/py.svg) ](https://docs.python.org/3/tutorial/<https:/www.python.org/>) dev (3.14)***********.*************.***********.***********.72.6\nEnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\nTheme  Auto Light Dark\n#### Previous topic\n[Changelog](https://docs.python.org/3/tutorial/<../whatsnew/changelog.html> \"previous chapter\")\n#### Next topic\n[1. Whetting Your Appetite](https://docs.python.org/3/tutorial/<appetite.html> \"next chapter\")\n### This Page\n  * [Report a Bug](https://docs.python.org/3/tutorial/<../bugs.html>)\n  * [Show Source ](https://docs.python.org/3/tutorial/<https:/github.com/python/cpython/blob/main/Doc/tutorial/index.rst>)", "metadata": {"title": "The Python Tutorial — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/index.html", "section": "Sin título"}, "embedding": null}, {"content": "### Navigation\n  * [index](https://docs.python.org/3/tutorial/<../genindex.html> \"General Index\")\n  * [modules](https://docs.python.org/3/tutorial/<../py-modindex.html> \"Python Module Index\") |\n  * [next](https://docs.python.org/3/tutorial/<appetite.html> \"1. Whetting Your Appetite\") |\n  * [previous](https://docs.python.org/3/tutorial/<../whatsnew/changelog.html> \"Changelog\") |\n  * ![Python logo](https://docs.python.org/3/_static/py.svg)\n  * [Python](https://docs.python.org/3/tutorial/<https:/www.python.org/>) »\n  * EnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\ndev (3.14)***********.*************.***********.***********.72.6\n  * [3.13.2 Documentation](https://docs.python.org/3/tutorial/<../index.html>) » \n  * [The Python Tutorial](https://docs.python.org/3/tutorial/<>)\n  * | \n  * Theme  Auto Light Dark |\n\n", "metadata": {"title": "The Python Tutorial — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/index.html", "section": "Sin título"}, "embedding": null}, {"content": "# The Python Tutorial[¶](https://docs.python.org/3/tutorial/<#the-python-tutorial> \"Link to this heading\")\nPython is an easy to learn, powerful programming language. It has efficient high-level data structures and a simple but effective approach to object-oriented programming. Python’s elegant syntax and dynamic typing, together with its interpreted nature, make it an ideal language for scripting and rapid application development in many areas on most platforms.\nThe Python interpreter and the extensive standard library are freely available in source or binary form for all major platforms from the Python web site, <https://www.python.org/>, and may be freely distributed. The same site also contains distributions of and pointers to many free third party Python modules, programs and tools, and additional documentation.\nThe Python interpreter is easily extended with new functions and data types implemented in C or C++ (or other languages callable from C). Python is also suitable as an extension language for customizable applications.\nThis tutorial introduces the reader informally to the basic concepts and features of the Python language and system. It helps to have a Python interpreter handy for hands-on experience, but all examples are self-contained, so the tutorial can be read off-line as well.\nFor a description of standard objects and modules, see [The Python Standard Library](https://docs.python.org/3/tutorial/<../library/index.html#library-index>). [The Python Language Reference](https://docs.python.org/3/tutorial/<../reference/index.html#reference-index>) gives a more formal definition of the language. To write extensions in C or C++, read [Extending and Embedding the Python Interpreter](https://docs.python.org/3/tutorial/<../extending/index.html#extending-index>) and [Python/C API Reference Manual](https://docs.python.org/3/tutorial/<../c-api/index.html#c-api-index>). There are also several books covering Python in depth.\nThis tutorial does not attempt to be comprehensive and cover every single feature, or even every commonly used feature. Instead, it introduces many of Python’s most noteworthy features, and will give you a good idea of the language’s flavor and style. After reading it, you will be able to read and write Python modules and programs, and you will be ready to learn more about the various Python library modules described in [The Python Standard Library](https://docs.python.org/3/tutorial/<../library/index.html#library-index>).\nThe [Glossary](https://docs.python.org/3/tutorial/<../glossary.html#glossary>) is also worth going through.\n  * [1. Whetting Your Appetite](https://docs.python.org/3/tutorial/<appetite.html>)\n  * [2. Using the Python Interpreter](https://docs.python.org/3/tutorial/<interpreter.html>)\n    * [2.1. Invoking the Interpreter](https://docs.python.org/3/tutorial/<interpreter.html#invoking-the-interpreter>)\n      * [2.1.1. Argument Passing](https://docs.python.org/3/tutorial/<interpreter.html#argument-passing>)\n      * [2.1.2. Interactive Mode](https://docs.python.org/3/tutorial/<interpreter.html#interactive-mode>)\n    * [2.2. The Interpreter and Its Environment](https://docs.python.org/3/tutorial/<interpreter.html#the-interpreter-and-its-environment>)\n      * [2.2.1. Source Code Encoding](https://docs.python.org/3/tutorial/<interpreter.html#source-code-encoding>)\n  * [3. An Informal Introduction to Python](https://docs.python.org/3/tutorial/<introduction.html>)\n    * [3.1. Using Python as a Calculator](https://docs.python.org/3/tutorial/<introduction.html#using-python-as-a-calculator>)\n      * [3.1.1. Numbers](https://docs.python.org/3/tutorial/<introduction.html#numbers>)\n      * [3.1.2. Text](https://docs.python.org/3/tutorial/<introduction.html#text>)\n      * [3.1.3. Lists](https://docs.python.org/3/tutorial/<introduction.html#lists>)\n    * [3.2. First Steps Towards Programming](https://docs.python.org/3/tutorial/<introduction.html#first-steps-towards-programming>)\n  * [4. More Control Flow Tools](https://docs.python.org/3/tutorial/<controlflow.html>)\n    * [4.1. `if` Statements](https://docs.python.org/3/tutorial/<controlflow.html#if-statements>)\n    * [4.2. `for` Statements](https://docs.python.org/3/tutorial/<controlflow.html#for-statements>)\n    * [4.3. The `range()` Function](https://docs.python.org/3/tutorial/<controlflow.html#the-range-function>)\n    * [4.4. `break` and `continue` Statements](https://docs.python.org/3/tutorial/<controlflow.html#break-and-continue-statements>)\n    * [4.5. `else` Clauses on Loops](https://docs.python.org/3/tutorial/<controlflow.html#else-clauses-on-loops>)\n    * [4.6. `pass` Statements](https://docs.python.org/3/tutorial/<controlflow.html#pass-statements>)\n    * [4.7. `match` Statements](https://docs.python.org/3/tutorial/<controlflow.html#match-statements>)\n    * [4.8. Defining Functions](https://docs.python.org/3/tutorial/<controlflow.html#defining-functions>)\n    * [4.9. More on Defining Functions](https://docs.python.org/3/tutorial/<controlflow.html#more-on-defining-functions>)\n      * [4.9.1. Default Argument Values](https://docs.python.org/3/tutorial/<controlflow.html#default-argument-values>)\n      * [4.9.2. Keyword Arguments](https://docs.python.org/3/tutorial/<controlflow.html#keyword-arguments>)\n      * [4.9.3. Special parameters](https://docs.python.org/3/tutorial/<controlflow.html#special-parameters>)\n        * [4.9.3.1. Positional-or-Keyword Arguments](https://docs.python.org/3/tutorial/<controlflow.html#positional-or-keyword-arguments>)\n        * [4.9.3.2. Positional-Only Parameters](https://docs.python.org/3/tutorial/<controlflow.html#positional-only-parameters>)\n        * [4.9.3.3. Keyword-Only Arguments](https://docs.python.org/3/tutorial/<controlflow.html#keyword-only-arguments>)\n        * [4.9.3.4. Function Examples](https://docs.python.org/3/tutorial/<controlflow.html#function-examples>)\n        * [4.9.3.5. Recap](https://docs.python.org/3/tutorial/<controlflow.html#recap>)\n      * [4.9.4. Arbitrary Argument Lists](https://docs.python.org/3/tutorial/<controlflow.html#arbitrary-argument-lists>)\n      * [4.9.5. Unpacking Argument Lists](https://docs.python.org/3/tutorial/<controlflow.html#unpacking-argument-lists>)\n      * [4.9.6. Lambda Expressions](https://docs.python.org/3/tutorial/<controlflow.html#lambda-expressions>)\n      * [4.9.7. Documentation Strings](https://docs.python.org/3/tutorial/<controlflow.html#documentation-strings>)\n      * [4.9.8. Function Annotations](https://docs.python.org/3/tutorial/<controlflow.html#function-annotations>)\n    * [4.10. Intermezzo: Coding Style](https://docs.python.org/3/tutorial/<controlflow.html#intermezzo-coding-style>)\n  * [5. Data Structures](https://docs.python.org/3/tutorial/<datastructures.html>)\n    * [5.1. More on Lists](https://docs.python.org/3/tutorial/<datastructures.html#more-on-lists>)\n      * [5.1.1. Using Lists as Stacks](https://docs.python.org/3/tutorial/<datastructures.html#using-lists-as-stacks>)\n      * [5.1.2. Using Lists as Queues](https://docs.python.org/3/tutorial/<datastructures.html#using-lists-as-queues>)\n      * [5.1.3. List Comprehensions](https://docs.python.org/3/tutorial/<datastructures.html#list-comprehensions>)\n      * [5.1.4. Nested List Comprehensions](https://docs.python.org/3/tutorial/<datastructures.html#nested-list-comprehensions>)\n    * [5.2. The `del` statement](https://docs.python.org/3/tutorial/<datastructures.html#the-del-statement>)\n    * [5.3. Tuples and Sequences](https://docs.python.org/3/tutorial/<datastructures.html#tuples-and-sequences>)\n    * [5.4. Sets](https://docs.python.org/3/tutorial/<datastructures.html#sets>)\n    * [5.5. Dictionaries](https://docs.python.org/3/tutorial/<datastructures.html#dictionaries>)\n    * [5.6. Looping Techniques](https://docs.python.org/3/tutorial/<datastructures.html#looping-techniques>)\n    * [5.7. More on Conditions](https://docs.python.org/3/tutorial/<datastructures.html#more-on-conditions>)\n    * [5.8. Comparing Sequences and Other Types](https://docs.python.org/3/tutorial/<datastructures.html#comparing-sequences-and-other-types>)\n  * [6. Modules](https://docs.python.org/3/tutorial/<modules.html>)\n    * [6.1. More on Modules](https://docs.python.org/3/tutorial/<modules.html#more-on-modules>)\n      * [6.1.1. Executing modules as scripts](https://docs.python.org/3/tutorial/<modules.html#executing-modules-as-scripts>)\n      * [6.1.2. The Module Search Path](https://docs.python.org/3/tutorial/<modules.html#the-module-search-path>)\n      * [6.1.3. “Compiled” Python files](https://docs.python.org/3/tutorial/<modules.html#compiled-python-files>)\n    * [6.2. Standard Modules](https://docs.python.org/3/tutorial/<modules.html#standard-modules>)\n    * [6.3. The `dir()` Function](https://docs.python.org/3/tutorial/<modules.html#the-dir-function>)\n    * [6.4. Packages](https://docs.python.org/3/tutorial/<modules.html#packages>)\n      * [6.4.1. Importing * From a Package](https://docs.python.org/3/tutorial/<modules.html#importing-from-a-package>)\n      * [6.4.2. Intra-package References](https://docs.python.org/3/tutorial/<modules.html#intra-package-references>)\n      * [6.4.3. Packages in Multiple Directories](https://docs.python.org/3/tutorial/<modules.html#packages-in-multiple-directories>)\n  * [7. Input and Output](https://docs.python.org/3/tutorial/<inputoutput.html>)\n    * [7.1. Fancier Output Formatting](https://docs.python.org/3/tutorial/<inputoutput.html#fancier-output-formatting>)\n      * [7.1.1. Formatted String Literals](https://docs.python.org/3/tutorial/<inputoutput.html#formatted-string-literals>)\n      * [7.1.2. The String format() Method](https://docs.python.org/3/tutorial/<inputoutput.html#the-string-format-method>)\n      * [7.1.3. Manual String Formatting](https://docs.python.org/3/tutorial/<inputoutput.html#manual-string-formatting>)\n      * [7.1.4. Old string formatting](https://docs.python.org/3/tutorial/<inputoutput.html#old-string-formatting>)\n    * [7.2. Reading and Writing Files](https://docs.python.org/3/tutorial/<inputoutput.html#reading-and-writing-files>)\n      * [7.2.1. Methods of File Objects](https://docs.python.org/3/tutorial/<inputoutput.html#methods-of-file-objects>)\n      * [7.2.2. Saving structured data with `json`](https://docs.python.org/3/tutorial/<inputoutput.html#saving-structured-data-with-json>)\n  * [8. Errors and Exceptions](https://docs.python.org/3/tutorial/<errors.html>)\n    * [8.1. Syntax Errors](https://docs.python.org/3/tutorial/<errors.html#syntax-errors>)\n    * [8.2. Exceptions](https://docs.python.org/3/tutorial/<errors.html#exceptions>)\n    * [8.3. Handling Exceptions](https://docs.python.org/3/tutorial/<errors.html#handling-exceptions>)\n    * [8.4. Raising Exceptions](https://docs.python.org/3/tutorial/<errors.html#raising-exceptions>)\n    * [8.5. Exception Chaining](https://docs.python.org/3/tutorial/<errors.html#exception-chaining>)\n    * [8.6. User-defined Exceptions](https://docs.python.org/3/tutorial/<errors.html#user-defined-exceptions>)\n    * [8.7. Defining Clean-up Actions](https://docs.python.org/3/tutorial/<errors.html#defining-clean-up-actions>)\n    * [8.8. Predefined Clean-up Actions](https://docs.python.org/3/tutorial/<errors.html#predefined-clean-up-actions>)\n    * [8.9. Raising and Handling Multiple Unrelated Exceptions](https://docs.python.org/3/tutorial/<errors.html#raising-and-handling-multiple-unrelated-exceptions>)\n    * [8.10. Enriching Exceptions with Notes](https://docs.python.org/3/tutorial/<errors.html#enriching-exceptions-with-notes>)\n  * [9. Classes](https://docs.python.org/3/tutorial/<classes.html>)\n    * [9.1. A Word About Names and Objects](https://docs.python.org/3/tutorial/<classes.html#a-word-about-names-and-objects>)\n    * [9.2. Python Scopes and Namespaces](https://docs.python.org/3/tutorial/<classes.html#python-scopes-and-namespaces>)\n      * [9.2.1. Scopes and Namespaces Example](https://docs.python.org/3/tutorial/<classes.html#scopes-and-namespaces-example>)\n    * [9.3. A First Look at Classes](https://docs.python.org/3/tutorial/<classes.html#a-first-look-at-classes>)\n      * [9.3.1. Class Definition Syntax](https://docs.python.org/3/tutorial/<classes.html#class-definition-syntax>)\n      * [9.3.2. Class Objects](https://docs.python.org/3/tutorial/<classes.html#class-objects>)\n      * [9.3.3. Instance Objects](https://docs.python.org/3/tutorial/<classes.html#instance-objects>)\n      * [9.3.4. Method Objects](https://docs.python.org/3/tutorial/<classes.html#method-objects>)\n      * [9.3.5. Class and Instance Variables](https://docs.python.org/3/tutorial/<classes.html#class-and-instance-variables>)\n    * [9.4. Random Remarks](https://docs.python.org/3/tutorial/<classes.html#random-remarks>)\n    * [9.5. Inheritance](https://docs.python.org/3/tutorial/<classes.html#inheritance>)\n      * [9.5.1. Multiple Inheritance](https://docs.python.org/3/tutorial/<classes.html#multiple-inheritance>)\n    * [9.6. Private Variables](https://docs.python.org/3/tutorial/<classes.html#private-variables>)\n    * [9.7. Odds and Ends](https://docs.python.org/3/tutorial/<classes.html#odds-and-ends>)\n    * [9.8. Iterators](https://docs.python.org/3/tutorial/<classes.html#iterators>)\n    * [9.9. Generators](https://docs.python.org/3/tutorial/<classes.html#generators>)\n    * [9.10. Generator Expressions](https://docs.python.org/3/tutorial/<classes.html#generator-expressions>)\n  * [10. Brief Tour of the Standard Library](https://docs.python.org/3/tutorial/<stdlib.html>)\n    * [10.1. Operating System Interface](https://docs.python.org/3/tutorial/<stdlib.html#operating-system-interface>)\n    * [10.2. File Wildcards](https://docs.python.org/3/tutorial/<stdlib.html#file-wildcards>)\n    * [10.3. Command Line Arguments](https://docs.python.org/3/tutorial/<stdlib.html#command-line-arguments>)\n    * [10.4. Error Output Redirection and Program Termination](https://docs.python.org/3/tutorial/<stdlib.html#error-output-redirection-and-program-termination>)\n    * [10.5. String Pattern Matching](https://docs.python.org/3/tutorial/<stdlib.html#string-pattern-matching>)\n    * [10.6. Mathematics](https://docs.python.org/3/tutorial/<stdlib.html#mathematics>)\n    * [10.7. Internet Access](https://docs.python.org/3/tutorial/<stdlib.html#internet-access>)\n    * [10.8. Dates and Times](https://docs.python.org/3/tutorial/<stdlib.html#dates-and-times>)\n    * [10.9. Data Compression](https://docs.python.org/3/tutorial/<stdlib.html#data-compression>)\n    * [10.10. Performance Measurement](https://docs.python.org/3/tutorial/<stdlib.html#performance-measurement>)\n    * [10.11. Quality Control](https://docs.python.org/3/tutorial/<stdlib.html#quality-control>)\n    * [10.12. Batteries Included](https://docs.python.org/3/tutorial/<stdlib.html#batteries-included>)\n  * [11. Brief Tour of the Standard Library — Part II](https://docs.python.org/3/tutorial/<stdlib2.html>)\n    * [11.1. Output Formatting](https://docs.python.org/3/tutorial/<stdlib2.html#output-formatting>)\n    * [11.2. Templating](https://docs.python.org/3/tutorial/<stdlib2.html#templating>)\n    * [11.3. Working with Binary Data Record Layouts](https://docs.python.org/3/tutorial/<stdlib2.html#working-with-binary-data-record-layouts>)\n    * [11.4. Multi-threading](https://docs.python.org/3/tutorial/<stdlib2.html#multi-threading>)\n    * [11.5. Logging](https://docs.python.org/3/tutorial/<stdlib2.html#logging>)\n    * [11.6. Weak References](https://docs.python.org/3/tutorial/<stdlib2.html#weak-references>)\n    * [11.7. Tools for Working with Lists](https://docs.python.org/3/tutorial/<stdlib2.html#tools-for-working-with-lists>)\n    * [11.8. Decimal Floating-Point Arithmetic](https://docs.python.org/3/tutorial/<stdlib2.html#decimal-floating-point-arithmetic>)\n  * [12. Virtual Environments and Packages](https://docs.python.org/3/tutorial/<venv.html>)\n    * [12.1. Introduction](https://docs.python.org/3/tutorial/<venv.html#introduction>)\n    * [12.2. Creating Virtual Environments](https://docs.python.org/3/tutorial/<venv.html#creating-virtual-environments>)\n    * [12.3. Managing Packages with pip](https://docs.python.org/3/tutorial/<venv.html#managing-packages-with-pip>)\n  * [13. What Now?](https://docs.python.org/3/tutorial/<whatnow.html>)\n  * [14. Interactive Input Editing and History Substitution](https://docs.python.org/3/tutorial/<interactive.html>)\n    * [14.1. Tab Completion and History Editing](https://docs.python.org/3/tutorial/<interactive.html#tab-completion-and-history-editing>)\n    * [14.2. Alternatives to the Interactive Interpreter](https://docs.python.org/3/tutorial/<interactive.html#alternatives-to-the-interactive-interpreter>)\n  * [15. Floating-Point Arithmetic: Issues and Limitations](https://docs.python.org/3/tutorial/<floatingpoint.html>)\n    * [15.1. Representation Error](https://docs.python.org/3/tutorial/<floatingpoint.html#representation-error>)\n  * [16. Appendix](https://docs.python.org/3/tutorial/<appendix.html>)\n    * [16.1. Interactive Mode](https://docs.python.org/3/tutorial/<appendix.html#interactive-mode>)\n      * [16.1.1. Error Handling](https://docs.python.org/3/tutorial/<appendix.html#error-handling>)\n      * [16.1.2. Executable Python Scripts](https://docs.python.org/3/tutorial/<appendix.html#executable-python-scripts>)\n      * [16.1.3. The Interactive Startup File](https://docs.python.org/3/tutorial/<appendix.html#the-interactive-startup-file>)\n      * [16.1.4. The Customization Modules](https://docs.python.org/3/tutorial/<appendix.html#the-customization-modules>)", "metadata": {"title": "The Python Tutorial — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/index.html", "section": "The Python Tutorial[¶](https://docs.python.org/3/tutorial/<#the-python-tutorial> \"Link to this heading\")"}, "embedding": null}, {"content": "#### Previous topic\n[Changelog](https://docs.python.org/3/tutorial/<../whatsnew/changelog.html> \"previous chapter\")\n#### Next topic\n[1. Whetting Your Appetite](https://docs.python.org/3/tutorial/<appetite.html> \"next chapter\")\n### This Page\n  * [Report a Bug](https://docs.python.org/3/tutorial/<../bugs.html>)\n  * [Show Source ](https://docs.python.org/3/tutorial/<https:/github.com/python/cpython/blob/main/Doc/tutorial/index.rst>)", "metadata": {"title": "The Python Tutorial — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/index.html", "section": "The Python Tutorial[¶](https://docs.python.org/3/tutorial/<#the-python-tutorial> \"Link to this heading\")"}, "embedding": null}, {"content": "«\n### Navigation\n  * [index](https://docs.python.org/3/tutorial/<../genindex.html> \"General Index\")\n  * [modules](https://docs.python.org/3/tutorial/<../py-modindex.html> \"Python Module Index\") |\n  * [next](https://docs.python.org/3/tutorial/<appetite.html> \"1. Whetting Your Appetite\") |\n  * [previous](https://docs.python.org/3/tutorial/<../whatsnew/changelog.html> \"Changelog\") |\n  * ![Python logo](https://docs.python.org/3/_static/py.svg)\n  * [Python](https://docs.python.org/3/tutorial/<https:/www.python.org/>) »\n  * EnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\ndev (3.14)***********.*************.***********.***********.72.6\n  * [3.13.2 Documentation](https://docs.python.org/3/tutorial/<../index.html>) » \n  * [The Python Tutorial](https://docs.python.org/3/tutorial/<>)\n  * | \n  * Theme  Auto Light Dark |", "metadata": {"title": "The Python Tutorial — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/index.html", "section": "The Python Tutorial[¶](https://docs.python.org/3/tutorial/<#the-python-tutorial> \"Link to this heading\")"}, "embedding": null}, {"content": "© [ Copyright ](https://docs.python.org/3/tutorial/<../copyright.html>) 2001-2025, Python Software Foundation. This page is licensed under the Python Software Foundation License Version 2. Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License. See [History and License](https://docs.python.org/3/tutorial/</license.html>) for more information. The Python Software Foundation is a non-profit corporation. [Please donate.](https://docs.python.org/3/tutorial/<https:/www.python.org/psf/donations/>) Last updated on Mar 02, 2025 (11:16 UTC). [Found a bug](https://docs.python.org/3/tutorial/</bugs.html>)? Created using [Sphinx](https://docs.python.org/3/tutorial/<https:/www.sphinx-doc.org/>) 8.2.1. \n", "metadata": {"title": "The Python Tutorial — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/index.html", "section": "The Python Tutorial[¶](https://docs.python.org/3/tutorial/<#the-python-tutorial> \"Link to this heading\")"}, "embedding": null}], "headers": [{"text": "Previous topic", "level": 4, "id": ""}, {"text": "Next topic", "level": 4, "id": ""}, {"text": "This Page", "level": 3, "id": ""}, {"text": "Navigation", "level": 3, "id": ""}, {"text": "The Python Tutorial¶", "level": 1, "id": ""}, {"text": "Previous topic", "level": 4, "id": ""}, {"text": "Next topic", "level": 4, "id": ""}, {"text": "This Page", "level": 3, "id": ""}, {"text": "Navigation", "level": 3, "id": ""}], "code_blocks": [], "success": true, "error_message": ""}, {"url": "https://docs.python.org/3/tutorial/appetite.html", "title": "1. Whetting Your Appetite — Python 3.13.2 documentation", "content": "[ ![Python logo](https://docs.python.org/3/_static/py.svg) ](https://docs.python.org/3/tutorial/<https:/www.python.org/>) dev (3.14)***********.*************.***********.***********.72.6\nEnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\nTheme  Auto Light Dark\n#### Previous topic\n[The Python Tutorial](https://docs.python.org/3/tutorial/<index.html> \"previous chapter\")\n#### Next topic\n[2. Using the Python Interpreter](https://docs.python.org/3/tutorial/<interpreter.html> \"next chapter\")\n### This Page\n  * [Report a Bug](https://docs.python.org/3/tutorial/<../bugs.html>)\n  * [Show Source ](https://docs.python.org/3/tutorial/<https:/github.com/python/cpython/blob/main/Doc/tutorial/appetite.rst>)\n\n\n### Navigation\n  * [index](https://docs.python.org/3/tutorial/<../genindex.html> \"General Index\")\n  * [modules](https://docs.python.org/3/tutorial/<../py-modindex.html> \"Python Module Index\") |\n  * [next](https://docs.python.org/3/tutorial/<interpreter.html> \"2. Using the Python Interpreter\") |\n  * [previous](https://docs.python.org/3/tutorial/<index.html> \"The Python Tutorial\") |\n  * ![Python logo](https://docs.python.org/3/_static/py.svg)\n  * [Python](https://docs.python.org/3/tutorial/<https:/www.python.org/>) »\n  * EnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\ndev (3.14)***********.*************.***********.***********.72.6\n  * [3.13.2 Documentation](https://docs.python.org/3/tutorial/<../index.html>) » \n  * [The Python Tutorial](https://docs.python.org/3/tutorial/<index.html>) »\n  * [1. Whetting Your Appetite](https://docs.python.org/3/tutorial/<>)\n  * | \n  * Theme  Auto Light Dark |\n\n\n# 1. Whetting Your Appetite[¶](https://docs.python.org/3/tutorial/<#whetting-your-appetite> \"Link to this heading\")\nIf you do much work on computers, eventually you find that there’s some task you’d like to automate. For example, you may wish to perform a search-and-replace over a large number of text files, or rename and rearrange a bunch of photo files in a complicated way. Perhaps you’d like to write a small custom database, or a specialized GUI application, or a simple game.\nIf you’re a professional software developer, you may have to work with several C/C++/Java libraries but find the usual write/compile/test/re-compile cycle is too slow. Perhaps you’re writing a test suite for such a library and find writing the testing code a tedious task. Or maybe you’ve written a program that could use an extension language, and you don’t want to design and implement a whole new language for your application.\nPython is just the language for you.\nYou could write a Unix shell script or Windows batch files for some of these tasks, but shell scripts are best at moving around files and changing text data, not well-suited for GUI applications or games. You could write a C/C++/Java program, but it can take a lot of development time to get even a first-draft program. Python is simpler to use, available on Windows, macOS, and Unix operating systems, and will help you get the job done more quickly.\nPython is simple to use, but it is a real programming language, offering much more structure and support for large programs than shell scripts or batch files can offer. On the other hand, Python also offers much more error checking than C, and, being a _very-high-level language_ , it has high-level data types built in, such as flexible arrays and dictionaries. Because of its more general data types Python is applicable to a much larger problem domain than Awk or even Perl, yet many things are at least as easy in Python as in those languages.\nPython allows you to split your program into modules that can be reused in other Python programs. It comes with a large collection of standard modules that you can use as the basis of your programs — or as examples to start learning to program in Python. Some of these modules provide things like file I/O, system calls, sockets, and even interfaces to graphical user interface toolkits like Tk.\nPython is an interpreted language, which can save you considerable time during program development because no compilation and linking is necessary. The interpreter can be used interactively, which makes it easy to experiment with features of the language, to write throw-away programs, or to test functions during bottom-up program development. It is also a handy desk calculator.\nPython enables programs to be written compactly and readably. Programs written in Python are typically much shorter than equivalent C, C++, or Java programs, for several reasons:\n  * the high-level data types allow you to express complex operations in a single statement;\n  * statement grouping is done by indentation instead of beginning and ending brackets;\n  * no variable or argument declarations are necessary.\n\n\nPython is _extensible_ : if you know how to program in C it is easy to add a new built-in function or module to the interpreter, either to perform critical operations at maximum speed, or to link Python programs to libraries that may only be available in binary form (such as a vendor-specific graphics library). Once you are really hooked, you can link the Python interpreter into an application written in C and use it as an extension or command language for that application.\nBy the way, the language is named after the BBC show “Monty Python’s Flying Circus” and has nothing to do with reptiles. Making references to Monty Python skits in documentation is not only allowed, it is encouraged!\nNow that you are all excited about Python, you’ll want to examine it in some more detail. Since the best way to learn a language is to use it, the tutorial invites you to play with the Python interpreter as you read.\nIn the next chapter, the mechanics of using the interpreter are explained. This is rather mundane information, but essential for trying out the examples shown later.\nThe rest of the tutorial introduces various features of the Python language and system through examples, beginning with simple expressions, statements and data types, through functions and modules, and finally touching upon advanced concepts like exceptions and user-defined classes.\n#### Previous topic\n[The Python Tutorial](https://docs.python.org/3/tutorial/<index.html> \"previous chapter\")\n#### Next topic\n[2. Using the Python Interpreter](https://docs.python.org/3/tutorial/<interpreter.html> \"next chapter\")\n### This Page\n  * [Report a Bug](https://docs.python.org/3/tutorial/<../bugs.html>)\n  * [Show Source ](https://docs.python.org/3/tutorial/<https:/github.com/python/cpython/blob/main/Doc/tutorial/appetite.rst>)\n\n\n«\n### Navigation\n  * [index](https://docs.python.org/3/tutorial/<../genindex.html> \"General Index\")\n  * [modules](https://docs.python.org/3/tutorial/<../py-modindex.html> \"Python Module Index\") |\n  * [next](https://docs.python.org/3/tutorial/<interpreter.html> \"2. Using the Python Interpreter\") |\n  * [previous](https://docs.python.org/3/tutorial/<index.html> \"The Python Tutorial\") |\n  * ![Python logo](https://docs.python.org/3/_static/py.svg)\n  * [Python](https://docs.python.org/3/tutorial/<https:/www.python.org/>) »\n  * EnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\ndev (3.14)***********.*************.***********.***********.72.6\n  * [3.13.2 Documentation](https://docs.python.org/3/tutorial/<../index.html>) » \n  * [The Python Tutorial](https://docs.python.org/3/tutorial/<index.html>) »\n  * [1. Whetting Your Appetite](https://docs.python.org/3/tutorial/<>)\n  * | \n  * Theme  Auto Light Dark |\n\n\n© [ Copyright ](https://docs.python.org/3/tutorial/<../copyright.html>) 2001-2025, Python Software Foundation. This page is licensed under the Python Software Foundation License Version 2. Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License. See [History and License](https://docs.python.org/3/tutorial/</license.html>) for more information. The Python Software Foundation is a non-profit corporation. [Please donate.](https://docs.python.org/3/tutorial/<https:/www.python.org/psf/donations/>) Last updated on Mar 02, 2025 (11:16 UTC). [Found a bug](https://docs.python.org/3/tutorial/</bugs.html>)? Created using [Sphinx](https://docs.python.org/3/tutorial/<https:/www.sphinx-doc.org/>) 8.2.1. \n", "chunks": [{"content": "[ ![Python logo](https://docs.python.org/3/_static/py.svg) ](https://docs.python.org/3/tutorial/<https:/www.python.org/>) dev (3.14)***********.*************.***********.***********.72.6\nEnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\nTheme  Auto Light Dark\n#### Previous topic\n[The Python Tutorial](https://docs.python.org/3/tutorial/<index.html> \"previous chapter\")\n#### Next topic\n[2. Using the Python Interpreter](https://docs.python.org/3/tutorial/<interpreter.html> \"next chapter\")\n### This Page\n  * [Report a Bug](https://docs.python.org/3/tutorial/<../bugs.html>)\n  * [Show Source ](https://docs.python.org/3/tutorial/<https:/github.com/python/cpython/blob/main/Doc/tutorial/appetite.rst>)", "metadata": {"title": "1. Whetting Your Appetite — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/appetite.html", "section": "Sin título"}, "embedding": null}, {"content": "### Navigation\n  * [index](https://docs.python.org/3/tutorial/<../genindex.html> \"General Index\")\n  * [modules](https://docs.python.org/3/tutorial/<../py-modindex.html> \"Python Module Index\") |\n  * [next](https://docs.python.org/3/tutorial/<interpreter.html> \"2. Using the Python Interpreter\") |\n  * [previous](https://docs.python.org/3/tutorial/<index.html> \"The Python Tutorial\") |\n  * ![Python logo](https://docs.python.org/3/_static/py.svg)\n  * [Python](https://docs.python.org/3/tutorial/<https:/www.python.org/>) »\n  * EnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\ndev (3.14)***********.*************.***********.***********.72.6\n  * [3.13.2 Documentation](https://docs.python.org/3/tutorial/<../index.html>) » \n  * [The Python Tutorial](https://docs.python.org/3/tutorial/<index.html>) »\n  * [1. Whetting Your Appetite](https://docs.python.org/3/tutorial/<>)\n  * | \n  * Theme  Auto Light Dark |", "metadata": {"title": "1. Whetting Your Appetite — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/appetite.html", "section": "Sin título"}, "embedding": null}, {"content": "# 1. Whetting Your Appetite[¶](https://docs.python.org/3/tutorial/<#whetting-your-appetite> \"Link to this heading\")\nIf you do much work on computers, eventually you find that there’s some task you’d like to automate. For example, you may wish to perform a search-and-replace over a large number of text files, or rename and rearrange a bunch of photo files in a complicated way. Perhaps you’d like to write a small custom database, or a specialized GUI application, or a simple game.\nIf you’re a professional software developer, you may have to work with several C/C++/Java libraries but find the usual write/compile/test/re-compile cycle is too slow. Perhaps you’re writing a test suite for such a library and find writing the testing code a tedious task. Or maybe you’ve written a program that could use an extension language, and you don’t want to design and implement a whole new language for your application.\nPython is just the language for you.\nYou could write a Unix shell script or Windows batch files for some of these tasks, but shell scripts are best at moving around files and changing text data, not well-suited for GUI applications or games. You could write a C/C++/Java program, but it can take a lot of development time to get even a first-draft program. Python is simpler to use, available on Windows, macOS, and Unix operating systems, and will help you get the job done more quickly.\nPython is simple to use, but it is a real programming language, offering much more structure and support for large programs than shell scripts or batch files can offer. On the other hand, Python also offers much more error checking than C, and, being a _very-high-level language_ , it has high-level data types built in, such as flexible arrays and dictionaries. Because of its more general data types Python is applicable to a much larger problem domain than Awk or even Perl, yet many things are at least as easy in Python as in those languages.\nPython allows you to split your program into modules that can be reused in other Python programs. It comes with a large collection of standard modules that you can use as the basis of your programs — or as examples to start learning to program in Python. Some of these modules provide things like file I/O, system calls, sockets, and even interfaces to graphical user interface toolkits like Tk.\nPython is an interpreted language, which can save you considerable time during program development because no compilation and linking is necessary. The interpreter can be used interactively, which makes it easy to experiment with features of the language, to write throw-away programs, or to test functions during bottom-up program development. It is also a handy desk calculator.\nPython enables programs to be written compactly and readably. Programs written in Python are typically much shorter than equivalent C, C++, or Java programs, for several reasons:\n  * the high-level data types allow you to express complex operations in a single statement;\n  * statement grouping is done by indentation instead of beginning and ending brackets;\n  * no variable or argument declarations are necessary.", "metadata": {"title": "1. Whetting Your Appetite — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/appetite.html", "section": "1. Whetting Your Appetite[¶](https://docs.python.org/3/tutorial/<#whetting-your-appetite> \"Link to this heading\")"}, "embedding": null}, {"content": "Python is _extensible_ : if you know how to program in C it is easy to add a new built-in function or module to the interpreter, either to perform critical operations at maximum speed, or to link Python programs to libraries that may only be available in binary form (such as a vendor-specific graphics library). Once you are really hooked, you can link the Python interpreter into an application written in C and use it as an extension or command language for that application.\nBy the way, the language is named after the BBC show “Monty Python’s Flying Circus” and has nothing to do with reptiles. Making references to Monty Python skits in documentation is not only allowed, it is encouraged!\nNow that you are all excited about Python, you’ll want to examine it in some more detail. Since the best way to learn a language is to use it, the tutorial invites you to play with the Python interpreter as you read.\nIn the next chapter, the mechanics of using the interpreter are explained. This is rather mundane information, but essential for trying out the examples shown later.\nThe rest of the tutorial introduces various features of the Python language and system through examples, beginning with simple expressions, statements and data types, through functions and modules, and finally touching upon advanced concepts like exceptions and user-defined classes.\n#### Previous topic\n[The Python Tutorial](https://docs.python.org/3/tutorial/<index.html> \"previous chapter\")\n#### Next topic\n[2. Using the Python Interpreter](https://docs.python.org/3/tutorial/<interpreter.html> \"next chapter\")\n### This Page\n  * [Report a Bug](https://docs.python.org/3/tutorial/<../bugs.html>)\n  * [Show Source ](https://docs.python.org/3/tutorial/<https:/github.com/python/cpython/blob/main/Doc/tutorial/appetite.rst>)", "metadata": {"title": "1. Whetting Your Appetite — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/appetite.html", "section": "1. Whetting Your Appetite[¶](https://docs.python.org/3/tutorial/<#whetting-your-appetite> \"Link to this heading\")"}, "embedding": null}, {"content": "«\n### Navigation\n  * [index](https://docs.python.org/3/tutorial/<../genindex.html> \"General Index\")\n  * [modules](https://docs.python.org/3/tutorial/<../py-modindex.html> \"Python Module Index\") |\n  * [next](https://docs.python.org/3/tutorial/<interpreter.html> \"2. Using the Python Interpreter\") |\n  * [previous](https://docs.python.org/3/tutorial/<index.html> \"The Python Tutorial\") |\n  * ![Python logo](https://docs.python.org/3/_static/py.svg)\n  * [Python](https://docs.python.org/3/tutorial/<https:/www.python.org/>) »\n  * EnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文\ndev (3.14)***********.*************.***********.***********.72.6\n  * [3.13.2 Documentation](https://docs.python.org/3/tutorial/<../index.html>) » \n  * [The Python Tutorial](https://docs.python.org/3/tutorial/<index.html>) »\n  * [1. Whetting Your Appetite](https://docs.python.org/3/tutorial/<>)\n  * | \n  * Theme  Auto Light Dark |", "metadata": {"title": "1. Whetting Your Appetite — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/appetite.html", "section": "1. Whetting Your Appetite[¶](https://docs.python.org/3/tutorial/<#whetting-your-appetite> \"Link to this heading\")"}, "embedding": null}, {"content": "© [ Copyright ](https://docs.python.org/3/tutorial/<../copyright.html>) 2001-2025, Python Software Foundation. This page is licensed under the Python Software Foundation License Version 2. Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License. See [History and License](https://docs.python.org/3/tutorial/</license.html>) for more information. The Python Software Foundation is a non-profit corporation. [Please donate.](https://docs.python.org/3/tutorial/<https:/www.python.org/psf/donations/>) Last updated on Mar 02, 2025 (11:16 UTC). [Found a bug](https://docs.python.org/3/tutorial/</bugs.html>)? Created using [Sphinx](https://docs.python.org/3/tutorial/<https:/www.sphinx-doc.org/>) 8.2.1. \n", "metadata": {"title": "1. Whetting Your Appetite — Python 3.13.2 documentation", "url": "https://docs.python.org/3/tutorial/appetite.html", "section": "1. Whetting Your Appetite[¶](https://docs.python.org/3/tutorial/<#whetting-your-appetite> \"Link to this heading\")"}, "embedding": null}], "headers": [{"text": "Previous topic", "level": 4, "id": ""}, {"text": "Next topic", "level": 4, "id": ""}, {"text": "This Page", "level": 3, "id": ""}, {"text": "Navigation", "level": 3, "id": ""}, {"text": "1.Whetting Your Appetite¶", "level": 1, "id": ""}, {"text": "Previous topic", "level": 4, "id": ""}, {"text": "Next topic", "level": 4, "id": ""}, {"text": "This Page", "level": 3, "id": ""}, {"text": "Navigation", "level": 3, "id": ""}], "code_blocks": [], "success": true, "error_message": ""}]