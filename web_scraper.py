import asyncio
from crawl4ai import AsyncWebCrawler
from rich import print
import json
from bs4 import BeautifulSoup

async def extract_page_content(crawler, url):
    try:
        print(f"[cyan]Fetching: {url}[/cyan]")
        result = await crawler.arun(url=url)
        
        if result and result.html:
            # Extract content with structure
            content_data = {
                'url': url,
                'title': result.metadata.get('title', 'N/A') if result.metadata else 'N/A',
                'content': result.markdown,
                'code_blocks': [],
                'sections': []
            }
            
            # Extract code blocks and their descriptions
            if result.html:
                soup = BeautifulSoup(result.html, 'html.parser')
                code_blocks = soup.select('pre code')
                for block in code_blocks:
                    content_data['code_blocks'].append({
                        'language': block.get('class', ['text'])[0] if block.get('class') else 'text',
                        'code': block.get_text()
                    })
                
                # Extract section headers and their content
                sections = soup.select('h1, h2, h3')
                for section in sections:
                    content_data['sections'].append({
                        'title': section.get_text(),
                        'level': int(section.name[1])
                    })
            
            return content_data
    except Exception as e:
        print(f"[red]Error extracting content from {url}: {str(e)}[/red]")
    return None

async def main():
    # List of URLs to scrape
    urls = [
        "https://github.com/codigoencasa/bot-plugins/tree/main/packages/openai-agents"
    ]
    
    docs_data = []
    output_file = 'smolagents_docs.json'
    
    async with AsyncWebCrawler(
        browser_required=True,
        javascript_enabled=True,
        wait_until='networkidle',
        css_selectors={
            'title': 'h1, h2',
            'content': 'article',
        },
        text_mode=True,
        extract_metadata=True,
        scroll_to_load=True,
        wait_for_navigation=10000,  # 10 seconds
        max_concurrent_requests=1,  # Process one page at a time
        request_delay=3  # 3 seconds delay between requests
    ) as crawler:
        for i, url in enumerate(urls, 1):
            print(f"\n[bold blue]Processing: {url} ({i}/{len(urls)})[/bold blue]")
            page_data = await extract_page_content(crawler, url)
            
            if page_data:
                docs_data.append(page_data)
                print(f"[green]✓[/green] Successfully extracted content from: {url}")
                
                # Save progress every 5 pages
                if i % 5 == 0:
                    try:
                        with open(f'smolagents_docs_partial_{i}.json', 'w', encoding='utf-8') as f:
                            json.dump(docs_data, f, indent=2, ensure_ascii=False)
                        print(f"\n[bold green]✓ Partial documentation saved ({i} pages)[/bold green]")
                    except Exception as e:
                        print(f"[red]Error saving partial documentation: {str(e)}[/red]")

        # Save the complete documentation
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(docs_data, f, indent=2, ensure_ascii=False)
            print(f"\n[bold green]✓ Documentation saved to {output_file}[/bold green]")
            print(f"[green]Total pages processed: {len(docs_data)}[/green]")
        except Exception as e:
            print(f"[red]Error saving documentation: {str(e)}[/red]")

if __name__ == "__main__":
    asyncio.run(main())
