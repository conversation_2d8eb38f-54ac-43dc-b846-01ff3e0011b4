import { addKeyword, EVENTS } from '@builderbot/bot';

/**
 * Welcome flow - Un chatbot para responder preguntas sobre productos
 */
export const flowWelcome = addKeyword(EVENTS.WELCOME)
    .addAnswer('👋 ¡Hola! Bienvenido a nuestro chatbot.')
    .addAnswer('¿En qué puedo ayudarte hoy?')
    .addAction(async (ctx, { flowDynamic }) => {
        await flowDynamic('Estoy aquí para asistirte con Un chatbot para responder preguntas sobre productos')
    })