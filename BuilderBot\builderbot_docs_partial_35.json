[{"url": "https://www.builderbot.app/en", "title": "BuilderBot.app Create a WhatsApp Chatbot, Without Limit", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/</>)\n  * [Contribute](https://www.builderbot.app/</contribute>)\n  * [Course](https://www.builderbot.app/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/</>)\n  * [Documentation](https://www.builderbot.app/</en#>)\n  * [Support](https://www.builderbot.app/</en#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/</>)\n    * [Quickstart](https://www.builderbot.app/</quickstart>)\n    * [Concepts](https://www.builderbot.app/</concepts>)\n    * [Examples](https://www.builderbot.app/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/</add-functions>)\n    * [Context](https://www.builderbot.app/</context>)\n    * [Methods](https://www.builderbot.app/</methods>)\n    * [Events](https://www.builderbot.app/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/</deploy>)\n    * [Railway](https://www.builderbot.app/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/</contribute>)\n    * [Core](https://www.builderbot.app/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/</en#>)\n\n\n# Get started with BuilderBot\nThis is a **free** and open source framework with an intuitive and extensible way to create chatbot and smart apps that connect to different communication channels like **[Whatsapp](https://www.builderbot.app/</plugins/telegram>)** , **[Telegram](https://www.builderbot.app/</plugins/telegram>)** and others. We have made an intuitive framework so you can have your first chatbot in minutes. **[Winner of the first prize at OpenExpo 2024](https://www.builderbot.app/<https:/a.cstmapp.com/voteme/974264/712365893>)** 🏆\n## [Quick Start](https://www.builderbot.app/</en#quick-start>)\nTo create quickly with the following command\npnpmnpm\n```\npnpmcreatebuilderbot@latest\n\n```\nCopyCopied!\n[Installation and requirements](https://www.builderbot.app/</quickstart#install>)\n## [⚡ Building an AI bot](https://www.builderbot.app/</en#building-an-ai-bot>)\nIn this few minutes tutorial you can have your own chatbot with whatsapp and artificial intelligence to talk about your business.\n### Learn how to create a bot with the new open ai assistants\n[Code repository](https://www.builderbot.app/<https:/github.com/leifermendez/builderbot-openai-assistants>)\n## Quick Example\nIn this example we can see the basis of a simple bot which responds to the keywords sent by a user, the words are: `info, hello, hi`. You can see how to create the bot and implement the [flows](https://www.builderbot.app/</concepts#flow>).\nmain.tsmain.js\n```\nimport { createBot, createProvider, createFlow, addKeyword, MemoryDB } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\nconstwelcomeFlow=addKeyword<BaileysProvider,MemoryDB>(['hello','hi'])\n.addAnswer('Ey! welcome')\n.addAnswer(`Send image from URL`, { media:'https://i.imgur.com/0HpzsEm.png' })\nconstmain=async () => {\nconstadapterDB=newMemoryDB()\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(BaileysProvider)\nconst { handleCtx,httpServer } =awaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\nhttpServer(3000)\nadapterProvider.server.post('/v1/messages',handleCtx(async (bot, req, res) => {\nconst { number,message } =req.body\nawaitbot.sendMessage(number, message, {})\nreturnres.end('send')\n  }))\n}\nmain()\n\n```\nCopyCopied!\n## [Guides](https://www.builderbot.app/</en#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/</plugins>)\n## [Resources](https://www.builderbot.app/</en#resources>)\n### [Modularize](https://www.builderbot.app/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-bash", "code": "pnpm create builderbot@latest\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, create<PERSON>low, add<PERSON>eyword, MemoryDB } from '@builderbot/bot'\nimport { BaileysProvider } from '@builderbot/provider-baileys'\n\nconst welcomeFlow = addKeyword<BaileysProvider, MemoryDB>(['hello', 'hi'])\n    .addAnswer('Ey! welcome')\n    .addAnswer(`Send image from URL`, { media: 'https://i.imgur.com/0HpzsEm.png' })\n\nconst main = async () => {\n\n    const adapterDB = new MemoryDB()\n    const adapterFlow = createFlow([welcomeFlow])\n    const adapterProvider = createProvider(BaileysProvider)\n\n    const { handleCtx, httpServer } = await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n\n    httpServer(3000)\n\n    adapterProvider.server.post('/v1/messages', handleCtx(async (bot, req, res) => {\n        const { number, message } = req.body\n        await bot.sendMessage(number, message, {})\n        return res.end('send')\n    }))\n}\n\nmain()\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/quickstart", "title": "Documentation - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/quickstart#>)\n  * [Support](https://www.builderbot.app/en/</en/quickstart#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n      * [Create](https://www.builderbot.app/en/</en/quickstart#create>)\n      * [Requirements](https://www.builderbot.app/en/</en/quickstart#requirements>)\n      * [Base Example](https://www.builderbot.app/en/</en/quickstart#base-example>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/quickstart#>)\n\n\n## [Create](https://www.builderbot.app/en/</en/quickstart#create>)\nCreating a bot is as simple as running the following command and following the instructions\nPrerequisites to consider before using this tool, [Node](https://www.builderbot.app/en/<https:/nodejs.org/en>) v20 or higher and [Git](https://www.builderbot.app/en/<https:/git-scm.com/download>)\npnpmnpm\n```\npnpmcreatebuilderbot@latest\n\n```\nCopyCopied!\nor you can use the following command to create a bot with the default configuration\npnpmnpm\n```\npnpmcreatebuilderbot@latest--provider=baileys--database=memory--language=ts\n\n```\nCopyCopied!\nUse the space key to select and the enter key to confirm. The CLI performs a preliminary check of the Node and operating system version, informing you if it meets the requirements or providing you with relevant information. In addition to generating a base project for you to simply start up\nIf you have problems with your terminal try running the command with **CMD, PowerShell, GitBash** or another console you have installed.\n## [Requirements](https://www.builderbot.app/en/</en/quickstart#requirements>)\nMake sure you have installed Node version **20 or higher** , below you can see an example to check the version of node you are using.\n### Node Version\n```\nnode-v\nv20.10.0\n\n```\nCopyCopied!\n[Download node from its official website](https://www.builderbot.app/en/<https:/nodejs.org/en>)\nIt is recommended to have GIT installed for proper operation. If you are using Linux or MacOc you probably already have GIT installed by default.\n### Git Version\n```\ngit-v\ngitversionXXXX\n\n```\nCopyCopied!\n[Download GIT from its official website](https://www.builderbot.app/en/<https:/git-scm.com/downloads>)\n## [Base Example](https://www.builderbot.app/en/</en/quickstart#base-example>)\nIn this example we can see the basis of a simple bot which responds to the keywords sent by a user, the words are: `info, hello, hi`. You can see how to create the bot and implement the [flows](https://www.builderbot.app/en/</concepts#flow>).\nmain.tsmain.js\n```\nimport { createBot, createProvider, createFlow, addKeyword, MemoryDB } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\n/** send static messages */\nconstwelcomeFlow=addKeyword<BaileysProvider,MemoryDB>(['hello','hi']).addAnswer('Ey! welcome')\n/** send dynamic message from db or other sources */\nconstinfoFlow=addKeyword<BaileysProvider,MemoryDB>('info')\n.addAction(async (ctx, { flowDynamic }) => {\nawaitflowDynamic(`Welcome ${ctx.name}`)\n  })\n/** send media files */\nconstmediaFlow=addKeyword<BaileysProvider,MemoryDB>('image')\n.addAnswer(`Send Image A`, { media:'https://i.imgur.com/AsvWfUX.png' })\n.addAction(async (ctx, { flowDynamic }) => {\nawaitflowDynamic(`Welcome ${ctx.name}`)\nawaitflowDynamic(\n      [\n        {\n          body:'Send Image B',\n          media:'https://i.imgur.com/w0RtKnN.png'\n        }\n      ]\n    )\n  })\n/** initialization bot */\nconstmain=async () => {\nconstadapterDB=newMemoryDB()\nconstadapterFlow=createFlow([welcomeFlow, infoFlow, mediaFlow])\nconstadapterProvider=createProvider(BaileysProvider)\nadapterProvider.initHttpServer(3000)\nawaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\n}\nmain()\n\n```\nCopyCopied!\n## [Resources](https://www.builderbot.app/en/</en/quickstart#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-bash", "code": "pnpm create builderbot@latest\n"}, {"language": "language-bash", "code": "pnpm create builderbot@latest --provider=baileys --database=memory --language=ts\n"}, {"language": "language-bash", "code": "node -v\nv20.10.0\n"}, {"language": "language-bash", "code": "git -v\ngit version XXXX\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, create<PERSON>low, addKeyword, MemoryDB } from '@builderbot/bot'\nimport { BaileysProvider } from '@builderbot/provider-baileys'\n\n/** send static messages */\nconst welcomeFlow = addKeyword<BaileysProvider, MemoryDB>(['hello', 'hi']).addAnswer('Ey! welcome')\n\n/** send dynamic message from db or other sources */\nconst infoFlow = addKeyword<BaileysProvider, MemoryDB>('info')\n    .addAction(async (ctx, { flowDynamic }) => {\n        await flowDynamic(`Welcome ${ctx.name}`)\n    })\n\n/** send media files */\nconst mediaFlow = addKeyword<BaileysProvider, MemoryDB>('image')\n    .addAnswer(`Send Image A`, { media: 'https://i.imgur.com/AsvWfUX.png' })\n    .addAction(async (ctx, { flowDynamic }) => {\n        await flowDynamic(`Welcome ${ctx.name}`)\n        await flowDynamic(\n            [\n                {\n                    body: 'Send Image B',\n                    media: 'https://i.imgur.com/w0RtKnN.png'\n                }\n            ]\n        )\n    })\n\n/** initialization bot */\nconst main = async () => {\n\n    const adapterDB = new MemoryDB()\n    const adapterFlow = createFlow([welcomeFlow, infoFlow, mediaFlow])\n    const adapterProvider = createProvider(BaileysProvider)\n\n    adapterProvider.initHttpServer(3000)\n\n    await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n}\n\nmain()\n\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/concepts", "title": "Why BuilderBot? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/concepts#>)\n  * [Support](https://www.builderbot.app/en/</en/concepts#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n      * [Flow](https://www.builderbot.app/en/</en/concepts#flow>)\n      * [Provider](https://www.builderbot.app/en/</en/concepts#provider>)\n      * [Database](https://www.builderbot.app/en/</en/concepts#database>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/concepts#>)\n\n\n# Why BuilderBot?\n**BuilderBot is the framework** for the creation of ChatBots focused on low-frequency communication channels, whatsapp, telegram, etc. We implement an architecture focused on improving the developer experience and the reuse of logic at all times, if you need to create chatbots for whatsapp quickly, without limits and easy connection between different providers then BuilderBot is for you.\nThe library is based on three key components for its correct functioning: the Flow, in charge of building the context of the conversation and offering a friendly interface to the developer; the Provider, which acts as a connector allowing to easily switch between WhatsApp providers without the risk of affecting other parts of the bot; and the Database, which in line with this connector philosophy, facilitates changing the data persistence layer without the need to rewrite the workflow.\n## [Flow](https://www.builderbot.app/en/</en/concepts#flow>)\nRefers to creating structured sequences of interactions, as in building conversation flows. Two key methods are addKeyword and addAnswer, which allow keywords to be associated with specific responses, providing options for customizing the conversation flow.\nKeywords are the words you will use to start the flow, you can use a single word or a list of words. Example \"hello\", \"good morning\".\napp.tsapp.js\n```\nimport { addKeyword } from'@builderbot/bot'\naddKeyword(['hello','hi']).addAnswer('Ey! welcome')\n\n```\nCopyCopied!\nSome examples of how to use the addKeyword in which you can place the **keyword** or a list of **keywords** that will be used to start a conversational flow\n```\n// Example with single keyword\naddKeyword('hello').addAnswer('Ey! welcome')\n// Example with multi keywords\naddKeyword(['hello','hi']).addAnswer('Ey! welcome')\n\n```\nCopyCopied!\nFor a quick understanding of the operation we have prepared a basic example of how to implement\n[View more examples](https://www.builderbot.app/en/</uses-cases>)\n## [Provider](https://www.builderbot.app/en/</en/concepts#provider>)\nIt is a key piece used to deliver the message to the chosen supplier. In a case you are building a bot for whatsapp you should use an adapter like **[Meta](https://www.builderbot.app/en/</providers#meta>)** , **[Twilio](https://www.builderbot.app/en/</providers#twilio>)** , **[Baileys](https://www.builderbot.app/en/</providers#baileys>)** , etc or even if you want to connect to Telegram.\napp.tsprovider.wppconnect.tsprovider.meta.ts\n```\nimport { addKeyword, MemoryDB, createProvider, createFlow } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\n// ...stuff code...\nconstmain=async () => {\nawaitcreateBot({\n    database:newMemoryDB(),\n    provider:createProvider(BaileysProvider),\n    flow:createFlow([flowDemo])\n  })\n}\nmain()\n\n```\nCopyCopied!\n[More information about the providers ](https://www.builderbot.app/en/</providers>)\n## [Database](https://www.builderbot.app/en/</en/concepts#database>)\nJust as providers can be easily exchanged between adapters, we can do the same with the database. Now the important thing to understand is how it works. The main purpose of the database inside the bot is to provide the bot with a record of the different events that have occurred between different conversations.\nIt is ready to implement adapters from [Mongo](https://www.builderbot.app/en/</databases#mongo>), [MySQL](https://www.builderbot.app/en/</databases#my-sql>), [Postgres](https://www.builderbot.app/en/</databases#postgres>), among others.\napp.tsprovider.wppconnect.tsprovider.meta.ts\n```\nimport { addKeyword, MemoryDB, createProvider, createFlow } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\n// ...stuff code...\nconstmain=async () => {\nawaitcreateBot({\n    database:newMemoryDB(),\n    provider:createProvider(BaileysProvider),\n    flow:createFlow([flowDemo])\n  })\n}\nmain()\n\n```\nCopyCopied!\n[More information about the databases ](https://www.builderbot.app/en/</databases>)\n## [Guides](https://www.builderbot.app/en/</en/concepts#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/</plugins>)\n## [Resources](https://www.builderbot.app/en/</en/concepts#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "import { addKeyword } from '@builderbot/bot'\n\naddKeyword(['hello','hi']).addAnswer('Ey! welcome')\n"}, {"language": "language-ts", "code": "// Example with single keyword\naddKeyword('hello').addAnswer('Ey! welcome')\n\n// Example with multi keywords\naddKeyword(['hello','hi']).addAnswer('Ey! welcome')\n"}, {"language": "language-ts", "code": "import { addKeyword, MemoryDB, createProvider, createFlow } from '@builderbot/bot'\nimport { BaileysProvider } from '@builderbot/provider-baileys'\n\n// ...stuff code...\n\nconst main = async () => {\n    \n    await createBot({\n        database: new MemoryDB(),\n        provider: createProvider(BaileysProvider),\n        flow: createFlow([flowDemo])\n    })\n}\n\nmain()\n"}, {"language": "language-ts", "code": "import { addKeyword, MemoryDB, createProvider, createFlow } from '@builderbot/bot'\nimport { BaileysProvider } from '@builderbot/provider-baileys'\n\n// ...stuff code...\n\nconst main = async () => {\n    \n    await createBot({\n        database: new MemoryDB(),\n        provider: createProvider(BaileysProvider),\n        flow: createFlow([flowDemo])\n    })\n}\n\nmain()\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/uses-cases", "title": "Examples - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/uses-cases#>)\n  * [Support](https://www.builderbot.app/en/</en/uses-cases#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n      * [How to Update to the Latest Version](https://www.builderbot.app/en/</en/uses-cases#how-to-update-to-the-latest-version>)\n      * [My first bot](https://www.builderbot.app/en/</en/uses-cases#my-first-bot>)\n      * [Conversational historystate](https://www.builderbot.app/en/</en/uses-cases#conversational-history>)\n      * [Dynamic MessagesflowDynamic](https://www.builderbot.app/en/</en/uses-cases#dynamic-messages>)\n      * [Send File](https://www.builderbot.app/en/</en/uses-cases#send-file>)\n      * [Switch to another flowgotoFlow](https://www.builderbot.app/en/</en/uses-cases#switch-to-another-flow>)\n      * [Turn off bot a certain userstate](https://www.builderbot.app/en/</en/uses-cases#turn-off-bot-a-certain-user>)\n      * [Turn off for everyonestate](https://www.builderbot.app/en/</en/uses-cases#turn-off-for-everyone>)\n      * [Bot Self-Interactionstate](https://www.builderbot.app/en/</en/uses-cases#bot-self-interaction>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/uses-cases#>)\n\n\n# Examples\nBelow you will find different examples showing the implementation in different use cases. These examples have been compiled based on the community, feel free to post an example that you like or that you think would be useful for new people.\n## [How to Update to the Latest Version](https://www.builderbot.app/en/</en/uses-cases#how-to-update-to-the-latest-version>)\nTo ensure you're using the most up-to-date features and bug fixes, it's important to keep your BuilderBot installation current. Follow the steps below to update to the latest version. To keep your project up to date, make sure to run the command to update the core and the corresponding provider\n```\npnpminstall@builderbot/bot@latest\npnpminstall@builderbot/provider-baileys@latest\npnpminstall@builderbot/provider-wppconnect@latest\npnpminstall@builderbot/provider-venom@latest\npnpminstall@builderbot/provider-meta@latest\npnpminstall@builderbot/provider-twilio@latest\n\n```\nCopyCopied!\n## [My first bot](https://www.builderbot.app/en/</en/uses-cases#my-first-bot>)\nThe following code represents the quick use of a bot that when you type the word `hi`, greets you with a welcome message and asks you for your name and then returns a funny image\n### app.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword, MemoryDB } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\nconstwelcomeFlow=addKeyword<BaileysProvider,MemoryDB>(['hi'])\n.addAnswer('Ey! welcome')\n.addAnswer('Your name is?', { capture:true },async (ctx, { flowDynamic }) => {\nawaitflowDynamic([`nice! ${ctx.body}`,'I will send you a funny image'])\n  })\n.addAction(async(_ , {flowDynamic}) => {\nconstdataApi=awaitfetch(`https://shibe.online/api/shibes?count=1&urls=true&httpsUrls=true`)\nconst [imageUrl] =awaitdataApi.json()\nawaitflowDynamic([{body:'😜', media: imageUrl}])\n  })\n\nconstmain=async () => {\nconstadapterDB=newMemoryDB()\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(BaileysProvider)\nadapterProvider.initHttpServer(3000)\nawaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\n}\nmain()\n\n```\nCopyCopied!\nstate\n## [Conversational history](https://www.builderbot.app/en/</en/uses-cases#conversational-history>)\nOften, we will need to manage conversations and keep the context in a memory called `state` which is volatile and accessible from any function executed in a stream.\n```\nconstwelcomeFlow=addKeyword(['hello'])\n.addAnswer(\n\"¿What's your name?\",\n     {capture:true},\nasync (ctx, { flowDynamic, state }) => {\nawaitstate.update({ name:ctx.body })\nawaitflowDynamic('Thanks for giving me your name!')\n     }\n   )\n.addAnswer(\n'¿How old are you?',\n      {capture:true},\nasync (ctx, { flowDynamic, state }) => {\nconstname=state.get('name')\nawaitstate.update({ age:ctx.body })\nawaitflowDynamic(`Thanks for sharing your age! ${name}`)\n     }\n   )\n.addAnswer('Here is your data:',null,async (_, { flowDynamic, state }) => {\nconstmyState=state.getMyState()\nawaitflowDynamic(`Name: ${myState.name} Age: ${myState.age}`)\n   })\n\n```\nCopyCopied!\nflowDynamic\n## [Dynamic Messages](https://www.builderbot.app/en/</en/uses-cases#dynamic-messages>)\nIn other occasions we need to send messages in a dynamic way of data that can be variable, below you can see an example of how you should do it and how you should NOT do it.\n❌ Avoid it this, does not work because addAnswer serializes the content at the start of execution.\n```\nlet name =''\nconstflow=addKeyword('hello')\n.addAnswer(`What is your name?`, { capture:true },async (ctx) => {\n    name =ctx.body\n  })\n.addAnswer(`Your name is: ${name}`)\n\n```\nCopyCopied!\nIf you want to send a dynamic message use flowDynamic.\n```\nconstflow=addKeyword('hello')\n.addAnswer(`What is your name?`, { capture:true },async (ctx, { state }) => {\nawaitstate.update({ name:ctx.body })\n  })\n.addAction(async (ctx, { state, flowDynamic }) => {\nconstname=state.get('name')\nawaitflowDynamic(`Your name is: ${name}`)\n  })\n\n```\nCopyCopied!\n## [Send File](https://www.builderbot.app/en/</en/uses-cases#send-file>)\nWhen you want to **send an image, audio** , file or any other file you can do it this way. It is **important** to note that the URL must be **publicly accessible**.\n```\nconstflow=addKeyword('hello')\n.addAnswer(`Send image from URL`,\n    { media:'https://i.imgur.com/0HpzsEm.png' }\n  )\n.addAnswer(`Send video from Local`,\n    { media:join(process.cwd(),'assets','sample.png') }\n  )\n.addAnswer(`Send video from URL`,\n    { media:'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4' }\n  )\n.addAnswer(`Send file from URL`,\n    { media:'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf' }\n  )\n\n```\nCopyCopied!\nOther ways to use when the route is coming from a dynamic data source\n```\nconstflow=addKeyword('hello')\n.addAction(async (_,{flowDynamic}) => {\n// ...db get source...\nawaitflowDynamic([\n      {body:'This is an image', media:'https://i.imgur.com/0HpzsEm.png'}\n    ])\nawaitflowDynamic([\n      {body:'This is a video', media:'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4'}\n    ])\n  })\n\n```\nCopyCopied!\nIf you need to send a file that is stored locally you can do that too. The use of `join` is recommended to ensure correct directory concatenation.\n```\nconstflow=addKeyword('hello')\n.addAction(async (_,{flowDynamic}) => {\nconstpathLocal=join('assets','doc.pdf')\n// pathLocal = c:/doc.pdf\nawaitflowDynamic([\n      {body:'This is a video', media: pathLocal }\n    ])\n  })\n\n```\nCopyCopied!\ngotoFlow\n## [Switch to another flow](https://www.builderbot.app/en/</en/uses-cases#switch-to-another-flow>)\nIf you want to divert a conversational flow to another logic flow based on a response input you can do it in this way:\n```\n\nconstflowToA=addKeyword(EVENTS.ACTION).addAnswer('Here we have Option A!')\nconstflowToB=addKeyword(EVENTS.ACTION).addAnswer('Here we have Option B!')\nconstflowToC=addKeyword(EVENTS.ACTION).addAnswer('Here we have Option C!')\nconstflowDefault=addKeyword(EVENTS.ACTION).addAnswer(\"We don't have that Option 🤔\")\nconstflow=addKeyword('order')\n.addAnswer(\n    [\n`Which one is the best option for you?`,\n`Type **A**`,\n`Type **B**`,\n`Type **C**`,\n    ],\n    { capture:true }\n  )\n.addAnswer(`Thanks for you answer`,async (ctx, {gotoFlow})=> {\nconstuserAnswer=ctx.body\nif(userAnswer ==='A'){\nreturngotoFlow(flowToA)\n    } \nif(userAnswer ==='B'){\nreturngotoFlow(flowToB)\n    } \nif(userAnswer ==='C'){\nreturngotoFlow(flowToC)\n    } \nreturngotoFlow(flowDefault)\n  })\n.addAnswer(`this message will not be sent`)\n\n```\nCopyCopied!\n❌ This does not work, the invocation of the gotoFlow function must necessarily include a return.\n```\n\n//...Previous code...\n....addAnswer(`Thanks for you answer`,async (ctx, {gotoFlow})=> {\ngotoFlow(flowToA)\n  })\n.addAnswer(`this message will not be sent`)\n\n```\nCopyCopied!\nThis does work\n```\n\n//...Previous code...\n....addAnswer(`Thanks for you answer`,async (ctx, {gotoFlow})=> {\nreturngotoFlow(flowToA)\n  })\n.addAnswer(`this message will not be sent`)\n\n```\nCopyCopied!\nstate\n## [Turn off bot a certain user](https://www.builderbot.app/en/</en/uses-cases#turn-off-bot-a-certain-user>)\nSometimes we will need to turn off the bot for a certain user, so that we can have a conversation with the client without the bot interfering.\n```\nconstflow=addKeyword<BaileysProvider>('magic keyword')\n.addAction(async (_, { state, endFlow }) => {\nconstbotOffForThisUser=state.get<boolean>('botOffForThisUser')\nawaitstate.update({botOffForThisUser:!botOffForThisUser})\nif(botOffForThisUser) returnendFlow()\n })\n.addAnswer('Hello!')\n\n```\nCopyCopied!\nstate\n## [Turn off for everyone](https://www.builderbot.app/en/</en/uses-cases#turn-off-for-everyone>)\nSometimes we will need to disable the bot for all people, without the need to shut down the server or stop the script.\n```\nconstflow=addKeyword<BaileysProvider>('botoff')\n.addAction(async (_, { globalState, endFlow }) => {\nconstbotOffForEveryOne=globalState.get<boolean>('botOffForEveryOne')\nawaitglobalState.update({botOffForEveryOne:!botOffForEveryOne})\nif(botOffForEveryOne) returnendFlow()\n })\n.addAnswer('Hello!')\n\n```\nCopyCopied!\nstate\n## [Bot Self-Interaction](https://www.builderbot.app/en/</en/uses-cases#bot-self-interaction>)\nIn certain scenarios, it is necessary for the bot's phone number (host) to be able to interact within logical flows. To achieve this, we have several configurable options:\n  * **host:** This is used when you want the bot to be able to respond to messages in the same chat with itself. For example, if the bot's number is 0000, it will be able to send and receive messages to/from 0000.\n  * **both:** This option allows both the bot and you (the developer/administrator) to intervene in the chat of a person interacting with the bot.\n  * **none:** (default option) Only allows interaction between the user and the bot, without intervention from the host number.\n\n\n### app.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword, MemoryDB } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\nconstmain=async () => {\nconstadapterDB=newMemoryDB()\nconstadapterFlow=createFlow([...])\nconstadapterProvider=createProvider(BaileysProvider, {\n     writeMyself:'host'as'none'|'host'|'both'\n  })\nadapterProvider.initHttpServer(3000)\nawaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\n}\nmain()\n\n```\nCopyCopied!\n## [Guides](https://www.builderbot.app/en/</en/uses-cases#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/</plugins>)\n## [Resources](https://www.builderbot.app/en/</en/uses-cases#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-bash", "code": "pnpm install @builderbot/bot@latest \npnpm install @builderbot/provider-baileys@latest\npnpm install @builderbot/provider-wppconnect@latest\npnpm install @builderbot/provider-venom@latest\npnpm install @builderbot/provider-meta@latest\npnpm install @builderbot/provider-twilio@latest\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, create<PERSON>low, addKeyword, MemoryDB } from '@builderbot/bot'\nimport { BaileysProvider } from '@builderbot/provider-baileys'\n\nconst welcomeFlow = addKeyword<BaileysProvider, MemoryDB>(['hi'])\n    .addAnswer('Ey! welcome')\n    .addAnswer('Your name is?', { capture: true }, async (ctx, { flowDynamic }) => {\n        await flowDynamic([`nice! ${ctx.body}`,'I will send you a funny image'])\n    })\n    .addAction(async(_ , {flowDynamic}) => {\n        const dataApi = await fetch(`https://shibe.online/api/shibes?count=1&urls=true&httpsUrls=true`)\n        const [imageUrl] = await dataApi.json()\n        await flowDynamic([{body:'😜', media: imageUrl}])\n    })\n\n\nconst main = async () => {\n    const adapterDB = new MemoryDB()\n    const adapterFlow = createFlow([welcomeFlow])\n    const adapterProvider = createProvider(BaileysProvider)\n\n    adapterProvider.initHttpServer(3000)\n\n    await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n}\n\nmain()\n"}, {"language": "language-ts", "code": "  const welcomeFlow = addKeyword(['hello'])\n      .addAnswer(\n          \"¿What's your name?\",\n          {capture: true},\n          async (ctx, { flowDynamic, state }) => {\n              await state.update({ name: ctx.body })\n              await flowDynamic('Thanks for giving me your name!')\n          }\n      )\n      .addAnswer(\n          '¿How old are you?',\n           {capture: true},\n          async (ctx, { flowDynamic, state }) => {\n              const name = state.get('name')\n              await state.update({ age: ctx.body })\n              await flowDynamic(`Thanks for sharing your age! ${name}`)\n          }\n      )\n      .addAnswer('Here is your data:', null, async (_, { flowDynamic, state }) => {\n          const myState = state.getMyState()\n          await flowDynamic(`Name: ${myState.name} Age: ${myState.age}`)\n      })\n"}, {"language": "language-ts", "code": "let name = ''\n\nconst flow = addKeyword('hello')\n    .addAnswer(`What is your name?`, { capture: true }, async (ctx) => {\n        name = ctx.body\n    })\n    .addAnswer(`Your name is: ${name}`)\n"}, {"language": "language-ts", "code": "const flow = addKeyword('hello')\n    .addAnswer(`What is your name?`, { capture: true }, async (ctx, { state }) => {\n        await state.update({ name: ctx.body })\n    })\n    .addAction(async (ctx, { state, flowDynamic }) => {\n        const name = state.get('name')\n        await flowDynamic(`Your name is: ${name}`)\n    })\n"}, {"language": "language-ts", "code": "const flow = addKeyword('hello')\n    .addAnswer(`Send image from URL`, \n        { media: 'https://i.imgur.com/0HpzsEm.png' }\n    )\n    .addAnswer(`Send video from Local`, \n        { media: join(process.cwd(), 'assets', 'sample.png') }\n    )\n    .addAnswer(`Send video from URL`, \n        { media: 'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4' }\n    )\n    .addAnswer(`Send file from URL`, \n        { media: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf' }\n    )\n"}, {"language": "language-ts", "code": "const flow = addKeyword('hello')\n    .addAction(async (_,{flowDynamic}) => {\n        // ...db get source...\n        await flowDynamic([\n            {body:'This is an image', media:'https://i.imgur.com/0HpzsEm.png'}\n        ])\n        await flowDynamic([\n            {body:'This is a video', media:'https://media.giphy.com/media/KWZKwdBC2ODWlQ8kgt/giphy.mp4'}\n        ])\n    })\n\n"}, {"language": "language-ts", "code": "const flow = addKeyword('hello')\n    .addAction(async (_,{flowDynamic}) => {\n        const pathLocal = join('assets','doc.pdf')\n        // pathLocal = c:/doc.pdf\n        await flowDynamic([\n            {body:'This is a video', media: pathLocal }\n        ])\n    })\n\n"}, {"language": "language-ts", "code": "\nconst flowToA = addKeyword(EVENTS.ACTION).addAnswer('Here we have Option A!')\nconst flowToB = addKeyword(EVENTS.ACTION).addAnswer('Here we have Option B!')\nconst flowToC = addKeyword(EVENTS.ACTION).addAnswer('Here we have Option C!')\n\nconst flowDefault = addKeyword(EVENTS.ACTION).addAnswer(\"We don't have that Option 🤔\")\n\nconst flow = addKeyword('order')\n    .addAnswer(\n        [\n            `Which one is the best option for you?`,\n            `Type **A**`,\n            `Type **B**`,\n            `Type **C**`,\n        ], \n        { capture: true }\n    )\n    .addAnswer(`Thanks for you answer`,async (ctx, {gotoFlow})=> {\n        const userAnswer = ctx.body\n        if(userAnswer === 'A'){\n            return gotoFlow(flowToA)\n        } \n        if(userAnswer === 'B'){\n            return gotoFlow(flowToB)\n        } \n        if(userAnswer === 'C'){\n            return gotoFlow(flowToC)\n        } \n        return gotoFlow(flowDefault)\n\n    })\n    .addAnswer(`this message will not be sent`)\n"}, {"language": "language-ts", "code": "\n//...Previous code...\n    ....addAnswer(`Thanks for you answer`,async (ctx, {gotoFlow})=> {\n        gotoFlow(flowToA)\n\n    })\n    .addAnswer(`this message will not be sent`)\n"}, {"language": "language-ts", "code": "\n//...Previous code...\n    ....addAnswer(`Thanks for you answer`,async (ctx, {gotoFlow})=> {\n        return gotoFlow(flowToA)\n\n    })\n    .addAnswer(`this message will not be sent`)\n"}, {"language": "language-ts", "code": "const flow = addKeyword<BaileysProvider>('magic keyword')\n  .addAction(async (_, { state, endFlow }) => {\n      const botOffForThisUser = state.get<boolean>('botOffForThisUser')\n      await state.update({botOffForThisUser:!botOffForThisUser})\n      if(botOffForThisUser) return endFlow()\n  })\n  .addAnswer('Hello!')\n"}, {"language": "language-ts", "code": "const flow = addKeyword<BaileysProvider>('botoff')\n  .addAction(async (_, { globalState, endFlow }) => {\n  const botOffForEveryOne = globalState.get<boolean>('botOffForEveryOne')\n    await globalState.update({botOffForEveryOne:!botOffForEveryOne})\n    if(botOffForEveryOne) return endFlow()\n  })\n  .addAnswer('Hello!')\n  \n"}, {"language": "language-ts", "code": "import { createBot, createProvider, create<PERSON>low, addKeyword, MemoryDB } from '@builderbot/bot'\nimport { BaileysProvider } from '@builderbot/provider-baileys'\n\nconst main = async () => {\n    const adapterDB = new MemoryDB()\n    const adapterFlow = createFlow([...])\n    const adapterProvider = createProvider(BaileysProvider, {\n          writeMyself: 'host' as 'none' | 'host' | 'both'\n    })\n\n    adapterProvider.initHttpServer(3000)\n\n    await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n}\n\nmain()\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/add-functions", "title": "Documentation - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/add-functions#>)\n  * [Support](https://www.builderbot.app/en/</en/add-functions#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n      * [Add keyword](https://www.builderbot.app/en/</en/add-functions#add-keyword>)\n      * [Add answer](https://www.builderbot.app/en/</en/add-functions#add-answer>)\n      * [Add Action](https://www.builderbot.app/en/</en/add-functions#add-action>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/add-functions#>)\n\n\n## [Add keyword](https://www.builderbot.app/en/</en/add-functions#add-keyword>)\n**`addKeyword`**is the starting point of a conversation flow, just need to pass an**array of keywords** , when someone text those keywords in the chatbot, the function will trigger the predefined dialog you have set up.\nIn the following example is stablished a conversation flow that received the keywords 'hello' and 'hi', then the chatbot will send a message using `addAnswer` with the text 'Hi, welcome to my amazing shop, how can I help you?' and 'The shop offers: shoes, shirts, pants, and hats'.\nThe addKeywords function also has a number of proprieties that will help you solve different use cases. Some of these functions are: [regex](https://www.builderbot.app/en/</en/add-functions#regex>), [sensitive](https://www.builderbot.app/en/</en/add-functions#sensitive>)\napp.tsapp.js\n```\nimport { addKeyword } from'@builderbot/bot';\nconstmainFlow=addKeyword(['hello','hi'])\n.addAnswer(['Hi, welcome to my amazing shop','how can I help you?'])\n.addAnswer(['The shop has: ','shoes, shirts, pants, and hats'])\n\n```\nCopyCopied!\nsay (hello/hi)\nHi, welcome to my amazing shop, how can I help you?\nThe shop has:, shoes, shirts, pants, and hats\nuser\nBot\nBot\nuser\nsay (hello)\nHi!, Do you know 4+4?\nTotal: 8\nuser\nBot\nBot\nuser\nThe conversation flow can be limited to a specific **keyword** , in example the word 'purchase' to trigger the next dialog\napp.tsapp.js\n```\nimport { addKeyword } from'@builderbot/bot';\nconstmainFlow=addKeyword('buy')\n.addAnswer(['Great! ','What do you want to buy?'])\n\n```\nCopyCopied!\naddKeyword\n## [Regex](https://www.builderbot.app/en/</en/add-functions#regex>)\nThe chatbot supports regex validation, in the following example an object is passed as the second parameter to the addKeyword function, the object has a property regex set to true and the keywords property is a regex pattern.\napp.tsapp.js\n```\nimport { addKeyword } from'@builderbot/bot';\nconstREGEX_GMAIL_EMAIL= /(\\w+)@gmail\\.com/g;\nconstmainFlow=addKeyword(REGEX_GMAIL_EMAIL,\n   { regex:true })\n.addAnswer(`Thanks for the gmail address`)\n\n```\nCopyCopied!\naddKeyword\n## [Sensitive](https://www.builderbot.app/en/</en/add-functions#sensitive>)\nThe chatbot detects an specific keyword in any part of a message, but passing the `sensitive` property as `true` the chatbot will trigger the dialog if the user write down the exact same **keyword**.\napp.tsapp.js\n```\nimport { addKeyword } from'@builderbot/bot';\nconstmainFlow=addKeyword('buy', { sensitive:true })\n.addAnswer(['Great! ','What do you want to buy?'])\n\n```\nCopyCopied!\n## [Add answer](https://www.builderbot.app/en/</en/add-functions#add-answer>)\n**`addAnswer`**is used to send a message to the user, it can be a simple text or a file attached. In the following example is stablished a conversation flow that received the keyword 'hello', then the chatbot will send a message using`addAnswer` with the text **'Hi, welcome to my amazing shop, how can I help you?'**\napp.tsapp.js\n```\nimport { addKeyword, addAnswer } from'@builderbot/bot';\nconstmainFlow=addKeyword('hello')\n.addAnswer('Hi, welcome to my amazing shop, how can I help you?')\n\n```\nCopyCopied!\nThere are different settings for sending messages, some functions such as sending files, images, video, delays, line breaks: [line breaks](https://www.builderbot.app/en/</add-functions#send-consecutive-messages>), [consecutive](https://www.builderbot.app/en/</add-functions#send-consecutive-messages>), [delay](https://www.builderbot.app/en/</add-functions#message-with-delay>) , [callback](https://www.builderbot.app/en/</add-functions#message-with-callback>)\naddAnswer\n## [Consecutive Messages](https://www.builderbot.app/en/</en/add-functions#consecutive-messages>)\nThe chatbot can send multiple messages just adding the addAnswer function one after another. In the following example the chatbot will send (3) messages: `Hi`, `Welcome to my amazing shop`, `how can I help you?` in that order.\napp.tsapp.js\n```\nimport { addKeyword, addAnswer } from'@builderbot/bot';\nconstmainFlow=addKeyword('hello')\n.addAnswer('Hi')\n.addAnswer('Welcome to my amazing shop')\n.addAnswer('how can I help you?')\n\n```\nCopyCopied!\naddAnswer\n## [Message with line breaks](https://www.builderbot.app/en/</en/add-functions#message-with-line-breaks>)\nWhenever you need to send a message with line breaks, you can use an array of string as you can see in the following example.\napp.tsapp.js\n```\nimport { addKeyword, addAnswer } from'@builderbot/bot';\nconstmainFlow=addKeyword('hello')\n.addAnswer([\n'Hi','Welcome to my amazing shop',\n'how can I help you?'\n   ])\n\n```\nCopyCopied!\naddAnswer\n## [Message with delay](https://www.builderbot.app/en/</en/add-functions#message-with-delay>)\nThe chatbot can send a message with a `delay`, just add the addAnswer function with the delay property set to the amount of milliseconds you want to wait before sending the message.\napp.tsapp.js\n```\nimport { addKeyword, addAnswer } from'@builderbot/bot';\nconstmainFlow=addKeyword('hello')\n.addAnswer('This message will after 2 seconds',\n  { delay:2000 }\n )\n\n```\nCopyCopied!\n## [Message with callback](https://www.builderbot.app/en/</en/add-functions#message-with-callback>)\nWhen using callback functions in an addAnswers the operation prioritizes the sending of the message and then the execution of the function.\napp.tsapp.js\n```\nimport { addKeyword, addAnswer } from'@builderbot/bot';\nconstmainFlow=addKeyword('hello')\n.addAnswer('Hi!, Do you know 4+4?',null,async (_, {flowDynamic}) => {\nconstsum=4+4\nawaitflowDynamic(`Total: ${sum}`)\n   })\n.addAction(async (_, {flowDynamic}) => {\nawaitflowDynamic(`Other message`)\n   })\n\n```\nCopyCopied!\n## [Add Action](https://www.builderbot.app/en/</en/add-functions#add-action>)\n**`addAction`**is used to define specific actions as a response when a whatsapp message has been received, this function allows to trigger conversation flows based on the user's input and define how the chatbot should act.\nIn the folowing example you can see how you can trigger a function when the user sends a message with the keyword 'buy'.\napp.tsapp.js\n```\nimport { addKeyword, addAction } from'@builderbot/bot';\nconstmainFlow=addKeyword('buy')\n.addAnswer('Great! What do you want to buy?')\n.addAction(async (_, { flowDynamic }):void=> {\nreturnconsole.log('The user wants to buy something');\n   })\n\n```\nCopyCopied!\nYou can use the `addAction` function with `capture` as you can see in the example below\napp.tsapp.js\n```\nimport { addKeyword, addAction } from'@builderbot/bot';\nconstmainFlow=addKeyword(['Hello','Hi'])\n.addAction(async (_, { flowDynamic }):void=> {\nreturnflowDynamic('Hi! how can I help you?');\n   })\n.addAction({ capture:true },async (ctx, { flowDynamic, state }):void=> {\nawaitstate.udpate({ name:ctx.body})\nreturnconsole.flowDynamic(`The user said: ${ctx.body}`);\n   })\n\n```\nCopyCopied!\n## [Guides](https://www.builderbot.app/en/</en/add-functions#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/</plugins>)\n## [Resources](https://www.builderbot.app/en/</en/add-functions#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "  import { addKeyword } from '@builderbot/bot';\n\n  const mainFlow = addKeyword(['hello', 'hi'])\n      .addAnswer(['Hi, welcome to my amazing shop', 'how can I help you?'])\n      .addAnswer(['The shop has: ', 'shoes, shirts, pants, and hats'])\n"}, {"language": "language-ts", "code": "  import { addKeyword } from '@builderbot/bot';\n\n  const mainFlow = addKeyword('buy')\n      .addAnswer(['Great! ', 'What do you want to buy?'])\n"}, {"language": "language-ts", "code": "  import { addKeyword } from '@builderbot/bot';\n\n  const REGEX_GMAIL_EMAIL = /(\\w+)@gmail\\.com/g;\n\n  const mainFlow = addKeyword(REGEX_GMAIL_EMAIL, \n      { regex: true })\n      .addAnswer(`Thanks for the gmail address`)\n"}, {"language": "language-ts", "code": "  import { addKeyword } from '@builderbot/bot';\n\n  const mainFlow = addKeyword('buy', { sensitive: true })\n      .addAnswer(['Great! ', 'What do you want to buy?'])\n"}, {"language": "language-ts", "code": "  import { addKeyword, addAnswer } from '@builderbot/bot';\n\n  const mainFlow = addKeyword('hello')\n      .addAnswer('Hi, welcome to my amazing shop, how can I help you?')\n"}, {"language": "language-ts", "code": "  import { addKeyword, addAnswer } from '@builderbot/bot';\n\n  const mainFlow = addKeyword('hello')\n      .addAnswer('Hi')\n      .addAnswer('Welcome to my amazing shop')\n      .addAnswer('how can I help you?')\n"}, {"language": "language-ts", "code": "  import { addKeyword, addAnswer } from '@builderbot/bot';\n\n  const mainFlow = addKeyword('hello')\n      .addAnswer([\n        'Hi', 'Welcome to my amazing shop', \n        'how can I help you?'\n      ])\n"}, {"language": "language-ts", "code": "import { addKeyword, addAnswer } from '@builderbot/bot';\n\nconst mainFlow = addKeyword('hello')\n  .addAnswer('This message will after 2 seconds',\n    { delay: 2000 }\n  )\n"}, {"language": "language-ts", "code": "  import { addKeyword, addAnswer } from '@builderbot/bot';\n\n  const mainFlow = addKeyword('hello')\n      .addAnswer('Hi!, Do you know 4+4?', null, async (_, {flowDynamic}) => {\n          const sum = 4 + 4\n          await flowDynamic(`Total: ${sum}`)\n      })\n      .addAction(async (_, {flowDynamic}) => {\n          await flowDynamic(`Other message`)\n      })\n"}, {"language": "language-ts", "code": "  import { addKeyword, addAction } from '@builderbot/bot';\n\n  const mainFlow = addKeyword('buy')\n      .addAnswer('Great! What do you want to buy?')\n      .addAction(async (_, { flowDynamic }): void => {\n          return console.log('The user wants to buy something');\n      })\n"}, {"language": "language-ts", "code": "  import { addKeyword, addAction } from '@builderbot/bot';\n\n  const mainFlow = addKeyword(['Hello', 'Hi'])\n      .addAction(async (_, { flowDynamic }): void => {\n          return flowDynamic('Hi! how can I help you?');\n      })\n      .addAction({ capture: true }, async (ctx, { flowDynamic, state }): void => {\n          await state.udpate({ name: ctx.body})\n          return console.flowDynamic(`The user said: ${ctx.body}`);\n      })\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/context", "title": "Context - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/context#>)\n  * [Support](https://www.builderbot.app/en/</en/context#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/context#>)\n\n\n# Context\nThe \"context\" object is a key component within the framework that provides developers with access to essential information about the ongoing interaction between the user and the bot. This information includes default properties like `from` (representing the sender) and `body` (containing the message or input from the user).\nWhen utilizing the framework's [addAnswer](https://www.builderbot.app/en/</add-functions#add-keyword>) and [addAction](https://www.builderbot.app/en/</add-functions#add-action>) methods, the context object is passed as the first argument in the callback functions. This allows developers to access and manipulate the context's properties dynamically, enabling tailored responses and actions based on the specifics of each user interaction.\nflow-addKeyword.tsflow-addAction.ts\n```\nimport { addKeyword } from'@builderbot/bot';\nconstflowA=addKeyword('register')\n.addAnswer('What is your name?', { capture:true },async (ctx, { state }) => {\nconstresponseName=ctx.body\nconstnameFrom=ctx.name\nconstnumberFrom=ctx.from\nconsole.log(`Other properties:`, ctx)\n  })\nexportdefault flowA\n\n```\nCopyCopied!\nIt is important to note that the properties you will always have available are from, body, name but you will also have many more different properties depending on the [provider](https://www.builderbot.app/en/</concepts#provider>).\n## [Guides](https://www.builderbot.app/en/</en/context#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/</plugins>)\n## [Resources](https://www.builderbot.app/en/</en/context#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "  import { addKeyword } from '@builderbot/bot';\n\n  const flowA = addKeyword('register')\n    .addAnswer('What is your name?', { capture: true }, async (ctx, { state }) => {\n      const responseName = ctx.body\n      const nameFrom = ctx.name\n      const numberFrom = ctx.from\n\n      console.log(`Other properties:`, ctx)\n    })\n\n  export default flowA\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/methods", "title": "Methods - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/methods#>)\n  * [Support](https://www.builderbot.app/en/</en/methods#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n      * [State](https://www.builderbot.app/en/</en/methods#state>)\n      * [GlobalState](https://www.builderbot.app/en/</en/methods#global-state>)\n      * [FlowDynamic](https://www.builderbot.app/en/</en/methods#flow-dynamic>)\n      * [FallBack](https://www.builderbot.app/en/</en/methods#fall-back>)\n      * [EndFlow](https://www.builderbot.app/en/</en/methods#end-flow>)\n      * [GotoFlow](https://www.builderbot.app/en/</en/methods#goto-flow>)\n      * [Blacklist](https://www.builderbot.app/en/</en/methods#blacklist>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/methods#>)\n\n\n# Methods\nInside each addAction or addAnswer we can execute logic and we can make use of some methods that will facilitate the development.\n## [State](https://www.builderbot.app/en/</en/methods#state>)\nIn many occasions you will need to share data between flows and no matter if you have modularized your project in different files you can use state in the callback function to be able to access the individual state of each user.\nLet's imagine the case where you have two flows. Flow A: In charge of collecting user data. Flow B: Responsible for generating a record in the database. but both flows are independent files\n**Remember** that the state is independent per conversation between user and bot.\napp.tsflow-a.tsflow-b.ts\n```\nimport { createFlow, MemoryDB, createProvider } from'@builderbot/bot';\n// ...\nimport flowA from'./flows/flow-a'\nimport flowB from'./flows/flow-b'\nconstmain=async () => {\nconstadapterDB=newMemoryDB()\nconstadapterFlow=createFlow([flowA, flowB])\nconstadapterProvider=createProvider(BaileysProvider)\nadapterProvider.initHttpServer(3000)\nawaitcreateBot({\n     flow: adapterFlow,\n     provider: adapterProvider,\n     database: adapterDB,\n   })\n }\n\n```\nCopyCopied!\nEach conversation history with the user is totally independent, in this way we avoid mixing conversations.\nIt is important to understand that the state is volatile, it does not permeate anywhere. If you are worried about the performance level, you can see it [here](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp/blob/d8eb94cae36fb233f5283c8d44c6c0307d26a978/packages/bot/src/context/stateClass.ts#L7>), but I'll save you time by telling you that it is a [Map](https://www.builderbot.app/en/<https:/developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map>)\n## [GlobalState](https://www.builderbot.app/en/</en/methods#global-state>)\nVery similar to [state](https://www.builderbot.app/en/</methods#state>) there is another method called GlobalState to share a global state of the bot between different flows. The main use for this method is to share data that can change and that every conversation between bot and user can access.\nBelow you can see a practical example where we use the globalState to use it as a switch to allow or disallow the bot to respond no matter who writes to it.\napp.tsflow-on-off.tsflow-welcome.ts\n```\nimport { createFlow, MemoryDB, createProvider } from'@builderbot/bot';\n// ...\nimport flowWelcome from'./flows/flow-welcome'\nimport flowOnOff from'./flows/flow-on-off'\nconstmain=async () => {\nconstadapterDB=newMemoryDB()\nconstadapterFlow=createFlow([flowWelcome, flowOnOff])\nconstadapterProvider=createProvider(BaileysProvider)\nadapterProvider.initHttpServer(3000)\nawaitcreateBot({\n     flow: adapterFlow,\n     provider: adapterProvider,\n     database: adapterDB,\n   })\n }\n\n```\nCopyCopied!\nInicia flow-a\nInicia flow-b\nflow-welcome.ts\nflow-a.ts\nflow-b.ts\n## [State/GlobalState Options](https://www.builderbot.app/en/</en/methods#state-global-state-options>)\nBoth methods such as state and global state contain similar options and funcinalides, which depending on the use case can be very useful.\n## [Clear](https://www.builderbot.app/en/</en/methods#clear>)\nThis method allows the state to be cleaned either globally or independently. It technically clears the Map.\nAvailable in: **state** , **globalState**\n```\n.addAnswer('..',null,async (_, { state }) => {\nstate.clear()\n})\n.addAction(async (_, { globalState }) => {\nglobalState.clear()\n})\n\n```\nCopyCopied!\n## [Update](https://www.builderbot.app/en/</en/methods#update>)\nTo add or update a value in the state we have available the update method. This method receives an object which if the value does not exist creates it and returns a new object with all the values. It is important to understand that it is a promise to avoid rare behavior by adding the `await`\nAvailable in: **state** , **globalState**\n```\n.addAnswer('..',null,async (_, { state }) => {\nawaitstate.update({name:'Joe', age:'33'})\nawaitstate.update({email:'<EMAIL>'})\n})\n.addAction(async (_, { globalState }) => {\nawaitglobalState.update({name:'Joe', age:'33'})\nawaitglobalState.update({email:'<EMAIL>'})\n})\n\n```\nCopyCopied!\n## [Get](https://www.builderbot.app/en/</en/methods#get>)\nWhen we need to retrieve the state values we can do it individually by calling by the property name as follows.\nAvailable in: **state** , **globalState**\n```\n.addAnswer('..',null,async (_, { state }) => {\nstate.get('propertyName')\n})\n.addAction(async (_, { globalState }) => {\nglobalState.get('propertyName')\n})\n\n```\nCopyCopied!\n## [GetMyState](https://www.builderbot.app/en/</en/methods#get-my-state>)\nAnother way to retrieve the entire state object belonging to a user-independent conversation is by using getMyState\nAvailable in: **state**\n```\n.addAnswer('..',null,async (_, { state }) => {\nstate.getMyState()\n})\n\n```\nCopyCopied!\n## [GetAllState](https://www.builderbot.app/en/</en/methods#get-all-state>)\nWhen we are working with the globalState and we want to retrieve all the properties object with their respective values you can use getAllState\nAvailable in: **globalState**\n```\n.addAnswer('..',null,async (_, { globalState }) => {\nglobalState.getAllState()\n})\n\n```\nCopyCopied!\n## [FlowDynamic](https://www.builderbot.app/en/</en/methods#flow-dynamic>)\nMany times you will need to send messages coming from an API call or dynamic data from data base or from processes. In this case you should use `flowDynamic`.\nflow-standalone.tsflow-multiline.tsflow-list.ts\n```\nimport { addKeyword } from'@builderbot/bot';\nconstflowStandAlone=addKeyword('register')\n.addAnswer('What is your name?', { capture:true },async (ctx, { flowDynamic }) => {\nconstresponseName=ctx.body\n//.... db.insert({name:responseName})\nawaitflowDynamic(`Thanks for register ${responseName}`)\n  })\nexportdefault flowStandAlone\n\n```\nCopyCopied!\nIt is NOT recommended to send many messages in a row because the [provider](https://www.builderbot.app/en/</concepts#provider>) may determine that it is spam.\nIf you want to send a list of products it is recommended to send a few products between 4 to 8 and you can ask the user what category of products and other details to filter and be able to respond with the ideal product list.\n## [FlowDynamic Options](https://www.builderbot.app/en/</en/methods#flow-dynamic-options>)\n  * Name\n    `body`\nType\n    string\nDescription\n    \nYou can send a message inside an object using the body property. It is ideal when you need to send a message apart from the message to send a media or to place a delay.\n  * Name\n    `delay`\nType\n    number\nDescription\n    \nThis is the number of milliseconds that will elapse before the message is sent.\n  * Name\n    `media`\nType\n    string\nDescription\n    \nThe url or local path of the file to send, must be a text string and if it is a URL it must be public.\n\n\n```\nconstflow=addKeyword('register')\n.addAction(async (_, { flowDynamic }) => {\nawaitflowDynamic([{ body:`Thanks ${responseName}` }])\nawaitflowDynamic([{ \n  body:`message with 2 seconds delay`,\n  delay:2000\n  }])\nawaitflowDynamic([{ \n  body:`Look at this`,\n  media:`https://i.imgur.com/0HpzsEm.png`\n }])\nawaitflowDynamic([{ \n  body:`Look at this`,\n  media:join('assets','file.pdf') \n }])\n})\n\n```\nCopyCopied!\n## [FallBack](https://www.builderbot.app/en/</en/methods#fall-back>)\nThe fallBack() function is a fundamental resource within a bot's interaction flow, used to handle invalid or unexpected responses from the user. When a user provides a message that does not match any keyword or expected response, the bot can invoke the fallBack() function to repeat the last message and wait for a valid response.\nTo integrate the fallBack() function into the bot interaction flow, it is used within the [addAnswer()](https://www.builderbot.app/en/</add-functions#add-answer>) or [addAction()](https://www.builderbot.app/en/</add-functions#add-action>) method. Within this method, a condition is set that verifies whether the user's response is valid or not. In case the response does not meet the expected criteria, fallBack() is called to repeat the last message and request a valid response. For example:\nflow-validate-email-custom-error.tsflow-validate-email-default-error.ts\n```\nimport { addKeyword } from'@builderbot/bot';\nconstflowEmailRegister=addKeyword('hello')\n.addAnswer('What is your email?', {capture:true}, (ctx, { fallBack }) => {\nif (!ctx.body.includes('@')) {\nreturnfallBack(`Ups! is not a valid email`);\n  } else {\n// db.insert({email:ctx.body})\n  }\n });\n\n```\nCopyCopied!\n## [EndFlow](https://www.builderbot.app/en/</en/methods#end-flow>)\nThe endFlow function is used in chat applications or conversational user interfaces to end a flow of interaction with the user. Imagine a scenario where you are collecting information from a user in several steps, such as their name, email address and phone number, and at each step the user has the option to cancel the current operation.\nBy using endFlow, you can provide the user with an easy way to cancel the transaction at any time. For example, you could present a button or command that the user can activate to indicate that they wish to stop the current process. Once endFlow is triggered, the interaction flow is terminated and a final message can be displayed to the user, informing them that the request has been canceled.\nIn summary, endFlow improves the user experience by providing a clear and easy-to-use exit in case they decide to abandon the process at any stage of the interaction flow. This helps ensure a smoother and more satisfying user experience in conversational applications.\n### flow-validate-email-custom-error.ts\n```\nconstflowRegister=addKeyword(['Hi'])\n.addAnswer(\n  ['Hello!','To submit the form I need some data...','Write your *Name*'],\n  { capture:true },\nasync (ctx, { flowDynamic, endFlow, state }) => {\nif (ctx.body ==='cancel') {\nreturnendFlow(`Your request has been canceled`);\n   }\nawaitstate.update({name:ctx.body})\nreturnflowDynamic(`Nice to meet you *${ctx.body}*, let's continue...`);\n  }\n )\n.addAnswer(\n  ['I also need your last names'],\n  { capture:true },\nasync (ctx, { flowDynamic, endFlow, state }) => {\nif (ctx.body ==='cancel') {\nreturnendFlow();\n   }\nawaitstate.update({lastName:ctx.body})\nreturnflowDynamic(`Perfect *${ctx.body}*, finally...`);\n  }\n )\n\n```\nCopyCopied!\n## [GotoFlow](https://www.builderbot.app/en/</en/methods#goto-flow>)\nThe `gotoFlow` function allows the smooth transition between different interaction flows in a conversational application. This method is useful when you need to separate the interaction logic into different flows and direct the user from one flow to another according to certain conditions or events.\nFor example, suppose that in a virtual assistant application you have one flow for registered users and another for unregistered users. With `gotoFlow`, it is possible to direct a newly registered user from the unregistered user flow to the registered user flow, or vice versa, providing a personalized and consistent experience for each type of user.\nIn the code provided, it is shown how to use `gotoFlow` to direct the user to the corresponding flow according to their registration status. This helps to modularize the application logic and facilitates the management of multiple conversation flows.\nflow-welcome.tsflow-a.tsflow-b.tsapp.ts\n```\nimport { addKeyword, EVENTS } from'@builderbot/bot';\nconstflowWelcome=addKeyword('hi')\n.addAnswer('Welcome!',null,async (ctx, { gotoFlow }) => {\n// db.get(...)\nconstuserRegistered=true;\nif (userRegistered) returngotoFlow(flowRegistered);\nreturngotoFlow(flowUserNotRegistered);\n  });\nexportdefault flowWelcome\n\n```\nCopyCopied!\nIt is important to note that the implementation of gotoFlow must have a return before\n## [Blacklist](https://www.builderbot.app/en/</en/methods#blacklist>)\nMany times we will need to add or manage a list of nuemers that we do not want to interact with our bot. For them there is a blacklist that contains a series of methods to add, remove and review numbers. Imagine a case where you want to talk to a contact without the intervention of the bot. You could use this mechanism\nflow-mute.tsflow-check-if.ts\n```\nimport { addKeyword } from'@builderbot/bot';\nconstflowMute=addKeyword('hi')\n.addAction(async (ctx, { flowDynamic, blacklist }) => {\n// const dataFromDb = db.findOne({from:ctx.from}) simualte db query\nconstdataFromDb= {muted:true}\nif(dataFromDb.muted) {\nblacklist.add(ctx.from)\nawaitflowDynamic(`${ctx.from}! added to blacklist`);\n     } else {\nblacklist.remove(ctx.from)\nawaitflowDynamic(`${ctx.from}! removed from blacklist`);\n     }\n   });\nexportdefault flowMute\n\n```\nCopyCopied!\n## [Guides](https://www.builderbot.app/en/</en/methods#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/</plugins>)\n## [Resources](https://www.builderbot.app/en/</en/methods#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "  import { createFlow, MemoryDB, createProvider } from '@builderbot/bot';\n  // ...\n  import flowA from './flows/flow-a'\n  import flowB from './flows/flow-b'\n\n  const main = async () => {\n\n      const adapterDB = new MemoryDB()\n      const adapterFlow = createFlow([flowA, flowB])\n      const adapterProvider = createProvider(BaileysProvider)\n\n      adapterProvider.initHttpServer(3000)\n\n      await createBot({\n          flow: adapterFlow,\n          provider: adapterProvider,\n          database: adapterDB,\n      })\n  }\n"}, {"language": "language-ts", "code": "  import { createFlow, MemoryDB, createProvider } from '@builderbot/bot';\n  // ...\n  import flowWelcome from './flows/flow-welcome'\n  import flowOnOff from './flows/flow-on-off'\n\n  const main = async () => {\n\n      const adapterDB = new MemoryDB()\n      const adapterFlow = createFlow([flowWelcome, flowOnOff])\n      const adapterProvider = createProvider(BaileysProvider)\n\n      adapterProvider.initHttpServer(3000)\n\n      await createBot({\n          flow: adapterFlow,\n          provider: adapterProvider,\n          database: adapterDB,\n      })\n  }\n"}, {"language": "language-ts", "code": ".addAnswer('..', null, async (_, { state }) => {\n  state.clear()\n})\n.addAction(async (_, { globalState }) => {\n  globalState.clear()\n})\n"}, {"language": "language-ts", "code": ".addAnswer('..', null, async (_, { state }) => {\n  await state.update({name:'<PERSON>', age:'33'})\n  await state.update({email:'<EMAIL>'})\n})\n.addAction(async (_, { globalState }) => {\n  await globalState.update({name:'<PERSON>', age:'33'})\n  await globalState.update({email:'<EMAIL>'})\n})\n"}, {"language": "language-ts", "code": ".addAnswer('..', null, async (_, { state }) => {\n  state.get('propertyName')\n})\n.addAction(async (_, { globalState }) => {\n  globalState.get('propertyName')\n})\n"}, {"language": "language-ts", "code": ".addAnswer('..', null, async (_, { state }) => {\n  state.getMyState()\n})\n"}, {"language": "language-ts", "code": ".addAnswer('..', null, async (_, { globalState }) => {\n  globalState.getAllState()\n})\n"}, {"language": "language-ts", "code": "  import { addKeyword } from '@builderbot/bot';\n\n  const flowStandAlone = addKeyword('register')\n    .addAnswer('What is your name?', { capture: true }, async (ctx, { flowDynamic }) => {\n      const responseName = ctx.body\n      //.... db.insert({name:responseName})\n      await flowDynamic(`Thanks for register ${responseName}`)\n    })\n\n  export default flowStandAlone\n"}, {"language": "language-ts", "code": "const flow = addKeyword('register')\n.addAction(async (_, { flowDynamic }) => {\n\n  await flowDynamic([{ body: `Thanks  ${responseName}` }])\n\n  await flowDynamic([{ \n    body: `message with 2 seconds delay`,\n    delay: 2000 \n   }])\n\n  await flowDynamic([{ \n    body: `Look at this`,\n    media: `https://i.imgur.com/0HpzsEm.png` \n  }])\n\n  await flowDynamic([{ \n    body: `Look at this`,\n    media: join('assets','file.pdf') \n  }])\n\n})\n"}, {"language": "language-ts", "code": "import { addKeyword } from '@builderbot/bot';\n\nconst flowEmailRegister = addKeyword('hello')\n  .addAnswer('What is your email?', {capture:true}, (ctx, { fallBack }) => {\n    if (!ctx.body.includes('@')) {\n      return fallBack(`Ups! is not a valid email`);\n    } else {\n      // db.insert({email:ctx.body})\n    }\n  });\n"}, {"language": "language-ts", "code": "const flowRegister = addKeyword(['Hi'])\n  .addAnswer(\n    ['Hello!', 'To submit the form I need some data...', 'Write your *Name*'],\n    { capture: true },\n\n    async (ctx, { flowDynamic, endFlow, state }) => {\n      if (ctx.body === 'cancel') {\n        return endFlow(`Your request has been canceled`);\n      }\n      await state.update({name:ctx.body})\n      return flowDynamic(`Nice to meet you *${ctx.body}*, let's continue...`);\n    }\n  )\n  .addAnswer(\n    ['I also need your last names'],\n    { capture: true },\n\n    async (ctx, { flowDynamic, endFlow, state }) => {\n      if (ctx.body === 'cancel') {\n        return endFlow();\n      }\n      await state.update({lastName:ctx.body})\n      return flowDynamic(`Perfect *${ctx.body}*, finally...`);\n    }\n  )\n"}, {"language": "language-ts", "code": "  import { addKeyword, EVENTS } from '@builderbot/bot';\n\n  const flowWelcome = addKeyword('hi')\n    .addAnswer('Welcome!', null, async (ctx, { gotoFlow }) => {\n        // db.get(...)\n        const userRegistered = true;\n\n        if (userRegistered) return gotoFlow(flowRegistered);\n\n        return gotoFlow(flowUserNotRegistered);\n    });\n  export default flowWelcome\n"}, {"language": "language-ts", "code": "  import { addKeyword } from '@builderbot/bot';\n\n  const flowMute = addKeyword('hi')\n      .addAction(async (ctx, { flowDynamic, blacklist }) => {\n          // const dataFromDb = db.findOne({from:ctx.from}) simualte db query\n          const dataFromDb = {muted:true}\n          if(dataFromDb.muted) {\n            blacklist.add(ctx.from)\n            await flowDynamic(`${ctx.from}! added to blacklist`);\n          } else {\n            blacklist.remove(ctx.from)\n            await flowDynamic(`${ctx.from}! removed from blacklist`);\n          }\n  \n      });\n  export default flowMute\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/events", "title": "Events - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/events#>)\n  * [Support](https://www.builderbot.app/en/</en/events#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n      * [DefaultWELCOME](https://www.builderbot.app/en/</en/events#default>)\n      * [Received Image or VideoMEDIA](https://www.builderbot.app/en/</en/events#received-image-or-video>)\n      * [Received DocumentDOCUMENT](https://www.builderbot.app/en/</en/events#received-document>)\n      * [Received LocationLOCATION](https://www.builderbot.app/en/</en/events#received-location>)\n      * [Received Voice NoteVOICE_NOTE](https://www.builderbot.app/en/</en/events#received-voice-note>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/events#>)\n\n\n# Events\nSometimes users send messages such as an image, a video or special location messages, among others, to receive and start a conversation when a message of this type arrives we can use the events.\nWELCOME\n## [Default](https://www.builderbot.app/en/</en/events#default>)\nWhen a user sends a \"text\" message that does not exist in a keyword on another flow, the WELCOME event will be triggered by default, which is the default event.\nLet's imagine the case in which a person writes the word `Thank you!`\napp.tsapp.js\n```\nimport { addKeyword, EVENTS } from'@builderbot/bot'\nconstwelcomeFlow=addKeyword(EVENTS.WELCOME).addAnswer('Ey welcome?')\nconstgreetingFlow=addKeyword(['hello','hi']).addAnswer('Hi!')\n\n```\nCopyCopied!\nhello\nHi\nUser\nBOT\nFlows\ngreetingFlow\nWe can see in the diagram above that the bot does a search in all the flows to get the best flow that can respond to the keyword \"Thank You\" but as it does not find then the \"WELCOME\" is triggered.\nMEDIA\n## [Received Image or Video](https://www.builderbot.app/en/</en/events#received-image-or-video>)\nWhen a user sends an image or a video, the MEDIA event, which is the default event, will be triggered by default. This is ideal for when we need them to send information and we need to store it.\napp.tsapp.js\n```\nimport { addKeyword, EVENTS } from'@builderbot/bot'\nconstmediaFlow=addKeyword(EVENTS.MEDIA).addAnswer('I received a media image/video')\n\n```\nCopyCopied!\nTo save the media file you must invoke the saveFile function from the provider you are using.\napp.tsapp.js\n```\nimport { addKeyword, EVENTS } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\nconstmediaFlow=addKeyword<BaileysProvider>(EVENTS.MEDIA)\n.addAnswer('I received a media image/video',async (ctx, { provider }) => {\nconstlocalPath=awaitprovider.saveFile(ctx, {path:'...'})\n//console.log(localPath)\n })\n\n```\nCopyCopied!\nDOCUMENT\n## [Received Document](https://www.builderbot.app/en/</en/events#received-document>)\nWhen a user sends a document, DOCUMENT event will be triggered by default, which is the default event.\napp.tsapp.js\n```\nimport { addKeyword, EVENTS } from'@builderbot/bot'\nconstdocumentFlow=addKeyword(EVENTS.DOCUMENT)\n.addAnswer(\"Wow! I'm sorry I can't read this document right now\",async (ctx, { provider }) => {\nconstlocalPath=awaitprovider.saveFile(ctx, {path:'...'})\n//console.log(localPath)\n })\n\n```\nCopyCopied!\nLOCATION\n## [Received Location](https://www.builderbot.app/en/</en/events#received-location>)\nLocation must be sent via WhatsApp, does not yet allow location links from external apps\nWhen your chatbot needs to access a user's location, it's important to ensure that the location is sent directly from the WhatsApp app to have results. Once the location is received, you can perform a console log of ctx to view the details of the received location.\nThe received location context will look something like this in console:\n```\nctx:{\n...\nmessage:Message{\nlocationMessage:LocationMessage{\ndegreesLatitude:-2.1462137699127197,\ndegreesLongitude:-79.88981628417969,\nname:'Doctor Miguel Angel Jijón Teran',\naddress:'Doctor Miguel Angel Jijón Teran, Guayaquil, Ecuador',\n  },\n },\nbody:'_event_location__0d5c9f57-0909-44a1-995f-902f9df3b21f',\nname:'yeyodev 👨🏾‍💻',\nfrom:'593000000000'\n}\n\n```\nCopyCopied!\nThis will output the user's latitude and longitude in the console, allowing you to effectively utilize the location data for your chatbot's functionality.\nTo access the location data, you can use the following approach:\nlocation.flow.tslocation.flow.js\n```\nimport { EVENTS, addKeyword } from\"@builderbot/bot\";\nexportdefaultaddKeyword(EVENTS.LOCATION)\n.addAnswer(\"I have received your location!\",null,async (ctx) => {\nconstuserLatitude=ctx.message.locationMessage.degreesLatitude;\nconstuserLongitude=ctx.message.locationMessage.degreesLongitude;\n})\n\n```\nCopyCopied!\nVOICE_NOTE\n## [Received Voice Note](https://www.builderbot.app/en/</en/events#received-voice-note>)\nWhen a user sends a voice note, the VOICE_NOTE event will be triggered by default, which is the event intended for this behavior, it is important to understand that a voice note is different from an image or video file.\napp.tsapp.js\n```\nimport { addKeyword, EVENTS } from'@builderbot/bot'\nconstvoiceNoteFlow=addKeyword(EVENTS.VOICE_NOTE)\n.addAnswer('Give me a second to hear you!',async (ctx, { provider }) => {\nconstlocalPath=awaitprovider.saveFile(ctx, {path:'...'})\n//console.log(localPath)\n })\n\n```\nCopyCopied!\n## [Guides](https://www.builderbot.app/en/</en/events#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/</plugins>)\n## [Resources](https://www.builderbot.app/en/</en/events#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "  import { addKeyword, EVENTS } from '@builderbot/bot'\n  \n  const welcomeFlow = addKeyword(EVENTS.WELCOME).addAnswer('Ey welcome?')\n  const greetingFlow = addKeyword(['hello','hi']).addAnswer('Hi!')\n"}, {"language": "language-ts", "code": "  import { addKeyword, EVENTS } from '@builderbot/bot'\n  \n  const mediaFlow = addKeyword(EVENTS.MEDIA).addAnswer('I received a media image/video')\n"}, {"language": "language-ts", "code": "  import { addKeyword, EVENTS } from '@builderbot/bot'\n  import { <PERSON>s<PERSON><PERSON>ider } from '@builderbot/provider-baileys'\n\n  const mediaFlow = addKeyword<BaileysProvider>(EVENTS.MEDIA)\n  .addAnswer('I received a media image/video', async (ctx, { provider }) => {\n    const localPath = await provider.saveFile(ctx, {path:'...'})\n    //console.log(localPath)\n  })\n"}, {"language": "language-ts", "code": "  import { addKeyword, EVENTS } from '@builderbot/bot'\n  \n  const documentFlow = addKeyword(EVENTS.DOCUMENT)\n  .addAnswer(\"Wow! I'm sorry I can't read this document right now\", async (ctx, { provider }) => {\n    const localPath = await provider.saveFile(ctx, {path:'...'})\n    //console.log(localPath)\n  })\n"}, {"language": "language-bash", "code": "ctx:  {\n  ...\n  message: Message {\n    locationMessage: LocationMessage {\n      degreesLatitude: -2.1462137699127197,\n      degreesLongitude: -79.88981628417969,\n      name: 'Doctor <PERSON>',\n      address: 'Doctor <PERSON>, Guayaquil, Ecuador',\n    },\n  },\n  body: '_event_location__0d5c9f57-0909-44a1-995f-902f9df3b21f',\n  name: 'yeyodev 👨🏾‍💻',\n  from: '593000000000'\n}\n"}, {"language": "language-ts", "code": "import { EVENTS, addKeyword } from \"@builderbot/bot\";\n\nexport default addKeyword(EVENTS.LOCATION)\n.addAnswer(\"I have received your location!\", null, async (ctx) => {\n  const userLatitude = ctx.message.locationMessage.degreesLatitude;\n  const userLongitude = ctx.message.locationMessage.degreesLongitude;\n})\n"}, {"language": "language-ts", "code": "  import { addKeyword, EVENTS } from '@builderbot/bot'\n  \n  const voiceNoteFlow = addKeyword(EVENTS.VOICE_NOTE)\n  .addAnswer('Give me a second to hear you!', async (ctx, { provider }) => {\n    const localPath = await provider.saveFile(ctx, {path:'...'})\n    //console.log(localPath)\n  })\n  \n"}], "sections": []}, {"url": "https://www.builderbot.app/en/databases", "title": "Databases - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/databases#>)\n  * [Support](https://www.builderbot.app/en/</en/databases#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n      * [Memory](https://www.builderbot.app/en/</en/databases#memory>)\n      * [Json](https://www.builderbot.app/en/</en/databases#json>)\n      * [Mongo](https://www.builderbot.app/en/</en/databases#mongo>)\n      * [MySQL](https://www.builderbot.app/en/</en/databases#my-sql>)\n      * [Postgres](https://www.builderbot.app/en/</en/databases#postgres>)\n      * [Custom Database](https://www.builderbot.app/en/</en/databases#custom-database>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/databases#>)\n\n\n# Databases\nJust as providers can be easily exchanged between adapters, we can do the same with the database. Now the important thing to understand is how it works. The main purpose of the database inside the bot is to provide the bot with a record of the different events that have occurred between different conversations.\nMany people use it as a chat history (in fact, it can also be used for that purpose), but you may find strange data in your logs because it stores not only messages, but also events.\nConnectors: [MemoryDB](https://www.builderbot.app/en/</en/databases#memory-db>), [Json](https://www.builderbot.app/en/</en/databases#json>), [Mongo](https://www.builderbot.app/en/</en/databases#mongo>), [MySQL](https://www.builderbot.app/en/</en/databases#my-sql>), [Postgres](https://www.builderbot.app/en/</en/databases#postgres>), [Custom Database](https://www.builderbot.app/en/</en/databases#custom-database>)\nEach database may need to adjust the access keys, configuration, among other properties that will be implemented as configuration of the implemented class.\nmemory-database.tsjson-database.tsmongo-database.tsmysql-database.tspostgres-database.ts\n```\nimport { MemoryDB } from\"@builderbot/bot\";\nexporttypeIDatabase=typeof MemoryDB\nexportconstadapterDB=newMemoryDB();\n\n```\nCopyCopied!\nBelow you will find more information about each of these databases.\n## [Memory](https://www.builderbot.app/en/</en/databases#memory>)\nThe Memory database, often called a Mock database, operates without storing bot-generated responses and needs no configuration. Primarily used for testing and development, it offers a lightweight and convenient solution. This database type is ideal for scenarios where data persistence isn't necessary, facilitating quick prototyping and efficient debugging without managing persistent data storage overhead.\nmemory-database.tsapp.ts\n```\nimport { MemoryDB } from\"@builderbot/bot\";\nexporttypeIDatabase=typeof MemoryDB\nexportconstadapterDB=newMemoryDB();\n\n```\nCopyCopied!\n## [Json](https://www.builderbot.app/en/</en/databases#json>)\n[JSON](https://www.builderbot.app/en/<https:/www.json.org/>) database provides the benefit of securely storing bot-generated responses in a local file, ensuring durability across sessions. Utilizing this database type requires specifying a file path (`filename`) where the JSON data will be stored. It proves particularly valuable when preserving conversation history or user interactions is imperative. By enabling structured data storage, it simplifies data retrieval and analysis, rendering it appropriate for applications where data persistence and retrieval play a critical role.\njson-database.tsapp.ts\n```\nimport { JsonFileDB } from'@builderbot/database-json';\nexporttypeIDatabase=typeof JsonFileDB\nexportconstadapterDB=newJsonFileDB({ filename:'db.json' });\n\n```\nCopyCopied!\n## [Mongo](https://www.builderbot.app/en/</en/databases#mongo>)\n[MongoDB](https://www.builderbot.app/en/<https:/www.mongodb.com/en/what-is-mongodb>) strength lies in its flexible document-oriented structure, which requires configuration parameters like the database URI and name (`dbUri`, `dbName`). Its scalable architecture provides robust storage capabilities, ideal for handling large data volumes.\nBy embracing a NoSQL approach, MongoDB offers flexibility in schema design, effortlessly accommodating evolving application needs. It proves particularly suitable for environments requiring high-performance data storage and retrieval, thanks to its efficient indexing and querying functionalities.\nmongo-database.tsapp.ts\n```\nimport { MongoDB } from'@builderbot/database-mongo'\nexporttypeIDatabase=typeof MongoDB\nexportconstadapterDB=newMongoDB({\n  dbUri:MONGO_DB_URI,\n  dbName:MONGO_DB_NAME,\n})\n\n```\nCopyCopied!\n## [MySQL](https://www.builderbot.app/en/</en/databases#my-sql>)\n[MySQL](https://www.builderbot.app/en/<https:/www.mysql.com/>) database, a widely embraced relational database management system, provides strong data storage capabilities for storing bot-generated responses. When integrating with MySQL, essential parameters such as `host`, `user`, `password`, and the `database` name must be specified. This database variant assures data durability and scalability, rendering it well-suited for applications demanding high-performance data storage and retrieval.\nWith MySQL, developers can harness advanced querying features and transaction support, facilitating efficient management of bot-generated data within a structured and secure framework.\nmysql-database.tsapp.ts\n```\nimport { MysqlDB } from'@builderbot/database-mysql'\nexporttypeIDatabase=typeof MysqlDB\nexportconstadapterDB=newMysqlDB({\n  host:MYSQL_DB_HOST,\n  user:MYSQL_DB_USER,\n  database:MYSQL_DB_NAME,\n  password:MYSQL_DB_PASSWORD,\n})\n\n```\nCopyCopied!\n## [Postgres](https://www.builderbot.app/en/</en/databases#postgres>)\n[PostgreSQL](https://www.builderbot.app/en/<https:/www.postgresql.org/>) database, celebrated for its reliability and cutting-edge features, presents formidable data storage solutions for bot-generated responses. Seamless integration with PostgreSQL entails specifying vital parameters such as `host`, `user`, `password`, `database` name, and `port`. This database variant guarantees data integrity and scalability, rendering it optimal for applications requiring top-tier data storage and retrieval performance.\n### Uses Cases\nIf you want connect your bot to cloud database like Supabase or any CloudProvider you can use it.\n[More info about Postgres connection uses cases](https://www.builderbot.app/en/</databases/postgres/uses-cases>)\nPostgreSQL's flexible architecture and endorsement of advanced data types empower developers to craft sophisticated bots endowed with extensive functionality. Leveraging PostgreSQL equips developers with potent querying capabilities, transactional support, and comprehensive data management features, fostering the creation of resilient and efficient bot systems.\npostgres-database.tsapp.ts\n```\nimport { PostgreSQLDB } from'@builderbot/database-postgres'\nexporttypeIDatabase=typeof PostgreSQLDB\nexportconstadapterDB=newPostgreSQLDB({\n  host:POSTGRES_DB_HOST,\n  user:POSTGRES_DB_USER,\n  database:POSTGRES_DB_NAME,\n  password:POSTGRES_DB_PASSWORD,\n  port:+POSTGRES_DB_PORT,\n})\n\n```\nCopyCopied!\nFirebase Database Adapter\n## [Custom Database](https://www.builderbot.app/en/</en/databases#custom-database>)\nCustom database connector there is the possibility to build your own customized adapter, we know that there are many more database providers that can be very useful, an example can be [Firebase Console](https://www.builderbot.app/en/<https:/firebase.google.com/docs/database/rest/start?hl=es-419>) which apart from giving us the possibility to interact via API Rest also offers a Dashboard to visualize your data.\napp.tsdatabase/firebase.tsdatabase/types.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword } from'@builderbot/bot'\nimport { BaileysProvider as Provider } from'@builderbot/provider-baileys'\nimport { FirebaseAdapter } from'./database/firebase'\nconstPORT=process.env.PORT??3008\nconstwelcomeFlow=addKeyword<Provider,FirebaseAdapter>(['hi'])\n.addAnswer('Ey! welcome')\n\nconstmain=async () => {\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(Provider)\nconstadapterDB=newFirebaseAdapter({\n    databaseURL:'YOUR URL FIREBASE REALTIME DATABASE',\n    pathPrivateKeyJson:\"YOUR PATH CREDENTIALS JSON FIREBASE\"\n  })()\nconst { httpServer } =awaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\nhttpServer(+PORT)\n}\nmain()\n\n```\nCopyCopied!\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "import { MemoryDB } from \"@builderbot/bot\";\n\nexport type IDatabase = typeof MemoryDB\nexport const adapterDB = new MemoryDB();\n"}, {"language": "language-ts", "code": "import { MemoryDB } from \"@builderbot/bot\";\n\nexport type IDatabase = typeof MemoryDB\nexport const adapterDB = new MemoryDB();\n"}, {"language": "language-ts", "code": "import { JsonFileDB } from '@builderbot/database-json';\n\nexport type IDatabase = typeof JsonFileDB\nexport const adapterDB = new JsonFileDB({ filename: 'db.json' });\n"}, {"language": "language-ts", "code": "import { MongoDB } from '@builderbot/database-mongo'\n\nexport type IDatabase = typeof MongoDB\nexport const adapterDB = new MongoDB({\n    dbUri: MONGO_DB_URI,\n    dbName: MONGO_DB_NAME,\n})\n"}, {"language": "language-ts", "code": "import { MysqlDB } from '@builderbot/database-mysql'\n\nexport type IDatabase = typeof MysqlDB\nexport const adapterDB = new MysqlDB({\n    host: MYSQL_DB_HOST,\n    user: MYSQL_DB_USER,\n    database: MYSQL_DB_NAME,\n    password: MYSQL_DB_PASSWORD,\n})\n"}, {"language": "language-ts", "code": "import { PostgreSQLDB } from '@builderbot/database-postgres'\n\nexport type IDatabase = typeof PostgreSQLDB\nexport const adapterDB = new PostgreSQLDB({\n    host: POSTGRES_DB_HOST,\n    user: POSTGRES_DB_USER,\n    database: POSTGRES_DB_NAME,\n    password: POSTGRES_DB_PASSWORD,\n    port: +POSTGRES_DB_PORT,\n})\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, createFlow, addKeyword } from '@builderbot/bot'\nimport { BaileysProvider as Provider } from '@builderbot/provider-baileys'\nimport { FirebaseAdapter } from './database/firebase'\n\nconst PORT = process.env.PORT ?? 3008\n\nconst welcomeFlow = addKeyword<Provider, FirebaseAdapter>(['hi'])\n    .addAnswer('Ey! welcome')\n\n\nconst main = async () => {\n    const adapterFlow = createFlow([welcomeFlow])\n\n    const adapterProvider = createProvider(Provider)\n    const adapterDB = new FirebaseAdapter({\n        databaseURL: 'YOUR URL FIREBASE REALTIME DATABASE',\n        pathPrivateKeyJson: \"YOUR PATH CREDENTIALS JSON FIREBASE\"\n    })()\n\n    const { httpServer } = await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n\n    httpServer(+PORT)\n}\n\nmain()\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/providers/meta", "title": "Meta Provider - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/providers/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/providers/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/providers/</>)\n  * [Contribute](https://www.builderbot.app/en/providers/</contribute>)\n  * [Course](https://www.builderbot.app/en/providers/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/providers/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/providers/</>)\n  * [Documentation](https://www.builderbot.app/en/providers/</en/providers/meta#>)\n  * [Support](https://www.builderbot.app/en/providers/</en/providers/meta#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/providers/</>)\n    * [Quickstart](https://www.builderbot.app/en/providers/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/providers/</concepts>)\n    * [Examples](https://www.builderbot.app/en/providers/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/providers/</add-functions>)\n    * [Context](https://www.builderbot.app/en/providers/</context>)\n    * [Methods](https://www.builderbot.app/en/providers/</methods>)\n    * [Events](https://www.builderbot.app/en/providers/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/providers/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/providers/</providers/meta>)\n      * [Requirements](https://www.builderbot.app/en/providers/</en/providers/meta#requirements>)\n      * [Uses Cases](https://www.builderbot.app/en/providers/</en/providers/meta#uses-cases>)\n      * [Meta tutorial video](https://www.builderbot.app/en/providers/</en/providers/meta#meta-tutorial-video>)\n      * [What do I do now?](https://www.builderbot.app/en/providers/</en/providers/meta#what-do-i-do-now>)\n    * [Twilio](https://www.builderbot.app/en/providers/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/providers/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/providers/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/providers/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/providers/</deploy>)\n    * [Railway](https://www.builderbot.app/en/providers/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/providers/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/providers/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/providers/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/providers/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/providers/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/providers/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/providers/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/providers/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/providers/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/providers/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/providers/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/providers/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/providers/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/providers/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/providers/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/providers/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/providers/</contribute>)\n    * [Core](https://www.builderbot.app/en/providers/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/providers/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/providers/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/providers/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/providers/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/providers/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/providers/</en/providers/meta#>)\n\n\n# Meta Provider\nThe [WhatsApp Business Platform](https://www.builderbot.app/en/providers/<https:/business.whatsapp.com/products/business-platform>) allows medium and large businesses to communicate with their customers on a large scale. You can start conversations with customers in just a few minutes, send them customer service notifications or purchase updates, offer them a personalized level of service, and provide support through their preferred channel.\n## [Requirements](https://www.builderbot.app/en/providers/</en/providers/meta#requirements>)\n  * Register a [Facebook Developers](https://www.builderbot.app/en/providers/<https:/developers.facebook.com/apps>) account.\n  * You will need to verify your business and your application with official documents, the type of which may vary depending on your country.\n  * You will need to associate an official phone number of your business with your account that is not linked to any other WhatsApp account, either regular or Business. For testing purposes, Meta provides a free phone number.\n  * Meta offers **1000** free customer-initiated conversations to the bot, and 250 free bot-initiated conversations to the customer. Once this limit is reached, each additional conversation will have a cost that may vary depending on your country or region.\n\n\n[More info about Meta Deploy](https://www.builderbot.app/en/providers/</deploy/meta>)\n## [Uses Cases](https://www.builderbot.app/en/providers/</en/providers/meta#uses-cases>)\nIf you want to give a quick overview of all the options that this provider allows you to implement in conjunction with builderbot you can go through the use cases, where in code we show some of the most important features of this provider. Cases such as sending **buttons** , **lists** , or **native methods** directly with Meta\n[More info about Meta uses cases](https://www.builderbot.app/en/providers/</providers/meta/uses-cases>)\n## [Meta tutorial video](https://www.builderbot.app/en/providers/</en/providers/meta#meta-tutorial-video>)\nIf you are one of those people who prefer to watch videos, here is a short tutorial on how to implement the Meta base.\n### Video Meta Provider\nYou must create a new application. To do this, click on the **Create application** button.\n![](https://i.imgur.com/DKgjwj9.png)\nOn this screen, you must select **Company or Business**.\n![](https://i.imgur.com/tapmpMk.png)\nIn the next step, you must enter a name for your application, also enter a contact email address and select the Test Business application.\n![](https://i.imgur.com/cfHLJTJ.png)\nNow, go to the WhatsApp Settings section.\n![](https://i.imgur.com/37gMMM5.png)\nThis is the part where you will find the access token and you can also see the number from where WhatsApp messages are sent and to where they will be sent. As you are in the trial version, you must register the numbers to which you want to send.\n![](https://i.imgur.com/bkSAZn2.png)\nOn this same page, you can find the Webhook section that we will soon need to use.\n![](https://i.imgur.com/L3PRcj7.png)\nIn the **main file** of the bot where you are implementing the meta adapter function, you are going to place the following data:\n  * **numberId:** You can find it on the previous page.\n  * **jwtToken:** You can find it on the previous page.\n  * **VerifyToken:** You can write whatever you want, it's like a keyword.\n\n\nIn the example below, you can see a suggestion of how this can be done by using environment variables\n```\nconstmain=async () => {\nconstadapterDB=newMockAdapter()\nconstadapterFlow=createFlow([flowPrincipal])\nconstadapterProvider=createProvider(MetaProvider, {\n    jwtToken:process.env.JWTOKEN,//EAARBW3ZBGU0UBAACDjtQIzI8JuEa.............\n    numberId:process.env.NUMBER_ID,//103975305758520\n    verifyToken:process.env.VERIFY_TOKEN,//LO_QUE_SEA,\n    version:'v16.0'\n  })\ncreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\n}\nmain()\n\n```\nCopyCopied!\nAfter executing the bot, you will find a message in the console similar to the following. Where you can find the URL for your **webhook**.\n```\n$npmstart\n> node app.js\n[meta]: Add this url \"WHEN A MESSAGE COMES IN\"\n[meta]: POST http://localhost:3000/webhook\n[meta]: More information in the documentation\n\n```\nCopyCopied!\n## [What do I do now?](https://www.builderbot.app/en/providers/</en/providers/meta#what-do-i-do-now>)\nYou will notice that the bot starts an HTTP service (endpoint) that must be online on a server so that you can connect it with Meta.\n### Option 1:\nYou can test locally through a proxy tunnel server. Download **[ngork](https://www.builderbot.app/en/providers/<https:/ngrok.com/download>)** , a free tool that will help with this.\n![](https://i.imgur.com/TjjBtRh.png)\nThis generates an online URL that we can use in the **WebHook** section of Meta\n![](https://i.imgur.com/NXHMDsf.png)\n![](https://i.imgur.com/tpov3D1.png)\n![](https://i.imgur.com/haRGylR.png)\n![](https://i.imgur.com/cMaIzeC.png)\n### Option 2:\nIf you have already deployed your bot on a server, you need to obtain the public IP or subdomain provided to you. **Example** if you are using [Railway](https://www.builderbot.app/en/providers/<https:/railway.app/>), you can go to the settings section and generate a subdomain. We would then have the **WebHook** `https://base-twilio-memory-production.up.railway.app/twilio-hook`\n![](https://i.imgur.com/Yg2BYqB.png)\n![](https://i.imgur.com/dIbyEwp.png)\n## [Guides](https://www.builderbot.app/en/providers/</en/providers/meta#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/providers/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/providers/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/providers/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/providers/</plugins>)\n## [Resources](https://www.builderbot.app/en/providers/</en/providers/meta#resources>)\n### [Modularize](https://www.builderbot.app/en/providers/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/providers/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/providers/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/providers/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/providers/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/providers/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/providers/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-js", "code": "const main = async () => {\n    const adapterDB = new MockAdapter()\n    const adapterFlow = createFlow([flowPrincipal])\n\n    const adapterProvider = createProvider(MetaProvider, {\n        jwtToken: process.env.JWTOKEN, //EAARBW3ZBGU0UBAACDjtQIzI8JuEa.............\n        numberId: process.env.NUMBER_ID, //103975305758520\n        verifyToken: process.env.VERIFY_TOKEN, //LO_QUE_SEA,\n        version:'v16.0'\n    })\n\n    createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n}\n\nmain()\n"}, {"language": "language-shell", "code": "$ npm start\n> node app.js\n\n[meta]: Add this url \"WHEN A MESSAGE COMES IN\"\n[meta]: POST http://localhost:3000/webhook\n[meta]: More information in the documentation\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/providers/twilio", "title": "Twilio Provider - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/providers/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/providers/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/providers/</>)\n  * [Contribute](https://www.builderbot.app/en/providers/</contribute>)\n  * [Course](https://www.builderbot.app/en/providers/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/providers/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/providers/</>)\n  * [Documentation](https://www.builderbot.app/en/providers/</en/providers/twilio#>)\n  * [Support](https://www.builderbot.app/en/providers/</en/providers/twilio#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/providers/</>)\n    * [Quickstart](https://www.builderbot.app/en/providers/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/providers/</concepts>)\n    * [Examples](https://www.builderbot.app/en/providers/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/providers/</add-functions>)\n    * [Context](https://www.builderbot.app/en/providers/</context>)\n    * [Methods](https://www.builderbot.app/en/providers/</methods>)\n    * [Events](https://www.builderbot.app/en/providers/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/providers/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/providers/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/providers/</providers/twilio>)\n      * [Send Buttons](https://www.builderbot.app/en/providers/</en/providers/twilio#send-buttons>)\n      * [Content Template Builder](https://www.builderbot.app/en/providers/</en/providers/twilio#content-template-builder>)\n    * [Baileys](https://www.builderbot.app/en/providers/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/providers/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/providers/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/providers/</deploy>)\n    * [Railway](https://www.builderbot.app/en/providers/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/providers/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/providers/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/providers/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/providers/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/providers/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/providers/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/providers/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/providers/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/providers/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/providers/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/providers/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/providers/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/providers/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/providers/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/providers/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/providers/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/providers/</contribute>)\n    * [Core](https://www.builderbot.app/en/providers/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/providers/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/providers/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/providers/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/providers/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/providers/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/providers/</en/providers/twilio#>)\n\n\n# Twilio Provider\nTwilio is a development platform that enables developers to build cloud communication applications and web systems. Twilio's communication APIs empower businesses to provide the appropriate communication experience for their customers within web and mobile applications. By leveraging Twilio's APIs, developers can swiftly integrate functionalities such as voice messages, video calls, text messages, and more into an application.\nTwilio provides you with a Sandbox account so you can try the service for free. [Register a Twilio account](https://www.builderbot.app/en/providers/<https:/www.twilio.com/try-twilio>).\nIf you want to take a closer look at the process of setting up your Twilio environment to start creating your chatbot, click here.\n[More info about Twilio Deploy](https://www.builderbot.app/en/providers/</providers/twilio/deploy>)\nUses Cases If you want to give a quick overview of all the options that this provider allows you to implement in conjunction with builderbot you can go through the use cases, where in code we show some of the most important features of this provider. Cases such as sending buttons, lists, or native methods directly with Meta\n[More info about Twilio uses cases](https://www.builderbot.app/en/providers/</providers/twilio/uses-cases>)\n## [Send Buttons](https://www.builderbot.app/en/providers/</en/providers/twilio#send-buttons>)\nOne of the most requested functions by users is the fact of **sending buttons** to generate more interactivity and more dynamic chatbots.\n### app.ts\n```\nconstwelcomeFlow=addKeyword<Provider,Database>(['template','plantilla'])\n.addAction(async (ctx) => {\nawaitprovider.send(ctx.from,\n'Agregar dos botones que permiten al cliente dar una respuesta mas rapida',\n      {\n        contentSid:'HXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX'\n      }\n    )\n  })\n\n```\nCopyCopied!\nRemember that the use of buttons, lists and other featured Whatsapp functionality needs to be a template authorized by the provider. `contentSid` is the id of the approved template you can use.\n### Content Quick\n![](https://i.imgur.com/RYnLCAy.png)\nRemember for correct functionality you must have the message comply with the message template constructed.\n### Buttons Examples\n![](https://i.imgur.com/zama25v.png)\n## [Content Template Builder](https://www.builderbot.app/en/providers/</en/providers/twilio#content-template-builder>)\nThe Content Template Builder is a Twilio product designed to create template messages and content-rich messages. It has an easy-to-use graphical interface built on a publicly accessible API. With the Content Template Builder, both engineers and non-technical users can create, approve and manage templates from the Twilio Console. With this system, it is possible to create templates faster, with fewer errors, while empowering the entire team to contribute. Both developers and non-technical users can build and submit templates for approval from the Twilio Console without writing a single line of code.\n### Content Templates\n![](https://i.imgur.com/14aes3Z.png)\n## [Guides](https://www.builderbot.app/en/providers/</en/providers/twilio#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/providers/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/providers/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/providers/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/providers/</plugins>)\n## [Resources](https://www.builderbot.app/en/providers/</en/providers/twilio#resources>)\n### [Modularize](https://www.builderbot.app/en/providers/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/providers/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/providers/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/providers/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/providers/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/providers/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/providers/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "const welcomeFlow = addKeyword<Provider, Database>(['template','plantilla'])\n    .addAction(async (ctx) => {\n       await provider.send(ctx.from,\n        'Agregar dos botones que permiten al cliente dar una respuesta mas rapida',\n            {\n                contentSid: 'HXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX'\n            }\n        )\n    })\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/providers/baileys", "title": "Baileys Provider - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/providers/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/providers/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/providers/</>)\n  * [Contribute](https://www.builderbot.app/en/providers/</contribute>)\n  * [Course](https://www.builderbot.app/en/providers/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/providers/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/providers/</>)\n  * [Documentation](https://www.builderbot.app/en/providers/</en/providers/baileys#>)\n  * [Support](https://www.builderbot.app/en/providers/</en/providers/baileys#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/providers/</>)\n    * [Quickstart](https://www.builderbot.app/en/providers/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/providers/</concepts>)\n    * [Examples](https://www.builderbot.app/en/providers/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/providers/</add-functions>)\n    * [Context](https://www.builderbot.app/en/providers/</context>)\n    * [Methods](https://www.builderbot.app/en/providers/</methods>)\n    * [Events](https://www.builderbot.app/en/providers/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/providers/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/providers/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/providers/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/providers/</providers/baileys>)\n      * [QR Code](https://www.builderbot.app/en/providers/</en/providers/baileys#qr-code>)\n      * [Pairing code](https://www.builderbot.app/en/providers/</en/providers/baileys#pairing-code>)\n      * [Send Presence Update](https://www.builderbot.app/en/providers/</en/providers/baileys#send-presence-update>)\n      * [Number Exists on WhatsApp](https://www.builderbot.app/en/providers/</en/providers/baileys#number-exists-on-whats-app>)\n      * [Profile Picture](https://www.builderbot.app/en/providers/</en/providers/baileys#profile-picture>)\n      * [Modifying Chats](https://www.builderbot.app/en/providers/</en/providers/baileys#modifying-chats>)\n      * [Improve performance with Baileys](https://www.builderbot.app/en/providers/</en/providers/baileys#improve-performance-with-baileys>)\n      * [More examples](https://www.builderbot.app/en/providers/</en/providers/baileys#more-examples>)\n    * [Venom](https://www.builderbot.app/en/providers/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/providers/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/providers/</deploy>)\n    * [Railway](https://www.builderbot.app/en/providers/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/providers/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/providers/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/providers/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/providers/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/providers/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/providers/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/providers/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/providers/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/providers/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/providers/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/providers/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/providers/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/providers/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/providers/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/providers/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/providers/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/providers/</contribute>)\n    * [Core](https://www.builderbot.app/en/providers/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/providers/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/providers/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/providers/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/providers/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/providers/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/providers/</en/providers/baileys#>)\n\n\n# Baileys Provider\nThe [Baileys](https://www.builderbot.app/en/providers/<https:/whiskeysockets.github.io/Baileys/>) library originated as a project for CS-2362 at [Ashoka University](https://www.builderbot.app/en/providers/<https:/www.ashoka.edu.in/courses/mathematics-and-computer-science/>) and is not affiliated with or endorsed by WhatsApp. Use it at your own discretion and avoid spamming individuals. We discourage the use of stalkerware, bulk messaging, or any automated messaging practices.\n**[Baileys](https://www.builderbot.app/en/providers/<https:/whiskeysockets.github.io/Baileys/>) is a free WhatsApp provider** that operates via WhatsApp Web. It interacts directly with WhatsApp Web using a WebSocket and does not require Selenium or any other browser. By avoiding Selenium or Chromium, Baileys conserves significant RAM resources.\nAvoid using buttons and lists as they are not fully compatible. While they may display on some devices, they often fail to function as intended.\n## [QR Code](https://www.builderbot.app/en/providers/</en/providers/baileys#qr-code>)\nLink device QR Code In the code below you can see the standard way to link a device by scanning the QR code with the whatsapp application.\n### app.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword } from'@builderbot/bot'\nimport { BaileysProvider as Provider } from'@builderbot/provider-baileys'\nimport { MemoryDB as Database } from'@builderbot/bot'\nconstPORT=process.env.PORT??3008\nconstwelcomeFlow=addKeyword<Provider,Database>(['hi','hello','hola'])\n.addAnswer(`🙌 Hello welcome to this *Chatbot*`)\nconstmain=async () => {\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(Provider)\nconstadapterDB=newDatabase()\nconst { httpServer } =awaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\nhttpServer(+PORT)\n}\nmain()\n\n```\nCopyCopied!\n## [Pairing code](https://www.builderbot.app/en/providers/</en/providers/baileys#pairing-code>)\nLink device pairing code, In the code below you can see the alternative way to link the whatsapp account through a pairing code.\n### app.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword } from'@builderbot/bot'\nimport { BaileysProvider as Provider } from'@builderbot/provider-baileys'\nimport { MemoryDB as Database } from'@builderbot/bot'\nimport { config } from'dotenv'\nconfig()\nconstPHONE_NUMBER=process.env.PHONE_NUMBER\nconstwelcomeFlow=addKeyword<Provider,Database>(['hi','hello','hola'])\n.addAnswer(`🙌 Hello welcome to this *Chatbot*`)\nconstmain=async () => {\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(Provider, { usePairingCode:true, phoneNumber:PHONE_NUMBER })\nconstadapterDB=newDatabase()\nconstbotResult=awaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\n}\nmain()\n\n```\nCopyCopied!\n## [Send Presence Update](https://www.builderbot.app/en/providers/</en/providers/baileys#send-presence-update>)\nThe method **sendPresenceUpdate** lets the person/group with id know whether you're online, offline, typing etc. This method has the following signature:\n```\n(property) sendPresenceUpdate: (type:WAPresence, toJid?:string) =>Promise<void>\n\n```\nCopyCopied!\nWAPresence can be one of the following:\n```\ntypeWAPresence='unavailable'|'available'|'composing'|'recording'|'paused'\n\n```\nCopyCopied!\n### app.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword, EVENTS } from'@builderbot/bot'\nimport { MemoryDB as Database } from'@builderbot/bot'\nimport { BaileysProvider as Provider } from'@builderbot/provider-baileys'\nimport { config } from'dotenv'\nconfig()\nconstPHONE_NUMBER=process.env.PHONE_NUMBER\nconstwaitT= (ms:number) => {\nreturnnewPromise((resolve) => {\nsetTimeout(() => {\nresolve(ms)\n    }, ms)\n  })\n}\nconstwelcomeFlow=addKeyword<Provider,Database>(EVENTS.WELCOME)\n.addAnswer(`💡 Example *Sending Presence Update*`)\n.addAction(\nasync (ctx, { provider, flowDynamic }) => {\nawaitflowDynamic('This is an example of presence update')\nawaitprovider.vendor.sendPresenceUpdate('recording',ctx.key.remoteJid)\nawaitwaitT(5000)\nawaitprovider.vendor.sendPresenceUpdate('composing',ctx.key.remoteJid)\nawaitwaitT(5000)\nawaitflowDynamic('Great!')\n    }\n  )\nconstmain=async () => {\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(Provider, { usePairingCode:true, phoneNumber:PHONE_NUMBER })\nconstadapterDB=newDatabase()\nconstbotResult=awaitcreateBot(\n    {\n      flow: adapterFlow,\n      provider: adapterProvider,\n      database: adapterDB,\n    }\n  )\n}\nmain()\n\n```\nCopyCopied!\n### Video Send Presence Update\n## [Number Exists on WhatsApp](https://www.builderbot.app/en/providers/</en/providers/baileys#number-exists-on-whats-app>)\nTo check if an entered number exists on WhatsApp, you use the **onWhatsApp** method of the provider. This method has the following signature:\n```\nonWhatsApp: (...jids:string[]) =>Promise<{\n  exists:boolean;\n  jid:string;\n}[]>\n\n```\nCopyCopied!\nThis method accepts one or more phone numbers (JIDs) as arguments and returns a promise that resolves to an array of objects containing the existence status (exists) and JID (jid) for each number.\n### app.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword, EVENTS } from'@builderbot/bot'\nimport { MemoryDB as Database } from'@builderbot/bot'\nimport { BaileysProvider as Provider } from'@builderbot/provider-baileys'\nimport { config } from'dotenv'\nconfig()\nconstPHONE_NUMBER=process.env.PHONE_NUMBER\nconstwelcomeFlow=addKeyword<Provider,Database>(EVENTS.WELCOME)\n.addAnswer(`💡 Example *Number Exist on Whatsapp*`)\n.addAnswer(\n'*Enter the number to check:*',\n    { capture:true },\nasync (ctx, { provider, flowDynamic }) => {\nconstcheckNumber=ctx.body\ntry {\nconstonWhats=awaitprovider.vendor.onWhatsApp(checkNumber)\nif (onWhats[0]?.exists) {\nawaitflowDynamic([`*Exists:* ${onWhats[0].exists}\\n*JID:* ${onWhats[0].jid}`,`*Object:* ${JSON.stringify(onWhats,null,6)}`])\n        }\nelse {\nawaitflowDynamic(`The number *${checkNumber}* does not exists on Whatsapp.`)\n        }\n      } catch (error) {\nawaitflowDynamic(`*Error:* ${error}`);\n      }\n    }\n  )\nconstmain=async () => {\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(Provider, { usePairingCode:true, phoneNumber:PHONE_NUMBER })\nconstadapterDB=newDatabase()\nconstbotResult=awaitcreateBot(\n    {\n      flow: adapterFlow,\n      provider: adapterProvider,\n      database: adapterDB,\n    }\n  )\n}\nmain()\n\n```\nCopyCopied!\n![API Call Example](https://www.builderbot.app/assets/examples/onWhatsapp.png)\n## [Profile Picture](https://www.builderbot.app/en/providers/</en/providers/baileys#profile-picture>)\nThe method **profilePictureUrl** allows us to retrieve the profile picture of a given number. This method has the following signature:\n```\n(property) profilePictureUrl: (jid:string, type?:\"image\"|\"preview\", timeoutMs?:number) =>Promise<string>\n\n```\nCopyCopied!\n### app.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword, EVENTS } from'@builderbot/bot'\nimport { BaileysProvider as Provider } from'@builderbot/provider-baileys'\nimport { MemoryDB as Database } from'@builderbot/bot'\nimport { config } from'dotenv'\nconfig()\nconstPHONE_NUMBER=process.env.PHONE_NUMBER\nconstwelcomeFlow=addKeyword<Provider,Database>(EVENTS.WELCOME)\n.addAnswer(`💡 Example *profile Picture*`)\n.addAnswer(\n'Enter number to check image profile: ', { capture:true },\nasync (ctx, { provider, flowDynamic, fallBack, endFlow }) => {\nconstcheck=ctx.body +'@s.whatsapp.net'\ntry {\nconstimageProfile=awaitprovider.vendor.profilePictureUrl(check.replace(/\\+/g,''),'image',10000)\nawaitflowDynamic([\n          {\n            body:'*Profile Picture:*',\n            media: imageProfile\n          }\n        ])\nreturnendFlow('End.')\n      } catch (error) {\nawaitflowDynamic(`Error: ${error.message}`)\nreturnfallBack('Try it again.')\n      }\n    }\n  )\nconstmain=async () => {\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(Provider, { usePairingCode:true, phoneNumber:PHONE_NUMBER })\nconstadapterDB=newDatabase()\nconstbotResult=awaitcreateBot(\n    {\n      flow: adapterFlow,\n      provider: adapterProvider,\n      database: adapterDB,\n    }\n  )\n}\nmain()\n\n```\nCopyCopied!\n![API Call Example](https://www.builderbot.app/assets/examples/imageProfile.png)\n## [Modifying Chats](https://www.builderbot.app/en/providers/</en/providers/baileys#modifying-chats>)\nThe chatModify method provides various options to modify a chat, including:\n  * Archive a chat\n  * Mute/unmute a chat\n  * Mark a chat read/unread\n  * Delete a message for me\n  * Delete a chat\n  * Pin/unpin a chat\n  * Star/unstar a message\n\n\nThis method has the following signature:\n```\n(property) chatModify: (mod: ChatModification, jid: string) => Promise\n```\nCopyCopied!\n**Delete a message for me** when the received message does not comply with my business logic rules. (Develop your own logic rules)\n### app.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword, EVENTS } from'@builderbot/bot'\nimport { BaileysProvider as Provider } from'@builderbot/provider-baileys'\nimport { MemoryDB as Database } from'@builderbot/bot'\nimport { config } from'dotenv'\nconfig()\nconstPHONE_NUMBER=process.env.PHONE_NUMBER\nconstbadWords= ['fuck','ass hole','motherfucker']\nconstwaitT= (ms:number) => {\nreturnnewPromise((resolve) => {\nsetTimeout(() => {\nresolve(ms);\n    }, ms)\n  })\n}\nconstwelcomeFlow=addKeyword<Provider,Database>(EVENTS.WELCOME)\n.addAnswer(`💡 Example *Delete User Message:*`)\n.addAction(\nasync (ctx, { provider, flowDynamic }) => {\nconstresp=ctx.body.toLocaleLowerCase()\nconstcontainsBadWord=badWords.some(word =>resp.includes(word))\nconstid=ctx.key.id\nconstfromMe=ctx.key.fromMe\nconsttimeStamp=ctx.messageTimestamp\nif (containsBadWord) {\nawaitflowDynamic('Your message is going to be deleted as you are sending inappropriate language.')\nawaitwaitT(3500)\ntry {\nawaitprovider.vendor.chatModify(\n            { clear: { messages: [{ id: id, fromMe: fromMe, timestamp: timeStamp }] } },\nctx.key.remoteJid\n          )\nawaitflowDynamic(`Message deleted successfully.`)\n        } catch (error) {\nawaitflowDynamic(`Error: ${JSON.stringify(error,null,3)}`)\n        }\n      }\nawaitflowDynamic('Welcome!')\n    }\n  )\nconstmain=async () => {\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(Provider, { usePairingCode:true, phoneNumber:PHONE_NUMBER })\nconstadapterDB=newDatabase()\nconstbotResult=awaitcreateBot(\n    {\n      flow: adapterFlow,\n      provider: adapterProvider,\n      database: adapterDB,\n    }\n  )\n}\nmain()\n\n```\nCopyCopied!\n### Delete user message\n## [Improve performance with Baileys](https://www.builderbot.app/en/providers/</en/providers/baileys#improve-performance-with-baileys>)\nBaileys is a powerful provider for WhatsApp, but its ability to listen and process a wide range of events in real-time can lead to significant resource consumption, especially for highly active accounts. This guide will help you optimize your builderbot.app bot's performance when using Baileys as a provider.\n### Quickly optimize your Baileys provider\nActivate these options in your Baileys provider configuration to improve performance:\n```\nconstadapterProvider=createProvider(Provider, { \n  experimentalStore:true,// Significantly reduces resource consumption\n  timeRelease:********,// Cleans up data every 3 hours (in milliseconds)\n})\n\n```\nCopyCopied!\nIn addition to this configuration I recommend a reboot every 12 or 24 hours depending on how active your bot is. You can follow this guide to [configure the restarts](https://www.builderbot.app/en/providers/</showcases/docker-pm2>)\n### Benefits:\n  * `experimentalStore: true`: Limits processing to individual messages only, drastically reducing resource usage.\n  * `timeRelease: ********`: Performs periodic cleanups of accumulated data every 3 hours.\n\n\n**Note** : `experimentalStore` may limit some advanced WhatsApp functionalities, especially for groups.\n### Types of monitored events\nBaileys constantly monitors various types of events, including:\n  1. **Individual messages** : Sending and receiving messages in one-on-one chats.\n  2. **Group messages** : All activity in WhatsApp groups.\n  3. **Read states** : Updates when messages are read.\n  4. **Contact stories** : Posting and viewing of statuses/stories.\n  5. **Message editing and deletion** : Changes to existing messages.\n  6. **Reactions** : Emojis and other reactions to messages.\n  7. **Profile updates** : Changes in profile pictures, statuses, etc.\n  8. **Calls** : Notifications of incoming and outgoing calls.\n\n\n### Impact on resources\n  1. **Constant processing** : Each event triggers processes in the bot, consuming CPU.\n  2. **Data storage** : Events are recorded in \"baileys_store.json\", increasing storage usage.\n  3. **Frequent overwriting** : The file is updated with each new event, which can be I/O intensive.\n  4. **Exponential growth** : In very active accounts, the volume of events can grow rapidly.\n\n\n### Optimization strategies\n### 1. Using ExperimentalStore\nThe `experimentalStore: true` option is an advanced feature in builderbot.app designed to significantly optimize the bot's resource usage.\n#### How it works:\n  * Limits listening and processing to only individual message events.\n  * Drastically reduces resource consumption by ignoring other types of events.\n\n\n#### Implementation:\n```\nconstadapterProvider=createProvider(Provider, { \n  experimentalStore:true,\n  timeRelease:********,// 3 hours in milliseconds\n})\n\n```\nCopyCopied!\n#### Important limitations:\n  * **Only for individual messages** : Works exclusively with \"message\" type events for individual chats.\n  * **Not compatible with group functions** : Not suitable for bots requiring group functionalities.\n  * **Feature restriction** : Some advanced WhatsApp features may not work correctly.\n\n\n### 2. Event filtering\nConfigure Baileys to ignore non-essential events for your bot. This can be done through provider configuration or by selectively handling events in your code.\n### 3. Periodic cleanup\nUse the `timeRelease` option or scheduled restarts to clear accumulated data. This helps keep the storage file size under control.\n### 4. Selective monitoring\nIf possible, limit the number of chats or groups actively monitored. This can significantly reduce the number of processed events.\n### Implementation example\nHere's an example of how to implement these strategies in your bot:\nThis example uses `experimentalStore` and `timeRelease` to optimize the bot's performance.\napp.tsapp-experimental.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword } from'@builderbot/bot'\nimport { BaileysProvider as Provider } from'@builderbot/provider-baileys'\nimport { MemoryDB as Database } from'@builderbot/bot'\nimport { config } from'dotenv'\nconfig()\n\nconstwelcomeFlow=addKeyword<Provider,Database>(['hi','hello','hola'])\n.addAnswer(`🙌 Hello welcome to this *Chatbot*`)\nconstmain=async () => {\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(Provider, { \n    timeRelease:********,// 3 hours in milliseconds\n  })\nconstadapterDB=newDatabase()\nconstbotResult=awaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\n}\nmain()\n\n```\nCopyCopied!\n## [More examples](https://www.builderbot.app/en/providers/</en/providers/baileys#more-examples>)\nIf you want to see more examples applying Baiely's functions you can check the links below\n[Delete Bot Message](https://www.builderbot.app/en/providers/</providers/baileys/delete-bot-message>)| Delete an existing message in the conversation  \n---|---  \n[Blocked Users on Bot](https://www.builderbot.app/en/providers/</providers/baileys/blocked-users>)| Block Whatsapp user using the provider  \n[Fetch Status](https://www.builderbot.app/en/providers/</providers/baileys/fetchStatus>)| Retrieve all whatsapp profile status information  \n## [Guides](https://www.builderbot.app/en/providers/</en/providers/baileys#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/providers/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/providers/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/providers/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/providers/</plugins>)\n## [Resources](https://www.builderbot.app/en/providers/</en/providers/baileys#resources>)\n### [Modularize](https://www.builderbot.app/en/providers/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/providers/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/providers/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/providers/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/providers/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/providers/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/providers/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "import { createBot, createProvider, createFlow, addKeyword } from '@builderbot/bot'\nimport { BaileysProvider as Provider } from '@builderbot/provider-baileys'\nimport { MemoryDB as Database } from '@builderbot/bot'\n\nconst PORT = process.env.PORT ?? 3008\n\nconst welcomeFlow = addKeyword<Provider, Database>(['hi', 'hello', 'hola'])\n    .addAnswer(`🙌 Hello welcome to this *Chatbot*`)\n\nconst main = async () => {\n    const adapterFlow = createFlow([welcomeFlow])\n    const adapterProvider = createProvider(Provider)\n    const adapterDB = new Database()\n    const { httpServer } = await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n    httpServer(+PORT)\n}\n\nmain()\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, createFlow, addKeyword } from '@builderbot/bot'\nimport { BaileysProvider as Provider } from '@builderbot/provider-baileys'\nimport { MemoryDB as Database } from '@builderbot/bot'\nimport { config } from 'dotenv'\nconfig()\n\nconst PHONE_NUMBER = process.env.PHONE_NUMBER\n\nconst welcomeFlow = addKeyword<Provider, Database>(['hi', 'hello', 'hola'])\n    .addAnswer(`🙌 Hello welcome to this *Chatbot*`)\n\nconst main = async () => {\n    const adapterFlow = createFlow([welcomeFlow])\n    const adapterProvider = createProvider(Provider, { usePairingCode: true, phoneNumber: PHONE_NUMBER })\n    const adapterDB = new Database()\n    const botResult = await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n}\n\nmain()\n"}, {"language": "language-ts", "code": "(property) sendPresenceUpdate: (type: WAPresence, toJid?: string) => Promise<void>\n"}, {"language": "language-ts", "code": "type WAPresence = 'unavailable' | 'available' | 'composing' | 'recording' | 'paused'\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, createFlow, addKeyword, EVENTS } from '@builderbot/bot'\nimport { MemoryDB as Database } from '@builderbot/bot'\nimport { BaileysProvider as Provider } from '@builderbot/provider-baileys'\nimport { config } from 'dotenv'\nconfig()\n\nconst PHONE_NUMBER = process.env.PHONE_NUMBER\n\nconst waitT = (ms: number) => {\n    return new Promise((resolve) => {\n        setTimeout(() => {\n            resolve(ms)\n        }, ms)\n    })\n}\n\nconst welcomeFlow = addKeyword<Provider, Database>(EVENTS.WELCOME)\n    .addAnswer(`💡 Example *Sending Presence Update*`)\n    .addAction(\n        async (ctx, { provider, flowDynamic }) => {\n            await flowDynamic('This is an example of presence update')\n            await provider.vendor.sendPresenceUpdate('recording', ctx.key.remoteJid)\n            await waitT(5000)\n            await provider.vendor.sendPresenceUpdate('composing', ctx.key.remoteJid)\n            await waitT(5000)\n            await flowDynamic('Great!')\n        }\n    )\n\nconst main = async () => {\n    const adapterFlow = createFlow([welcomeFlow])\n    const adapterProvider = createProvider(Provider, { usePairingCode: true, phoneNumber: PHONE_NUMBER })\n    const adapterDB = new Database()\n    const botResult = await createBot(\n        {\n            flow: adapterFlow,\n            provider: adapterProvider,\n            database: adapterDB,\n        }\n    )\n\n}\n\nmain()\n"}, {"language": "language-ts", "code": "onWhatsApp: (...jids: string[]) => Promise<{\n    exists: boolean;\n    jid: string;\n}[]>\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, create<PERSON>low, addKeyword, EVENTS } from '@builderbot/bot'\nimport { MemoryDB as Database } from '@builderbot/bot'\nimport { BaileysProvider as Provider } from '@builderbot/provider-baileys'\nimport { config } from 'dotenv'\nconfig()\n\nconst PHONE_NUMBER = process.env.PHONE_NUMBER\n\nconst welcomeFlow = addKeyword<Provider, Database>(EVENTS.WELCOME)\n    .addAnswer(`💡 Example *Number Exist on Whatsapp*`)\n    .addAnswer(\n        '*Enter the number to check:*',\n        { capture: true },\n        async (ctx, { provider, flowDynamic }) => {\n            const checkNumber = ctx.body\n            try {\n                const onWhats = await provider.vendor.onWhatsApp(checkNumber)\n                if (onWhats[0]?.exists) {\n                    await flowDynamic([`*Exists:* ${onWhats[0].exists}\\n*JID:* ${onWhats[0].jid}`, `*Object:* ${JSON.stringify(onWhats, null, 6)}`])\n                }\n                else {\n                    await flowDynamic(`The number *${checkNumber}* does not exists on Whatsapp.`)\n                }\n            } catch (error) {\n                await flowDynamic(`*Error:* ${error}`);\n            }\n        }\n    )\n\nconst main = async () => {\n    const adapterFlow = createFlow([welcomeFlow])\n    const adapterProvider = createProvider(Provider, { usePairingCode: true, phoneNumber: PHONE_NUMBER })\n    const adapterDB = new Database()\n    const botResult = await createBot(\n        {\n            flow: adapterFlow,\n            provider: adapterProvider,\n            database: adapterDB,\n        }\n    )\n}\n\nmain()\n"}, {"language": "language-ts", "code": "(property) profilePictureUrl: (jid: string, type?: \"image\" | \"preview\", timeoutMs?: number) => Promise<string>\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, create<PERSON>low, addKeyword, EVENTS } from '@builderbot/bot'\nimport { <PERSON>s<PERSON><PERSON>ider as Provider } from '@builderbot/provider-baileys'\nimport { MemoryDB as Database } from '@builderbot/bot'\nimport { config } from 'dotenv'\nconfig()\n\nconst PHONE_NUMBER = process.env.PHONE_NUMBER\n\nconst welcomeFlow = addKeyword<Provider, Database>(EVENTS.WELCOME)\n    .addAnswer(`💡 Example *profile Picture*`)\n    .addAnswer(\n        'Enter number to check image profile: ', { capture: true },\n        async (ctx, { provider, flowDynamic, fallBack, endFlow }) => {\n            const check = ctx.body + '@s.whatsapp.net'\n            try {\n                const imageProfile = await provider.vendor.profilePictureUrl(check.replace(/\\+/g, ''), 'image', 10000)\n                await flowDynamic([\n                    {\n                        body: '*Profile Picture:*',\n                        media: imageProfile\n                    }\n                ])\n                return endFlow('End.')\n            } catch (error) {\n                await flowDynamic(`Error: ${error.message}`)\n                return fallBack('Try it again.')\n            }\n\n        }\n    )\n\nconst main = async () => {\n    const adapterFlow = createFlow([welcomeFlow])\n    const adapterProvider = createProvider(Provider, { usePairingCode: true, phoneNumber: PHONE_NUMBER })\n    const adapterDB = new Database()\n    const botResult = await createBot(\n        {\n            flow: adapterFlow,\n            provider: adapterProvider,\n            database: adapterDB,\n        }\n    )\n}\n\nmain()\n"}, {"language": "text", "code": "(property) chatModify: (mod: ChatModification, jid: string) => Promise\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, create<PERSON>low, addKeyword, EVENTS } from '@builderbot/bot'\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON> as Provider } from '@builderbot/provider-baileys'\nimport { MemoryDB as Database } from '@builderbot/bot'\nimport { config } from 'dotenv'\nconfig()\n\nconst PHONE_NUMBER = process.env.PHONE_NUMBER\n\nconst badWords = ['fuck', 'ass hole', 'motherfucker']\n\nconst waitT = (ms: number) => {\n    return new Promise((resolve) => {\n        setTimeout(() => {\n            resolve(ms);\n        }, ms)\n    })\n}\n\nconst welcomeFlow = addKeyword<Provider, Database>(EVENTS.WELCOME)\n    .addAnswer(`💡 Example *Delete User Message:*`)\n    .addAction(\n        async (ctx, { provider, flowDynamic }) => {\n            const resp = ctx.body.toLocaleLowerCase()\n            const containsBadWord = badWords.some(word => resp.includes(word))\n            const id = ctx.key.id\n            const fromMe = ctx.key.fromMe\n            const timeStamp = ctx.messageTimestamp\n            if (containsBadWord) {\n                await flowDynamic('Your message is going to be deleted as you are sending inappropriate language.')\n                await waitT(3500)\n                try {\n                    await provider.vendor.chatModify(\n                        { clear: { messages: [{ id: id, fromMe: fromMe, timestamp: timeStamp }] } },\n                        ctx.key.remoteJid\n                    )\n                    await flowDynamic(`Message deleted successfully.`)\n                } catch (error) {\n                    await flowDynamic(`Error: ${JSON.stringify(error, null, 3)}`)\n                }\n            }\n            await flowDynamic('Welcome!')\n        }\n    )\n\nconst main = async () => {\n    const adapterFlow = createFlow([welcomeFlow])\n    const adapterProvider = createProvider(Provider, { usePairingCode: true, phoneNumber: PHONE_NUMBER })\n    const adapterDB = new Database()\n    const botResult = await createBot(\n        {\n            flow: adapterFlow,\n            provider: adapterProvider,\n            database: adapterDB,\n        }\n    )\n}\n\nmain()\n"}, {"language": "language-typescript", "code": "const adapterProvider = createProvider(Provider, { \n    experimentalStore: true,  // Significantly reduces resource consumption\n    timeRelease: ********,    // Cleans up data every 3 hours (in milliseconds)\n})\n"}, {"language": "language-typescript", "code": "const adapterProvider = createProvider(Provider, { \n    experimentalStore: true,\n    timeRelease: ********, // 3 hours in milliseconds\n})\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, createFlow, addKeyword } from '@builderbot/bot'\nimport { BaileysProvider as Provider } from '@builderbot/provider-baileys'\nimport { MemoryDB as Database } from '@builderbot/bot'\nimport { config } from 'dotenv'\nconfig()\n\n\nconst welcomeFlow = addKeyword<Provider, Database>(['hi', 'hello', 'hola'])\n    .addAnswer(`🙌 Hello welcome to this *Chatbot*`)\n\nconst main = async () => {\n    const adapterFlow = createFlow([welcomeFlow])\n    const adapterProvider = createProvider(Provider, { \n        timeRelease: ********, // 3 hours in milliseconds\n    })\n\n    const adapterDB = new Database()\n    const botResult = await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n}\n\nmain()\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/providers#venom", "title": "Providers - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/providers#>)\n  * [Support](https://www.builderbot.app/en/</en/providers#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/providers#>)\n\n\n# Providers\nProviders are in charge of providing the communication bridge between your bot and **whatsapp** , **telegram** or custom provider.\nYou can change your provider very easily without affecting the logic of your bot. It is as simple as implementing the connector of the provider of your choice. At the moment we have integrations with more than four suppliers.\nWhatsapp: [Meta](https://www.builderbot.app/en/</en/providers#meta>), [Twilio](https://www.builderbot.app/en/</en/providers#twilio>), [Baileys](https://www.builderbot.app/en/</en/providers#baileys>), [WPPConnect](https://www.builderbot.app/en/</en/providers#wpp-connect>), [Venom](https://www.builderbot.app/en/</en/providers#venom>), [Telegram](https://www.builderbot.app/en/</plugins/telegram>), [Custom Provider](https://www.builderbot.app/en/</en/providers#custom-provider>)\nBOT\nUser\nuser\naddAnswer\nflows\nTwilio\nMeta\nTelegram\ncustom\nProvider\nEach provider may need to adjust access keys, settings, among other properties that are usually passed as an object in the `createProvider` function.\nmeta-provider.tstwilio-provider.tsbaileys-provider.tsvenom-provider.tswppconnect-provider.ts\n```\nimport { createProvider } from\"@builderbot/bot\";\nimport { MetaProvider } from'@builderbot/provider-meta';\nexporttypeIProvider=typeof MetaProvider\nexportconstadapterProvider=createProvider(MetaProvider, {\n  jwtToken:'jwtToken',\n  numberId:'numberId',\n  verifyToken:'verifyToken',\n  version:'v16.0',\n})\n\n```\nCopyCopied!\nBelow you will find more information about each of these providers.\n## [Meta](https://www.builderbot.app/en/</en/providers#meta>)\nThe [WhatsApp Business Platform](https://www.builderbot.app/en/<https:/business.whatsapp.com/products/business-platform>) enables medium and large businesses to communicate with their customers on a large scale. You can initiate conversations with customers in just minutes, send them customer service notifications or purchase updates, offer them a personalized level of service, and provide support through the channel of their choice.\nmeta-provider.tsapp.ts\n```\nimport { createProvider } from\"@builderbot/bot\";\nimport { MetaProvider } from'@builderbot/provider-meta';\nexporttypeIProvider=typeof MetaProvider\nexportconstadapterProvider=createProvider(MetaProvider, {\n  jwtToken:'jwtToken',\n  numberId:'numberId',\n  verifyToken:'verifyToken',\n  version:'v16.0',\n})\n\n```\nCopyCopied!\n[More info about Meta provider](https://www.builderbot.app/en/</providers/meta>)\n## [Twilio](https://www.builderbot.app/en/</en/providers#twilio>)\n[Twilio](https://www.builderbot.app/en/<https:/www.twilio.com/en-us/messaging/channels/whatsapp>) is a development platform that enables developers to build cloud communication applications and web systems. Twilio's communications APIs enable businesses to provide the right communication experience for their customers within web and mobile applications. By using Twilio APIs, developers can quickly add this functionality to an application, such as voice messaging, video calls, text messaging and more.\ntwilio-provider.tsapp.ts\n```\nimport { createProvider } from\"@builderbot/bot\";\nimport { TwilioProvider } from'@builderbot/provider-twilio';\nexporttypeIProvider=typeof TwilioProvider\nexportconstadapterProvider=createProvider(TwilioProvider, {\n  accountSid:'YOUR_ACCOUNT_SID',\n  authToken:'YOUR_ACCOUNT_TOKEN',\n  vendorNumber:'+***********',\n  publicUrl:\"public_url\",//optional\n});\n\n```\nCopyCopied!\n[More info about Twilio provider](https://www.builderbot.app/en/</providers/twilio>)\n## [Baileys](https://www.builderbot.app/en/</en/providers#baileys>)\n[Baileys](https://www.builderbot.app/en/<https:/whiskeysockets.github.io/>) is an open source project which allows sending messages, receiving messages and dozens of other features by implementing WebSocket in a version of whatsapp. It is a project with great trajectory driven by people with great knowledge of the subject, you can deepen in this library directly in its documentation or [repository](https://www.builderbot.app/en/<https:/github.com/WhiskeySockets/Baileys>).\nBecause this is a free provider that emulates the whatsapp web interface, you must scan the QR to log in.\nIf you want to see more examples taking full advantage of this provider in conjunction with the builderbot library you can check this **@jorgechavarriaga** [repository](https://www.builderbot.app/en/<https:/github.com/jorgechavarriaga/builder_bot_baileys_examples>)\nbaileys-provider.tsbaileys-provider-extend.tsapp.ts\n```\nimport { createProvider } from\"@builderbot/bot\";\nimport { BaileysProvider } from'@builderbot/provider-baileys';\nexporttypeIProvider=typeof BaileysProvider\nexportconstadapterProvider=createProvider(BaileysProvider)\n\n```\nCopyCopied!\n## [Venom](https://www.builderbot.app/en/</en/providers#venom>)\n[Venom](https://www.builderbot.app/en/<https:/github.com/orkestral/venom>) is an open-source project that utilizes JavaScript to create high-performance bots for WhatsApp. It supports a wide range of interactions including customer care, media sending, AI-based phrase recognition, and various architectural designs tailored for WhatsApp. You can visit their [official](https://www.builderbot.app/en/<https:/orkestral.io/>) website as the repository to understand other features you can use.\nBecause this is a free provider that emulates the whatsapp web interface, you must scan the QR to log in.\nvenom-provider.tsvenom-provider-extend.tsapp.ts\n```\nimport { createProvider } from\"@builderbot/bot\";\nimport { VenomProvider } from'@builderbot/provider-venom';\nexporttypeIProvider=typeof VenomProvider\nexportconstadapterProvider=createProvider(VenomProvider)\n\n```\nCopyCopied!\n## [WPPConnect](https://www.builderbot.app/en/</en/providers#wpp-connect>)\n[WPPConnect](https://www.builderbot.app/en/<https:/wppconnect.io>) is an open source project developed by the JavaScript community with the aim of exporting functions from WhatsApp Web to the node, which can be used to support the creation of any interaction, such as customer service, media sending, intelligence recognition based on phrases artificial and many other things, use your imagination You can visit their official website as the [repository](https://www.builderbot.app/en/<https:/github.com/wppconnect-team/wppconnect>) to understand other features you can use.\nBecause this is a free provider that emulates the whatsapp web interface, you must scan the QR to log in.\nwppconnect-provider.tswppconnect-provider-extend.tsapp.ts\n```\nimport { createProvider } from\"@builderbot/bot\";\nimport { WPPConnectProvider } from'@builderbot/provider-wppconnect';\nexporttypeIProvider=typeof WPPConnectProvider\nexportconstadapterProvider=createProvider(WPPConnectProvider)\n\n```\nCopyCopied!\nWali.chat Provider\n## [Custom Provider](https://www.builderbot.app/en/</en/providers#custom-provider>)\nCustom provider there is the possibility to build your own customized adapter, we know that there are many more providers that can be very useful, an example can be [Wali.chat](https://www.builderbot.app/en/<https:/wali.chat/?ref=7v34k0>) which apart from giving us the possibility to interact via API Rest also offers a Dashboard to visualize and share data with your agents.\napp.tsprovider/wali.tsprovider/wali.events.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword, utils } from'@builderbot/bot'\nimport { MemoryDB as Database } from'@builderbot/bot'\nimport { WaliProvider as Provider } from'./provider/wali'\nconstPORT=process.env.PORT??3008\nconstfullSamplesFlow=addKeyword<Provider,Database>(['samples',utils.setEvent('SAMPLES')])\n.addAnswer(`💪 I'll send you a lot files...`)\n.addAnswer(`Send video from URL`,\n    { media:'https://media.giphy.com/media/v1.Y2lkPTc5MGI3NjExYTJ0ZGdjd2syeXAwMjQ4aWdkcW04OWlqcXI3Ynh1ODkwZ25zZWZ1dCZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/LCohAb657pSdHv0Q5h/giphy.mp4' }\n  )\n.addAnswer(`Send audio from URL`,\n    { media:'https://cdn.freesound.org/previews/728/728142_11861866-lq.mp3' }\n  )\n.addAnswer(`Send file from URL`,\n    { media:'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf' }\n  )\n\nconstmain=async () => {\nconstadapterFlow=createFlow([fullSamplesFlow])\nconstadapterProvider=createProvider(Provider, {\n    token:process.env.TOKEN,\n    deviceId:process.env.DEVICE_ID\n  })\nconstadapterDB=newDatabase()\nconst { httpServer } =awaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\nhttpServer(+PORT)\n}\nmain()\n\n```\nCopyCopied!\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "import { createProvider } from \"@builderbot/bot\";\nimport { MetaProvider } from '@builderbot/provider-meta';\n\nexport type IProvider = typeof MetaProvider\nexport const adapterProvider = createProvider(MetaProvider, {\n    jwtToken: 'jwtToken',\n    numberId: 'numberId',\n    verifyToken: 'verifyToken',\n    version: 'v16.0',\n})\n"}, {"language": "language-ts", "code": "import { createProvider } from \"@builderbot/bot\";\nimport { MetaProvider } from '@builderbot/provider-meta';\n\nexport type IProvider = typeof MetaProvider\nexport const adapterProvider = createProvider(MetaProvider, {\n    jwtToken: 'jwtToken',\n    numberId: 'numberId',\n    verifyToken: 'verifyToken',\n    version: 'v16.0',\n})\n"}, {"language": "language-ts", "code": "import { createProvider } from \"@builderbot/bot\";\nimport { TwilioProvider } from '@builderbot/provider-twilio';\n\nexport type IProvider = typeof TwilioProvider\nexport const adapterProvider = createProvider(TwilioProvider, {\n    accountSid: 'YOUR_ACCOUNT_SID',\n    authToken: 'YOUR_ACCOUNT_TOKEN',\n    vendorNumber: '+***********',\n    publicUrl: \"public_url\", //optional\n});\n"}, {"language": "language-ts", "code": "import { createProvider } from \"@builderbot/bot\";\nimport { BaileysProvider } from '@builderbot/provider-baileys';\n\nexport type IProvider = typeof BaileysProvider\nexport const adapterProvider = createProvider(BaileysProvider)\n\n"}, {"language": "language-ts", "code": "import { createProvider } from \"@builderbot/bot\";\nimport { VenomProvider } from '@builderbot/provider-venom';\n\nexport type IProvider = typeof VenomProvider\nexport const adapterProvider = createProvider(VenomProvider)\n"}, {"language": "language-ts", "code": "import { createProvider } from \"@builderbot/bot\";\nimport { WPPConnectProvider } from '@builderbot/provider-wppconnect';\n\nexport type IProvider = typeof WPPConnectProvider\nexport const adapterProvider = createProvider(WPPConnectProvider)\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, createFlow, addKeyword, utils } from '@builderbot/bot'\nimport { MemoryDB as Database } from '@builderbot/bot'\nimport { WaliProvider as Provider } from './provider/wali'\n\nconst PORT = process.env.PORT ?? 3008\n\nconst fullSamplesFlow = addKeyword<Provider, Database>(['samples', utils.setEvent('SAMPLES')])\n    .addAnswer(`💪 I'll send you a lot files...`)\n    .addAnswer(`Send video from URL`,\n        { media: 'https://media.giphy.com/media/v1.Y2lkPTc5MGI3NjExYTJ0ZGdjd2syeXAwMjQ4aWdkcW04OWlqcXI3Ynh1ODkwZ25zZWZ1dCZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/LCohAb657pSdHv0Q5h/giphy.mp4' }\n    )\n    .addAnswer(`Send audio from URL`,\n        { media: 'https://cdn.freesound.org/previews/728/728142_11861866-lq.mp3' }\n    )\n    .addAnswer(`Send file from URL`,\n        { media: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf' }\n    )\n\n\nconst main = async () => {\n    const adapterFlow = createFlow([fullSamplesFlow])\n\n    const adapterProvider = createProvider(Provider, {\n        token: process.env.TOKEN,\n        deviceId: process.env.DEVICE_ID\n    })\n    const adapterDB = new Database()\n\n    const { httpServer } = await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n\n    httpServer(+PORT)\n}\n\nmain()\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/providers#wpp-connect", "title": "Providers - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/providers#>)\n  * [Support](https://www.builderbot.app/en/</en/providers#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/providers#>)\n\n\n# Providers\nProviders are in charge of providing the communication bridge between your bot and **whatsapp** , **telegram** or custom provider.\nYou can change your provider very easily without affecting the logic of your bot. It is as simple as implementing the connector of the provider of your choice. At the moment we have integrations with more than four suppliers.\nWhatsapp: [Meta](https://www.builderbot.app/en/</en/providers#meta>), [Twilio](https://www.builderbot.app/en/</en/providers#twilio>), [Baileys](https://www.builderbot.app/en/</en/providers#baileys>), [WPPConnect](https://www.builderbot.app/en/</en/providers#wpp-connect>), [Venom](https://www.builderbot.app/en/</en/providers#venom>), [Telegram](https://www.builderbot.app/en/</plugins/telegram>), [Custom Provider](https://www.builderbot.app/en/</en/providers#custom-provider>)\nBOT\nUser\nuser\naddAnswer\nflows\nTwilio\nMeta\nTelegram\ncustom\nProvider\nEach provider may need to adjust access keys, settings, among other properties that are usually passed as an object in the `createProvider` function.\nmeta-provider.tstwilio-provider.tsbaileys-provider.tsvenom-provider.tswppconnect-provider.ts\n```\nimport { createProvider } from\"@builderbot/bot\";\nimport { MetaProvider } from'@builderbot/provider-meta';\nexporttypeIProvider=typeof MetaProvider\nexportconstadapterProvider=createProvider(MetaProvider, {\n  jwtToken:'jwtToken',\n  numberId:'numberId',\n  verifyToken:'verifyToken',\n  version:'v16.0',\n})\n\n```\nCopyCopied!\nBelow you will find more information about each of these providers.\n## [Meta](https://www.builderbot.app/en/</en/providers#meta>)\nThe [WhatsApp Business Platform](https://www.builderbot.app/en/<https:/business.whatsapp.com/products/business-platform>) enables medium and large businesses to communicate with their customers on a large scale. You can initiate conversations with customers in just minutes, send them customer service notifications or purchase updates, offer them a personalized level of service, and provide support through the channel of their choice.\nmeta-provider.tsapp.ts\n```\nimport { createProvider } from\"@builderbot/bot\";\nimport { MetaProvider } from'@builderbot/provider-meta';\nexporttypeIProvider=typeof MetaProvider\nexportconstadapterProvider=createProvider(MetaProvider, {\n  jwtToken:'jwtToken',\n  numberId:'numberId',\n  verifyToken:'verifyToken',\n  version:'v16.0',\n})\n\n```\nCopyCopied!\n[More info about Meta provider](https://www.builderbot.app/en/</providers/meta>)\n## [Twilio](https://www.builderbot.app/en/</en/providers#twilio>)\n[Twilio](https://www.builderbot.app/en/<https:/www.twilio.com/en-us/messaging/channels/whatsapp>) is a development platform that enables developers to build cloud communication applications and web systems. Twilio's communications APIs enable businesses to provide the right communication experience for their customers within web and mobile applications. By using Twilio APIs, developers can quickly add this functionality to an application, such as voice messaging, video calls, text messaging and more.\ntwilio-provider.tsapp.ts\n```\nimport { createProvider } from\"@builderbot/bot\";\nimport { TwilioProvider } from'@builderbot/provider-twilio';\nexporttypeIProvider=typeof TwilioProvider\nexportconstadapterProvider=createProvider(TwilioProvider, {\n  accountSid:'YOUR_ACCOUNT_SID',\n  authToken:'YOUR_ACCOUNT_TOKEN',\n  vendorNumber:'+***********',\n  publicUrl:\"public_url\",//optional\n});\n\n```\nCopyCopied!\n[More info about Twilio provider](https://www.builderbot.app/en/</providers/twilio>)\n## [Baileys](https://www.builderbot.app/en/</en/providers#baileys>)\n[Baileys](https://www.builderbot.app/en/<https:/whiskeysockets.github.io/>) is an open source project which allows sending messages, receiving messages and dozens of other features by implementing WebSocket in a version of whatsapp. It is a project with great trajectory driven by people with great knowledge of the subject, you can deepen in this library directly in its documentation or [repository](https://www.builderbot.app/en/<https:/github.com/WhiskeySockets/Baileys>).\nBecause this is a free provider that emulates the whatsapp web interface, you must scan the QR to log in.\nIf you want to see more examples taking full advantage of this provider in conjunction with the builderbot library you can check this **@jorgechavarriaga** [repository](https://www.builderbot.app/en/<https:/github.com/jorgechavarriaga/builder_bot_baileys_examples>)\nbaileys-provider.tsbaileys-provider-extend.tsapp.ts\n```\nimport { createProvider } from\"@builderbot/bot\";\nimport { BaileysProvider } from'@builderbot/provider-baileys';\nexporttypeIProvider=typeof BaileysProvider\nexportconstadapterProvider=createProvider(BaileysProvider)\n\n```\nCopyCopied!\n## [Venom](https://www.builderbot.app/en/</en/providers#venom>)\n[Venom](https://www.builderbot.app/en/<https:/github.com/orkestral/venom>) is an open-source project that utilizes JavaScript to create high-performance bots for WhatsApp. It supports a wide range of interactions including customer care, media sending, AI-based phrase recognition, and various architectural designs tailored for WhatsApp. You can visit their [official](https://www.builderbot.app/en/<https:/orkestral.io/>) website as the repository to understand other features you can use.\nBecause this is a free provider that emulates the whatsapp web interface, you must scan the QR to log in.\nvenom-provider.tsvenom-provider-extend.tsapp.ts\n```\nimport { createProvider } from\"@builderbot/bot\";\nimport { VenomProvider } from'@builderbot/provider-venom';\nexporttypeIProvider=typeof VenomProvider\nexportconstadapterProvider=createProvider(VenomProvider)\n\n```\nCopyCopied!\n## [WPPConnect](https://www.builderbot.app/en/</en/providers#wpp-connect>)\n[WPPConnect](https://www.builderbot.app/en/<https:/wppconnect.io>) is an open source project developed by the JavaScript community with the aim of exporting functions from WhatsApp Web to the node, which can be used to support the creation of any interaction, such as customer service, media sending, intelligence recognition based on phrases artificial and many other things, use your imagination You can visit their official website as the [repository](https://www.builderbot.app/en/<https:/github.com/wppconnect-team/wppconnect>) to understand other features you can use.\nBecause this is a free provider that emulates the whatsapp web interface, you must scan the QR to log in.\nwppconnect-provider.tswppconnect-provider-extend.tsapp.ts\n```\nimport { createProvider } from\"@builderbot/bot\";\nimport { WPPConnectProvider } from'@builderbot/provider-wppconnect';\nexporttypeIProvider=typeof WPPConnectProvider\nexportconstadapterProvider=createProvider(WPPConnectProvider)\n\n```\nCopyCopied!\nWali.chat Provider\n## [Custom Provider](https://www.builderbot.app/en/</en/providers#custom-provider>)\nCustom provider there is the possibility to build your own customized adapter, we know that there are many more providers that can be very useful, an example can be [Wali.chat](https://www.builderbot.app/en/<https:/wali.chat/?ref=7v34k0>) which apart from giving us the possibility to interact via API Rest also offers a Dashboard to visualize and share data with your agents.\napp.tsprovider/wali.tsprovider/wali.events.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword, utils } from'@builderbot/bot'\nimport { MemoryDB as Database } from'@builderbot/bot'\nimport { WaliProvider as Provider } from'./provider/wali'\nconstPORT=process.env.PORT??3008\nconstfullSamplesFlow=addKeyword<Provider,Database>(['samples',utils.setEvent('SAMPLES')])\n.addAnswer(`💪 I'll send you a lot files...`)\n.addAnswer(`Send video from URL`,\n    { media:'https://media.giphy.com/media/v1.Y2lkPTc5MGI3NjExYTJ0ZGdjd2syeXAwMjQ4aWdkcW04OWlqcXI3Ynh1ODkwZ25zZWZ1dCZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/LCohAb657pSdHv0Q5h/giphy.mp4' }\n  )\n.addAnswer(`Send audio from URL`,\n    { media:'https://cdn.freesound.org/previews/728/728142_11861866-lq.mp3' }\n  )\n.addAnswer(`Send file from URL`,\n    { media:'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf' }\n  )\n\nconstmain=async () => {\nconstadapterFlow=createFlow([fullSamplesFlow])\nconstadapterProvider=createProvider(Provider, {\n    token:process.env.TOKEN,\n    deviceId:process.env.DEVICE_ID\n  })\nconstadapterDB=newDatabase()\nconst { httpServer } =awaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\nhttpServer(+PORT)\n}\nmain()\n\n```\nCopyCopied!\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "import { createProvider } from \"@builderbot/bot\";\nimport { MetaProvider } from '@builderbot/provider-meta';\n\nexport type IProvider = typeof MetaProvider\nexport const adapterProvider = createProvider(MetaProvider, {\n    jwtToken: 'jwtToken',\n    numberId: 'numberId',\n    verifyToken: 'verifyToken',\n    version: 'v16.0',\n})\n"}, {"language": "language-ts", "code": "import { createProvider } from \"@builderbot/bot\";\nimport { MetaProvider } from '@builderbot/provider-meta';\n\nexport type IProvider = typeof MetaProvider\nexport const adapterProvider = createProvider(MetaProvider, {\n    jwtToken: 'jwtToken',\n    numberId: 'numberId',\n    verifyToken: 'verifyToken',\n    version: 'v16.0',\n})\n"}, {"language": "language-ts", "code": "import { createProvider } from \"@builderbot/bot\";\nimport { TwilioProvider } from '@builderbot/provider-twilio';\n\nexport type IProvider = typeof TwilioProvider\nexport const adapterProvider = createProvider(TwilioProvider, {\n    accountSid: 'YOUR_ACCOUNT_SID',\n    authToken: 'YOUR_ACCOUNT_TOKEN',\n    vendorNumber: '+***********',\n    publicUrl: \"public_url\", //optional\n});\n"}, {"language": "language-ts", "code": "import { createProvider } from \"@builderbot/bot\";\nimport { BaileysProvider } from '@builderbot/provider-baileys';\n\nexport type IProvider = typeof BaileysProvider\nexport const adapterProvider = createProvider(BaileysProvider)\n\n"}, {"language": "language-ts", "code": "import { createProvider } from \"@builderbot/bot\";\nimport { VenomProvider } from '@builderbot/provider-venom';\n\nexport type IProvider = typeof VenomProvider\nexport const adapterProvider = createProvider(VenomProvider)\n"}, {"language": "language-ts", "code": "import { createProvider } from \"@builderbot/bot\";\nimport { WPPConnectProvider } from '@builderbot/provider-wppconnect';\n\nexport type IProvider = typeof WPPConnectProvider\nexport const adapterProvider = createProvider(WPPConnectProvider)\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, createFlow, addKeyword, utils } from '@builderbot/bot'\nimport { MemoryDB as Database } from '@builderbot/bot'\nimport { WaliProvider as Provider } from './provider/wali'\n\nconst PORT = process.env.PORT ?? 3008\n\nconst fullSamplesFlow = addKeyword<Provider, Database>(['samples', utils.setEvent('SAMPLES')])\n    .addAnswer(`💪 I'll send you a lot files...`)\n    .addAnswer(`Send video from URL`,\n        { media: 'https://media.giphy.com/media/v1.Y2lkPTc5MGI3NjExYTJ0ZGdjd2syeXAwMjQ4aWdkcW04OWlqcXI3Ynh1ODkwZ25zZWZ1dCZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/LCohAb657pSdHv0Q5h/giphy.mp4' }\n    )\n    .addAnswer(`Send audio from URL`,\n        { media: 'https://cdn.freesound.org/previews/728/728142_11861866-lq.mp3' }\n    )\n    .addAnswer(`Send file from URL`,\n        { media: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf' }\n    )\n\n\nconst main = async () => {\n    const adapterFlow = createFlow([fullSamplesFlow])\n\n    const adapterProvider = createProvider(Provider, {\n        token: process.env.TOKEN,\n        deviceId: process.env.DEVICE_ID\n    })\n    const adapterDB = new Database()\n\n    const { httpServer } = await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n\n    httpServer(+PORT)\n}\n\nmain()\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/deploy", "title": "Deploying - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/deploy#>)\n  * [Support](https://www.builderbot.app/en/</en/deploy#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/deploy#>)\n\n\n# Deploying\nCongratulations the time has come to take your project to production and for your convenience there are several ways to achieve this from quite tenic options to other easier strategies.\nIt should be noted that for the application to function correctly, the execution must be kept alive, which means that a server running the application code must be maintained. Therefore, **it is not recommended to use serverless** services.\nDeployment methods: [Railway](https://www.builderbot.app/en/</deploy/railway>), [Docker](https://www.builderbot.app/en/</deploy/docker>), [VPS](https://www.builderbot.app/en/</deploy/vps>)\nEasy\n## [Railway](https://www.builderbot.app/en/</en/deploy#railway>)\n[Railway](https://www.builderbot.app/en/<https:/railway.app?referralCode=jyd_0y>) provides infrastructure primitives with sane defaults to manage secrets, builds, and deploys. So you can start quickly and focus on your product.\nUsing this service, you won't have to worry about anything as it is capable of automatically reading the different packages necessary for your application to function correctly, in addition to providing you with a subdomain with HTTPS security.\n### Start a new project\n![](https://i.imgur.com/Zbr5YDO.png)\n[More info about it](https://www.builderbot.app/en/</deploy/railway>)\nMedium\n## [Docker](https://www.builderbot.app/en/</en/deploy#docker>)\n[Dockerizing](https://www.builderbot.app/en/<https:/docs.docker.com/engine/install/>) the application is the best option if you are looking for a more personalized and scalable solution. It is also important to note that today most cloud providers support Docker containers and offer automated deployment processes. Bot templates come with a default `Dockerfile` for basic operation. Keep in mind that depending on other libraries you use, this file may need to be adapted for proper functioning.\n### List of bots\n![](https://i.imgur.com/lNW9hH2.png)\n[More info about it](https://www.builderbot.app/en/</deploy/docker>)\nHard\n## [VPS](https://www.builderbot.app/en/</en/deploy#vps>)\nShould you be considering the deployment of your bot onto a private server (VPS), our comprehensive guide is tailored specifically for Ubuntu server environments. This meticulously crafted, step-by-step manual not only provides detailed instructions but also offers insightful guidance on the critical factors to consider throughout the deployment process. By following this guide, you can navigate the intricacies of VPS deployment with confidence, ensuring a smooth and successful integration of your bot into your desired hosting environment.\n### Run on Ubuntu VPS\n![](https://i.imgur.com/FFMp4Jd.png)\n[More info about it](https://www.builderbot.app/en/</deploy/vps>)\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [], "sections": []}, {"url": "https://www.builderbot.app/en/deploy/railway", "title": "Railway - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/deploy/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/deploy/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/deploy/</>)\n  * [Contribute](https://www.builderbot.app/en/deploy/</contribute>)\n  * [Course](https://www.builderbot.app/en/deploy/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/deploy/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/deploy/</>)\n  * [Documentation](https://www.builderbot.app/en/deploy/</en/deploy/railway#>)\n  * [Support](https://www.builderbot.app/en/deploy/</en/deploy/railway#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/deploy/</>)\n    * [Quickstart](https://www.builderbot.app/en/deploy/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/deploy/</concepts>)\n    * [Examples](https://www.builderbot.app/en/deploy/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/deploy/</add-functions>)\n    * [Context](https://www.builderbot.app/en/deploy/</context>)\n    * [Methods](https://www.builderbot.app/en/deploy/</methods>)\n    * [Events](https://www.builderbot.app/en/deploy/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/deploy/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/deploy/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/deploy/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/deploy/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/deploy/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/deploy/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/deploy/</deploy>)\n    * [Railway](https://www.builderbot.app/en/deploy/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/deploy/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/deploy/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/deploy/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/deploy/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/deploy/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/deploy/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/deploy/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/deploy/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/deploy/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/deploy/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/deploy/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/deploy/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/deploy/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/deploy/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/deploy/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/deploy/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/deploy/</contribute>)\n    * [Core](https://www.builderbot.app/en/deploy/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/deploy/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/deploy/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/deploy/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/deploy/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/deploy/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/deploy/</en/deploy/railway#>)\n\n\n# Railway\n[Railway](https://www.builderbot.app/en/deploy/<https:/railway.app?referralCode=jyd_0y>) provides infrastructure primitives with sane defaults to manage secrets, builds, and deploys. So you can start quickly and focus on your product.\nUsing this service, you won't have to worry about anything as it is capable of automatically reading the different packages necessary for your application to function correctly, in addition to providing you with a subdomain with HTTPS security.\n## [Railway Step by Step](https://www.builderbot.app/en/deploy/</en/deploy/railway#railway-step-by-step>)\nThe first thing we need to do is click on start a new project and follow the steps you will find below\n### Start a new project\n![](https://i.imgur.com/Zbr5YDO.png)\nNow with your Github account linked in railway, you will be able to see the list of all your projects available for deployment, select the one corresponding to builderbot\n### To begin, register and click on Start a new project\n![](https://i.imgur.com/2SOCLIB.png)\nDepending on whether your project requires environment variables `.env`, you should select Add variables or simply click on Deploy Now.\n### Select the GitHub project\n![](https://i.imgur.com/p2Z2NS9.png)\nAfter clicking on deploy, you will find a screen similar to the following where we can see the construction and deployment process of your project. Depending on the number of packages you have installed, this process may take approximately **4 to 10 minutes**. If you see that this time has elapsed and it is still running, try refreshing the browser.\n### Deploying\n![](https://i.imgur.com/U0bdh5x.png)\nIn the settings section, you will find a section called \"networking\" which has the ability to generate a domain, click on this will generate a domain with an SSL certificate that will serve you to use as a webhook, API, or even a portal to scan the QR code.\n### HTTPS Domain\n![](https://i.imgur.com/fy80sJG.png)\nNow you have your project deployed on railway, you can repeat these steps for each bot you want to deploy.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/deploy/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/deploy/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/deploy/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [], "sections": []}, {"url": "https://www.builderbot.app/en/deploy/docker", "title": "Deploy - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/deploy/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/deploy/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/deploy/</>)\n  * [Contribute](https://www.builderbot.app/en/deploy/</contribute>)\n  * [Course](https://www.builderbot.app/en/deploy/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/deploy/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/deploy/</>)\n  * [Documentation](https://www.builderbot.app/en/deploy/</en/deploy/docker#>)\n  * [Support](https://www.builderbot.app/en/deploy/</en/deploy/docker#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/deploy/</>)\n    * [Quickstart](https://www.builderbot.app/en/deploy/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/deploy/</concepts>)\n    * [Examples](https://www.builderbot.app/en/deploy/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/deploy/</add-functions>)\n    * [Context](https://www.builderbot.app/en/deploy/</context>)\n    * [Methods](https://www.builderbot.app/en/deploy/</methods>)\n    * [Events](https://www.builderbot.app/en/deploy/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/deploy/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/deploy/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/deploy/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/deploy/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/deploy/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/deploy/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/deploy/</deploy>)\n    * [Railway](https://www.builderbot.app/en/deploy/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/deploy/</deploy/docker>)\n      * [Build Image](https://www.builderbot.app/en/deploy/</en/deploy/docker#build-image>)\n      * [Run Container](https://www.builderbot.app/en/deploy/</en/deploy/docker#run-container>)\n      * [Portainer](https://www.builderbot.app/en/deploy/</en/deploy/docker#portainer>)\n    * [VPS](https://www.builderbot.app/en/deploy/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/deploy/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/deploy/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/deploy/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/deploy/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/deploy/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/deploy/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/deploy/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/deploy/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/deploy/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/deploy/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/deploy/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/deploy/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/deploy/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/deploy/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/deploy/</contribute>)\n    * [Core](https://www.builderbot.app/en/deploy/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/deploy/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/deploy/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/deploy/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/deploy/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/deploy/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/deploy/</en/deploy/docker#>)\n\n\n# Deploy\nPreviously, you need to have [Docker installed](https://www.builderbot.app/en/deploy/<https:/docs.docker.com/get-docker/>) on your server depending on the operating system, the processes change, you can find all the official information about docker at this link.\nDepending on the provider you have chosen, you will need a specific Docker implementation, but don't worry, as it comes automatically implemented in a file called Dockerfile, you can also view the other Dockerfiles in the templates section.\nDockerfile, [Build Image](https://www.builderbot.app/en/deploy/</en/deploy/docker#build-image>), [Run Container](https://www.builderbot.app/en/deploy/</en/deploy/docker#run-container>), [Portainer](https://www.builderbot.app/en/deploy/</en/deploy/docker#portainer>)\n### Dockerfile\n```\n# THIS IS THE BASE IMAGE FOR THE BOT\nFROM node:21-alpine3.18 as builder\n# Enable Corepack and prepare for PNPM installation to increase performance\nRUN corepack enable && corepack prepare pnpm@latest --activate\nENV PNPM_HOME=/usr/local/bin\n# Set the working directory\nWORKDIR /app\n# Copy package.json and pnpm-lock.yaml files to the working directory\nCOPY package*.json pnpm-lock.yaml ./\n# Install dependencies using PNPM\nCOPY . .\nRUN pnpm i\n# Create a new stage for deployment\nFROM builder as deploy\n# Copy only necessary files and directories for deployment\nCOPY --from=builder /app/src ./src\nCOPY --from=builder /app/package.json /app/pnpm-lock.yaml ./\nRUN pnpm install\nCMD [\"pnpm\", \"start\"]\n\n```\nCopyCopied!\n## [Build Image](https://www.builderbot.app/en/deploy/</en/deploy/docker#build-image>)\nIn this step, we are going to build the Docker image with all the settings of your project, remember that depending on the libraries you are using, you may need to modify the `Dockerfile` for it to function correctly.\n### Build Image\n```\ndocker build . -t builderbot:latest\n\n```\nCopyCopied!\nYou can see in your console that the image starts to build and the process logs are coming out, you must ensure that it ends with `naming to docker.io/library/builderbot:latest`\n```\n => [internal] load build definition from Dockerfile                       0.4s\n => [internal] load metadata for docker.io/library/node:21-alpine3.18              3.6s\n => ....................\n => => naming to docker.io/library/builderbot:latest\n\n```\nCopyCopied!\n## [Run Container](https://www.builderbot.app/en/deploy/</en/deploy/docker#run-container>)\nTo run your containerized image by passing different environment variables depending on the configuration needed, you can do it in the following way.\nRemember to release the corresponding ports in your server's firewall. Example 3008\nRun Container FullRun Container Quick\n```\ndocker rm -f bot 2>/dev/null\ndocker run \\\n --name \"bot\" \\\n --env OPENAI_API_KEY=\"your_api_key_value\" \\\n --env PORT=3008 \\\n -p 3008:3008/tcp \\\n -v \"$(pwd)/bot_sessions:/app/bot_sessions:rw\" \\\n --cap-add SYS_ADMIN \\\n --restart always \\\n builderbot:latest\n\n```\nCopyCopied!\nCommand| Explanation  \n---|---  \n**`docker rm -f bot 2>/dev/null`**| Deletes any running container named \"bot\", if it exists. The`2>/dev/null` redirects any error message to nothing, meaning if the container doesn't exist, no error message will be displayed.  \n**`docker run -d`**| Starts a new Docker container in detached mode (in the background).  \n**`--name \"bot\"`**| Assigns the name \"amazing-bot\" to the container.  \n**`--env OPENAI_API_KEY=\"API\"`**| Sets the environment variable`OPENAI_API_KEY` inside the container to the provided value, which should be a valid API key to access OpenAI.  \n**`--user $(id -u):$(id -g)`**| Specifies the user and group under which the process will run inside the container.`$(id -u)` gets the current user ID of the system and `$(id -g)` gets the current group ID.  \n**`-p 3008:3008/tcp`**| Publishes port 3008 of the container to port 3008 of the host, allowing traffic to be directed to the container's port.  \n**`-v $(pwd)/bot_sessions:/app/bot_sessions:rw`**| Mounts the host directory`/root/sessions/bot_sessions` to the container directory `/app/bot_sessions` with read-write permissions (`rw`).  \n**`--cap-add SYS_ADMIN`**| Grants the container the`SYS_ADMIN` capability, providing a broad set of system administration capabilities.  \n**`--restart always`**| Configures the container to restart automatically if it stops or encounters an error.  \n**`builderbot:latest`**| Specifies the Docker image to be used for creating and running the container. In this case,`builderbot:latest` is the image tag.  \n## [Portainer](https://www.builderbot.app/en/deploy/</en/deploy/docker#portainer>)\nIf you are already familiar with docker and want to manage different bots mounted in containers a good OpenSource option is portainer.\n[Portainer](https://www.builderbot.app/en/deploy/<https:/docs.portainer.io/>) Community Edition is a user-friendly platform tailored for managing containerized applications across various environments like Docker, Swarm, Kubernetes, and ACI. With its straightforward deployment and intuitive interface, it simplifies the management of container resources such as images, volumes, and networks through a graphical interface or an API. Portainer operates within a single container, adaptable to both Linux and Windows environments.\n### List of bots\n![](https://i.imgur.com/lNW9hH2.png)\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/deploy/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/deploy/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/deploy/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-docker", "code": "# THIS IS THE BASE IMAGE FOR THE BOT\nFROM node:21-alpine3.18 as builder\n\n# Enable Corepack and prepare for PNPM installation to increase performance\nRUN corepack enable && corepack prepare pnpm@latest --activate\nENV PNPM_HOME=/usr/local/bin\n\n# Set the working directory\nWORKDIR /app\n\n# Copy package.json and pnpm-lock.yaml files to the working directory\nCOPY package*.json pnpm-lock.yaml ./\n\n# Install dependencies using PNPM\nCOPY . .\nRUN pnpm i\n\n# Create a new stage for deployment\nFROM builder as deploy\n\n# Copy only necessary files and directories for deployment\nCOPY --from=builder /app/src ./src\nCOPY --from=builder /app/package.json /app/pnpm-lock.yaml ./\n\nRUN pnpm install\nCMD [\"pnpm\", \"start\"]\n"}, {"language": "text", "code": "docker build . -t builderbot:latest\n"}, {"language": "text", "code": " => [internal] load build definition from Dockerfile                                             0.4s\n => [internal] load metadata for docker.io/library/node:21-alpine3.18                            3.6s\n => ....................\n => => naming to docker.io/library/builderbot:latest\n"}, {"language": "text", "code": "docker rm -f bot 2>/dev/null\ndocker run \\\n  --name \"bot\" \\\n  --env OPENAI_API_KEY=\"your_api_key_value\" \\\n  --env PORT=3008 \\\n  -p 3008:3008/tcp \\\n  -v \"$(pwd)/bot_sessions:/app/bot_sessions:rw\" \\\n  --cap-add SYS_ADMIN \\\n  --restart always \\\n  builderbot:latest\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/deploy/vps", "title": "VPS (Virtual Private Server) - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/deploy/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/deploy/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/deploy/</>)\n  * [Contribute](https://www.builderbot.app/en/deploy/</contribute>)\n  * [Course](https://www.builderbot.app/en/deploy/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/deploy/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/deploy/</>)\n  * [Documentation](https://www.builderbot.app/en/deploy/</en/deploy/vps#>)\n  * [Support](https://www.builderbot.app/en/deploy/</en/deploy/vps#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/deploy/</>)\n    * [Quickstart](https://www.builderbot.app/en/deploy/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/deploy/</concepts>)\n    * [Examples](https://www.builderbot.app/en/deploy/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/deploy/</add-functions>)\n    * [Context](https://www.builderbot.app/en/deploy/</context>)\n    * [Methods](https://www.builderbot.app/en/deploy/</methods>)\n    * [Events](https://www.builderbot.app/en/deploy/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/deploy/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/deploy/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/deploy/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/deploy/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/deploy/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/deploy/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/deploy/</deploy>)\n    * [Railway](https://www.builderbot.app/en/deploy/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/deploy/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/deploy/</deploy/vps>)\n      * [Pre-requirements](https://www.builderbot.app/en/deploy/</en/deploy/vps#pre-requirements>)\n      * [Install](https://www.builderbot.app/en/deploy/</en/deploy/vps#install>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/deploy/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/deploy/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/deploy/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/deploy/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/deploy/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/deploy/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/deploy/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/deploy/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/deploy/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/deploy/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/deploy/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/deploy/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/deploy/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/deploy/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/deploy/</contribute>)\n    * [Core](https://www.builderbot.app/en/deploy/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/deploy/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/deploy/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/deploy/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/deploy/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/deploy/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/deploy/</en/deploy/vps#>)\n\n\n# VPS (Virtual Private Server)\nPrioritize initiating a connection to your server using SSH or via your chosen cloud provider's preferred method. This initial step is essential to start following this entire tutorial.\nIf you are using windows I recommend [git-scm](https://www.builderbot.app/en/deploy/<https:/git-scm.com/download/win>), otherwise if you are using Linux or Mac you already have git by default.\n```\n<EMAIL>-pXX\nsudoapt-getupdate&&sudoapt-getupgrade-y\n\n```\nCopyCopied!\n## [Pre-requirements](https://www.builderbot.app/en/deploy/</en/deploy/vps#pre-requirements>)\nFirst you must download the [NodeJS installation](https://www.builderbot.app/en/deploy/<https:/nodejs.org/en/download/package-manager>) depending on your operating system but this guide focuses on Ubuntu 22. It is important to run the following commands in order for the changes to take effect\n###  Install NVM (Node Version Manager)\n```\ncurl-o-https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh|bash\n\n```\nCopyCopied!\n###  Set NVM environment variables\n```\nexport NVM_DIR=\"$HOME/.nvm\"\n[ -s\"$NVM_DIR/nvm.sh\" ] &&\\.\"$NVM_DIR/nvm.sh\"\n[ -s\"$NVM_DIR/bash_completion\" ] &&\\.\"$NVM_DIR/bash_completion\"\n\n```\nCopyCopied!\n###  Install Node.js version 20\n```\nnvminstall20\n\n```\nCopyCopied!\nNow you have node installed on your server ready to use, the following steps will be performed inside the directory of your project you are working on.\nWe will proceed to install [pnpm](https://www.builderbot.app/en/deploy/<https:/pnpm.io/>) the node package manager that we use and we will also install [pm2](https://www.builderbot.app/en/deploy/<https:/pm2.keymetrics.io/docs/usage/quick-start/>) that daemon which helps us to keep node applications running in the background.\n###  Install pnpm and pm2\n```\nnpmipnpm@latestpm2--global\n\n```\nCopyCopied!\nNow we proceed to clone the builderbot-examples project and install the dependencies necessary for its operation.\nRemember to release the corresponding ports in your server's firewall. Example 3008\n## [Install](https://www.builderbot.app/en/deploy/</en/deploy/vps#install>)\n###  Clone Project\n```\ngitclonehttps://github.com/codigoencasa/builderbot-examples-bot.git\ncdbuilderbot-examples-bot\n\n```\nCopyCopied!\n###  Install\n/root/builderbot-examples-bot\n```\npnpminstall\n\n```\nCopyCopied!\nComo es un proyecto en TypeScript necesitamos hacer el paso previo de la compilaciones y posteriormente iniciamos nuestro proyecto con pm2 ademas si quieres ver los logs puedes usar el comando `pm2 logs`\n###  Build\n/root/builderbot-examples-bot\n```\npnpmrunbuild\npm2start./dist/app.js--namebot\npm2logs\n\n```\nCopyCopied!\n### Terminal\n```\n🛜 HTTP Server ON\n[POST]: http://localhost:3008/v1/messages\n[GET]: http://localhost:3008/\n⚡⚡ ACTION REQUIRED ⚡⚡\nYou must scan the QR Code\nRemember that the QR code updates every minute\nNeed help: https://link.codigoencasa.com/DISCORD\n\n```\nCopyCopied!\nAs we can see the project is running and ready to scan the QR code, now only remains to link your whatsapp for that step visit `http://your-ip-public:3008`.\nRemember this QR code is updated every 45 seconds approximately, if you have a linking error refresh the F5 page.\n### Link your whatsapp application\n![](https://i.imgur.com/NqBuP2t.png)\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/deploy/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/deploy/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/deploy/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-bash", "code": "ssh <EMAIL> -pXX\nsudo apt-get update && sudo apt-get upgrade -y\n"}, {"language": "language-bash", "code": "curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash\n"}, {"language": "language-bash", "code": "export NVM_DIR=\"$HOME/.nvm\"\n[ -s \"$NVM_DIR/nvm.sh\" ] && \\. \"$NVM_DIR/nvm.sh\"\n[ -s \"$NVM_DIR/bash_completion\" ] && \\. \"$NVM_DIR/bash_completion\"\n"}, {"language": "language-bash", "code": "nvm install 20\n"}, {"language": "language-bash", "code": "npm i pnpm@latest pm2 --global\n"}, {"language": "language-bash", "code": "git clone https://github.com/codigoencasa/builderbot-examples-bot.git\ncd builderbot-examples-bot\n"}, {"language": "language-bash", "code": "pnpm install\n"}, {"language": "language-bash", "code": "pnpm run build\npm2 start ./dist/app.js --name bot\npm2 logs\n"}, {"language": "text", "code": "🛜  HTTP Server ON\n[POST]: http://localhost:3008/v1/messages\n[GET]: http://localhost:3008/\n\n⚡⚡ ACTION REQUIRED ⚡⚡\nYou must scan the QR Code\nRemember that the QR code updates every minute\nNeed help: https://link.codigoencasa.com/DISCORD\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/showcases/queue-limit", "title": "Queue Limit - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n  * [Contribute](https://www.builderbot.app/en/showcases/</contribute>)\n  * [Course](https://www.builderbot.app/en/showcases/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/showcases/</>)\n  * [Documentation](https://www.builderbot.app/en/showcases/</en/showcases/queue-limit#>)\n  * [Support](https://www.builderbot.app/en/showcases/</en/showcases/queue-limit#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/showcases/</>)\n    * [Quickstart](https://www.builderbot.app/en/showcases/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/showcases/</concepts>)\n    * [Examples](https://www.builderbot.app/en/showcases/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/showcases/</add-functions>)\n    * [Context](https://www.builderbot.app/en/showcases/</context>)\n    * [Methods](https://www.builderbot.app/en/showcases/</methods>)\n    * [Events](https://www.builderbot.app/en/showcases/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/showcases/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/showcases/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/showcases/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/showcases/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/showcases/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/showcases/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/showcases/</deploy>)\n    * [Railway](https://www.builderbot.app/en/showcases/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/showcases/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/showcases/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/showcases/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/showcases/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/showcases/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/showcases/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/showcases/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/showcases/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/showcases/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/showcases/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/showcases/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/showcases/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/showcases/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/showcases/</contribute>)\n    * [Core](https://www.builderbot.app/en/showcases/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/showcases/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/showcases/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/showcases/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/showcases/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/showcases/</en/showcases/queue-limit#>)\n\n\n# Queue Limit\n## [Issue](https://www.builderbot.app/en/showcases/</en/showcases/queue-limit#issue>)\nThe problem was that when more than 60 people started a conversation in less than 20 seconds the bot did not respond to all the people.\n## [Possible Solution](https://www.builderbot.app/en/showcases/</en/showcases/queue-limit#possible-solution>)\nThe framework by default implements a queue manager based on per-user work promises. If you already understand the concept and you are in an environment where you receive a considerable number of recurring messages in a short period of time and you notice that it answers a large number of users but not all of them. You can try extending the process queue configuration.\n**Imagine the case** in which you receive more than 60 messages from more than 60 people in a very short period of time of less than 20 seconds for example\nUser n\nBot\nIt is very likely that it will not respond to all users, since the default configuration of the bot is set to handle a smaller amount of processes, but it is very easy to modify.\n### app.ts\n```\nconstmain=async () => {\nawaitcreateBot({\ndatabase:newMemoryDB(),\nprovider:createProvider(BaileysProvider),\nflow:createFlow([...])\n  }, {\nqueue:{\ntimeout:20000,//👌\nconcurrencyLimit:50//👌\n    }\n  })\n}\n\n```\nCopyCopied!\n  * Name\n    `timeout`\nType\n    number\nDescription\n    \nNumber of milliseconds used as maximum duration for the execution of an asynchronous function. **default 20000**\n  * Name\n    `concurrencyLimit`\nType\n    number\nDescription\n    \nNumber of parallel processes you can be running at the same time. **default 15**\n\n\n## [Guides](https://www.builderbot.app/en/showcases/</en/showcases/queue-limit#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/showcases/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/showcases/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/showcases/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/showcases/</plugins>)\n## [Resources](https://www.builderbot.app/en/showcases/</en/showcases/queue-limit#resources>)\n### [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/showcases/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/showcases/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/showcases/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/showcases/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/showcases/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-bash", "code": "const main = async () => {\n\n    await createBot({\n        database: new MemoryDB(),\n        provider: createProvider(BaileysProvider),\n        flow: createFlow([...])\n    }, {\n        queue: {\n            timeout: 20000, //👌\n            concurrencyLimit: 50 //👌\n        }\n    })\n}\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/showcases/modularize", "title": "Modularize Flows - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n  * [Contribute](https://www.builderbot.app/en/showcases/</contribute>)\n  * [Course](https://www.builderbot.app/en/showcases/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/showcases/</>)\n  * [Documentation](https://www.builderbot.app/en/showcases/</en/showcases/modularize#>)\n  * [Support](https://www.builderbot.app/en/showcases/</en/showcases/modularize#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/showcases/</>)\n    * [Quickstart](https://www.builderbot.app/en/showcases/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/showcases/</concepts>)\n    * [Examples](https://www.builderbot.app/en/showcases/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/showcases/</add-functions>)\n    * [Context](https://www.builderbot.app/en/showcases/</context>)\n    * [Methods](https://www.builderbot.app/en/showcases/</methods>)\n    * [Events](https://www.builderbot.app/en/showcases/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/showcases/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/showcases/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/showcases/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/showcases/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/showcases/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/showcases/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/showcases/</deploy>)\n    * [Railway](https://www.builderbot.app/en/showcases/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/showcases/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/showcases/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/showcases/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/showcases/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/showcases/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/showcases/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/showcases/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/showcases/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/showcases/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/showcases/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/showcases/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/showcases/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/showcases/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/showcases/</contribute>)\n    * [Core](https://www.builderbot.app/en/showcases/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/showcases/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/showcases/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/showcases/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/showcases/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/showcases/</en/showcases/modularize#>)\n\n\n# Modularize Flows\n## [Issue](https://www.builderbot.app/en/showcases/</en/showcases/modularize#issue>)\nMy application grew so large that I was having trouble maintaining it and the code was becoming spaghetti code.\n## [Possible Solution](https://www.builderbot.app/en/showcases/</en/showcases/modularize#possible-solution>)\nAs our projects grow we will need to implement a better way to maintain the project, in this case it is highly recommended to implement a module view.\nIn the following example we will show how we have migrated the modules to a directory, as well as the provider and the database.\napp.tsprovider/index.tsdatabase/index.tsflow/index.tsflow/welcome.flow.tsservices/ai.ts\n```\nimport { createBot } from'@builderbot/bot';\nimport { flow } from\"./flow\";\nimport { database } from\"./database\";\nimport { provider } from\"./provider\";\nimport { ai } from\"./services/ai\";\nconstmain=async () => {\nawaitcreateBot({\n     flow,\n     provider,\n     database,\n   },\n     extensions: {\n     ai // Dependency AI \n   })\nprovider.initHttpServer(3000)\n}\nmain()\n\n```\nCopyCopied!\nAn example of the scaffolding you can use in your project. Or a more user friendly folder structure.\n### structure\n```\nsrc\n├── app.ts\n├── database\n│  └── index.ts\n├── flow\n│  ├── index.ts\n│  └── welcome.flow.ts\n│  └── bye.flow.ts\n│  └── media.flow.ts\n├── provider\n│  └── index.ts\n└── services\n  └── ai.ts\n\n```\nCopyCopied!\n## [Guides](https://www.builderbot.app/en/showcases/</en/showcases/modularize#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/showcases/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/showcases/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/showcases/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/showcases/</plugins>)\n## [Resources](https://www.builderbot.app/en/showcases/</en/showcases/modularize#resources>)\n### [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/showcases/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/showcases/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/showcases/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/showcases/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/showcases/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "  import { createBot } from '@builderbot/bot';\n  import { flow } from \"./flow\";\n  import { database } from \"./database\";\n  import { provider } from \"./provider\";\n  import { ai } from \"./services/ai\";\n\n  const main = async () => {\n  await createBot({\n          flow,\n          provider,\n          database,\n      },\n          extensions: {\n          ai // Dependency AI \n      })\n\n  provider.initHttpServer(3000)\n}\nmain()\n"}, {"language": "text", "code": "src\n├── app.ts\n├── database\n│   └── index.ts\n├── flow\n│   ├── index.ts\n│   └── welcome.flow.ts\n│   └── bye.flow.ts\n│   └── media.flow.ts\n├── provider\n│   └── index.ts\n└── services\n    └── ai.ts\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/showcases/fast-entires", "title": "Fast Entries - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n  * [Contribute](https://www.builderbot.app/en/showcases/</contribute>)\n  * [Course](https://www.builderbot.app/en/showcases/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/showcases/</>)\n  * [Documentation](https://www.builderbot.app/en/showcases/</en/showcases/fast-entires#>)\n  * [Support](https://www.builderbot.app/en/showcases/</en/showcases/fast-entires#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/showcases/</>)\n    * [Quickstart](https://www.builderbot.app/en/showcases/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/showcases/</concepts>)\n    * [Examples](https://www.builderbot.app/en/showcases/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/showcases/</add-functions>)\n    * [Context](https://www.builderbot.app/en/showcases/</context>)\n    * [Methods](https://www.builderbot.app/en/showcases/</methods>)\n    * [Events](https://www.builderbot.app/en/showcases/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/showcases/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/showcases/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/showcases/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/showcases/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/showcases/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/showcases/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/showcases/</deploy>)\n    * [Railway](https://www.builderbot.app/en/showcases/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/showcases/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/showcases/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/showcases/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/showcases/</showcases/fast-entires>)\n      * [Issue](https://www.builderbot.app/en/showcases/</en/showcases/fast-entires#issue>)\n      * [Improved Solution](https://www.builderbot.app/en/showcases/</en/showcases/fast-entires#improved-solution>)\n    * [Idle](https://www.builderbot.app/en/showcases/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/showcases/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/showcases/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/showcases/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/showcases/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/showcases/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/showcases/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/showcases/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/showcases/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/showcases/</contribute>)\n    * [Core](https://www.builderbot.app/en/showcases/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/showcases/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/showcases/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/showcases/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/showcases/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/showcases/</en/showcases/fast-entires#>)\n\n\n# Fast Entries\n## [Issue](https://www.builderbot.app/en/showcases/</en/showcases/fast-entires#issue>)\nSometimes it happens that people are impatient and write independent messages in a very short time gap preventing the bot to answer, which makes that each message is answered but not in the desired order.\n## [Improved Solution](https://www.builderbot.app/en/showcases/</en/showcases/fast-entires#improved-solution>)\nFor this type of environment, we've implemented an enhanced functionality that introduces a margin of 3000ms for the user to write messages. Each time a user writes a message within this 3000ms window, it accumulates all the messages. After the margin time expires, the bot interprets everything as a single conversation.\nm1\nm1, m2, m3\na1, a2, a,3\nm2\nm3\nUser\nQueue 3sec\nBot\nProcess\nAnswers\nUser 1 answer\nThis implementation ensures that before passing to the processing stage, all independent messages (e.g., 3) become one (1) and are processed as a single message.\nIn this example, we use **3000ms** (equal to 3 seconds) as the default gap, but you can modify this to your liking by adjusting the `gapSeconds` in the `QueueConfig`.\n### Video Fast Entries\nfast-entires.tsapp.ts\n```\n/**\n * @file multiUserMessageQueue.ts\n * @description An improved functional implementation of a multi-user message queueing system with debounce functionality,\n * ensuring separate conversation handling for each user.\n */\nimport { BotContext } from\"@builderbot/bot/dist/types\";\ninterfaceMessage {\n  text:string;\n  timestamp:number;\n}\ninterfaceQueueConfig {\n  gapMilliseconds:number;\n}\ninterfaceUserQueue {\n  messages:Message[];\n  timer:NodeJS.Timeout|null;\n  callback: ((body:string, from:string) =>void) |null;\n}\ninterfaceQueueState {\n  queues:Map<string,UserQueue>;\n}\nfunctioncreateInitialState():QueueState {\nreturn {\n    queues:newMap()\n  };\n}\nfunctionresetTimer(userQueue:UserQueue):UserQueue {\nif (userQueue.timer) {\nclearTimeout(userQueue.timer);\n  }\nreturn { ...userQueue, timer:null };\n}\nfunctionprocessQueue(messages:Message[]):string {\nconstresult=messages.map(message =>message.text).join(\" \");\nconsole.log('Accumulated messages:', result);\nreturn result;\n}\nfunctioncreateMessageQueue(config:QueueConfig) {\nlet state:QueueState=createInitialState();\nreturnfunctionenqueueMessage(ctx:BotContext,callback: (body:string, from:string) =>void):void {\nconstfrom=ctx.from;\nconstmessageBody=ctx.body;\nif (!from ||!messageBody) {\nconsole.error('Invalid message context:', ctx);\nreturn;\n    }\nconsole.log('Enqueueing:', messageBody,'from:', from);\nlet userQueue =state.queues.get(from);\nif (!userQueue) {\n      userQueue = { messages: [], timer:null, callback:null };\nstate.queues.set(from, userQueue);\n    }\n    userQueue =resetTimer(userQueue);\nuserQueue.messages.push({ text: messageBody, timestamp:Date.now() });\nuserQueue.callback = callback;\nconsole.log('Messages for', from,':',userQueue.messages);\nif (!userQueue.timer) {\nuserQueue.timer =setTimeout(() => {\nconstcurrentQueue=state.queues.get(from);\nif (currentQueue) {\nconstresult=processQueue(currentQueue.messages);\nif (currentQueue.callback) {\ncurrentQueue.callback(result, from);\n          }\nstate.queues.set(from, { ...currentQueue, messages: [], timer:null });\n        }\n      },config.gapMilliseconds);\n    }\nstate.queues.set(from, userQueue);\n  };\n}\nexport { createMessageQueue, QueueConfig };\n\n```\nCopyCopied!\n### Key Improvements in the New Implementation:\n  1. **Functional Approach** : The new implementation uses a functional programming style, which can lead to more predictable and testable code.\n  2. **Immutable State** : The state of the queue is managed immutably, which helps prevent unexpected side effects.\n  3. **Flexible Configuration** : The `QueueConfig` interface allows for easy adjustment of the gap time.\n  4. **Enhanced Error Handling** : The implementation includes try-catch blocks for better error management.\n  5. **Callback-based Processing** : Instead of returning a promise, the new implementation uses a callback function, allowing for more flexible message processing.\n  6. **Detailed Logging** : Console logs have been added at key points to aid in debugging and understanding the message flow.\n\n\nRemember that while this implementation offers significant improvements, it's always possible to further optimize based on specific use cases and requirements.\n## [Guides](https://www.builderbot.app/en/showcases/</en/showcases/fast-entires#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/showcases/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/showcases/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/showcases/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/showcases/</plugins>)\n## [Resources](https://www.builderbot.app/en/showcases/</en/showcases/fast-entires#resources>)\n### [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/showcases/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/showcases/</events>)\nLearning about events will make us more fluent when creating chatbots.\n## [Guides](https://www.builderbot.app/en/showcases/</en/showcases/fast-entires#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/showcases/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/showcases/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/showcases/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/showcases/</plugins>)\n## [Resources](https://www.builderbot.app/en/showcases/</en/showcases/fast-entires#resources>)\n### [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/showcases/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/showcases/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/showcases/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/showcases/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/showcases/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "/**\n * @file multiUserMessageQueue.ts\n * @description An improved functional implementation of a multi-user message queueing system with debounce functionality,\n * ensuring separate conversation handling for each user.\n */\n\nimport { BotContext } from \"@builderbot/bot/dist/types\";\n\ninterface Message {\n    text: string;\n    timestamp: number;\n}\n\ninterface QueueConfig {\n    gapMilliseconds: number;\n}\n\ninterface UserQueue {\n    messages: Message[];\n    timer: NodeJS.Timeout | null;\n    callback: ((body: string, from: string) => void) | null;\n}\n\ninterface QueueState {\n    queues: Map<string, UserQueue>;\n}\n\nfunction createInitialState(): QueueState {\n    return {\n        queues: new Map()\n    };\n}\n\nfunction resetTimer(userQueue: UserQueue): UserQueue {\n    if (userQueue.timer) {\n        clearTimeout(userQueue.timer);\n    }\n    return { ...userQueue, timer: null };\n}\n\nfunction processQueue(messages: Message[]): string {\n    const result = messages.map(message => message.text).join(\" \");\n    console.log('Accumulated messages:', result);\n    return result;\n}\n\nfunction createMessageQueue(config: QueueConfig) {\n    let state: QueueState = createInitialState();\n\n    return function enqueueMessage(ctx: BotContext, callback: (body: string, from: string) => void): void {\n        const from = ctx.from;\n        const messageBody = ctx.body;\n\n        if (!from || !messageBody) {\n            console.error('Invalid message context:', ctx);\n            return;\n        }\n\n        console.log('Enqueueing:', messageBody, 'from:', from);\n\n        let userQueue = state.queues.get(from);\n        if (!userQueue) {\n            userQueue = { messages: [], timer: null, callback: null };\n            state.queues.set(from, userQueue);\n        }\n\n        userQueue = resetTimer(userQueue);\n        userQueue.messages.push({ text: messageBody, timestamp: Date.now() });\n        userQueue.callback = callback;\n\n        console.log('Messages for', from, ':', userQueue.messages);\n\n        if (!userQueue.timer) {\n            userQueue.timer = setTimeout(() => {\n                const currentQueue = state.queues.get(from);\n                if (currentQueue) {\n                    const result = processQueue(currentQueue.messages);\n                    if (currentQueue.callback) {\n                        currentQueue.callback(result, from);\n                    }\n                    state.queues.set(from, { ...currentQueue, messages: [], timer: null });\n                }\n            }, config.gapMilliseconds);\n        }\n\n        state.queues.set(from, userQueue);\n    };\n}\n\nexport { createMessageQueue, QueueConfig };\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/showcases/idle-optional", "title": "Idle - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n  * [Contribute](https://www.builderbot.app/en/showcases/</contribute>)\n  * [Course](https://www.builderbot.app/en/showcases/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/showcases/</>)\n  * [Documentation](https://www.builderbot.app/en/showcases/</en/showcases/idle-optional#>)\n  * [Support](https://www.builderbot.app/en/showcases/</en/showcases/idle-optional#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/showcases/</>)\n    * [Quickstart](https://www.builderbot.app/en/showcases/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/showcases/</concepts>)\n    * [Examples](https://www.builderbot.app/en/showcases/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/showcases/</add-functions>)\n    * [Context](https://www.builderbot.app/en/showcases/</context>)\n    * [Methods](https://www.builderbot.app/en/showcases/</methods>)\n    * [Events](https://www.builderbot.app/en/showcases/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/showcases/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/showcases/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/showcases/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/showcases/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/showcases/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/showcases/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/showcases/</deploy>)\n    * [Railway](https://www.builderbot.app/en/showcases/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/showcases/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/showcases/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/showcases/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/showcases/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/showcases/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/showcases/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/showcases/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/showcases/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/showcases/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/showcases/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/showcases/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/showcases/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/showcases/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/showcases/</contribute>)\n    * [Core](https://www.builderbot.app/en/showcases/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/showcases/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/showcases/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/showcases/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/showcases/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/showcases/</en/showcases/idle-optional#>)\n\n\n# Idle\n## [Issue](https://www.builderbot.app/en/showcases/</en/showcases/idle-optional#issue>)\nI need to wait for the customer's response but only for a limited time.\n## [Possible Solution](https://www.builderbot.app/en/showcases/</en/showcases/idle-optional#possible-solution>)\nThe inactivity of flows in our business logic can become a nuisance, that is why we share the following resources as an alternative solution to this situation.\nCreate a file named `idle-custom.ts` and paste the following code:\n### idle-custom.ts\n```\nimport { EVENTS, addKeyword } from'@builderbot/bot'\nimport { BotContext, TFlow } from'@builderbot/bot/dist/types';\n// Object to store timers for each user\nconsttimers= {};\n// Flow for handling inactivity\nconstidleFlow=addKeyword(EVENTS.ACTION).addAction(\nasync (_, { endFlow }) => {\nreturnendFlow(\"Response time has expired\");\n  }\n);\n// Function to start the inactivity timer for a user\nconststart= (ctx:BotContext,gotoFlow: (a:TFlow) =>Promise<void>, ms:number) => {\n  timers[ctx.from] =setTimeout(() => {\nconsole.log(`User timeout: ${ctx.from}`);\nreturngotoFlow(idleFlow);\n  }, ms);\n}\n// Function to reset the inactivity timer for a user\nconstreset= (ctx:BotContext,gotoFlow: (a:TFlow) =>Promise<void>, ms:number) => {\nstop(ctx);\nif (timers[ctx.from]) {\nconsole.log(`reset countdown for the user: ${ctx.from}`);\nclearTimeout(timers[ctx.from]);\n  }\nstart(ctx, gotoFlow, ms);\n}\n// Function to stop the inactivity timer for a user\nconststop= (ctx:BotContext) => {\nif (timers[ctx.from]) {\nclearTimeout(timers[ctx.from]);\n  }\n}\nexport {\n  start,\n  reset,\n  stop,\n  idleFlow,\n}\n\n```\nCopyCopied!\n**Remember** to add this flow to your project's main flow array\n### app.ts\n```\nimport { idleFlow } from'./idle-custom'\nconstmain=async () => {\nconstadapterFlow=createFlow([welcomeFlow, registerFlow, idleFlow])\nconstadapterProvider=createProvider(Provider)\nconstadapterDB=newDatabase()\nconst { httpServer } =awaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\nhttpServer(+PORT)\n}\n\n```\nCopyCopied!\n## [Start Inactivity](https://www.builderbot.app/en/showcases/</en/showcases/idle-optional#start-inactivity>)\n  * Name\n    `ctx`\nType\n    BotContext\nDescription\n    \nCurrent execution context\n  * Name\n    `gotoFlow`\nType\n    TFlow\nDescription\n    \nFunction providing the execution flow\n  * Name\n    `ms`\nType\n    number\nDescription\n    \nNumber of milliseconds to be set\n\n\n```\nconstquestionFlow=addKeyword(\"hello\")\n.addAction(async (ctx, { gotoFlow }) => {\nstart(ctx, gotoFlow,10000)\n  })\n\n```\nCopyCopied!\n## [Reset Inactivity](https://www.builderbot.app/en/showcases/</en/showcases/idle-optional#reset-inactivity>)\n  * Name\n    `ctx`\nType\n    BotContext\nDescription\n    \nCurrent execution context\n  * Name\n    `gotoFlow`\nType\n    TFlow\nDescription\n    \nFunction providing the execution flow\n  * Name\n    `ms`\nType\n    number\nDescription\n    \nNumber of milliseconds to be set\n\n\n```\n// ...\n.addAnswer(\n\"Give me your last name\",\n  { capture:true },\nasync (ctx, { gotoFlow, state }) => {\nreset(ctx, gotoFlow,10000);\nawaitstate.update({ lastName:ctx.body });\n  }\n)\n// ...\n\n```\nCopyCopied!\n## [Stop Inactivity](https://www.builderbot.app/en/showcases/</en/showcases/idle-optional#stop-inactivity>)\n  * Name\n    `ctx`\nType\n    BotContext\nDescription\n    \nCurrent execution context\n\n\n```\n// ...\n.addAnswer(\n\"Thank you!\",\nnull,\nasync (ctx, { gotoFlow, state }) => {\nstop(ctx);\n  }\n)\n// ...\n\n```\nCopyCopied!\n### app.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword } from'@bot-whatsapp/bot'\nimport { MemoryDB as Database } from'@bot-whatsapp/bot'\nimport { BaileysProvider as Provider } from'@bot-whatsapp/provider-baileys'\nimport { idleFlow, reset, start, stop, } from'./idle-custom'\nconstPORT=process.env.PORT??3008\nconstquestionFlow=addKeyword(\"hello\")\n.addAction(async (ctx, { gotoFlow }) =>start(ctx, gotoFlow,10000))\n.addAnswer(\n    [\n\"This is a test of the Home idle, if you do not respond within 10 seconds I will end the flow.\",\n\"Give me your name\",\n    ],\n    { capture:true },\nasync (ctx, { gotoFlow, state }) => {\nreset(ctx, gotoFlow,10000);\nawaitstate.update({ name:ctx.body });\n    }\n  )\n.addAnswer(\n\"Give me your last name\",\n    { capture:true },\nasync (ctx, { gotoFlow, state }) => {\nreset(ctx, gotoFlow,10000);\nawaitstate.update({ lastName:ctx.body });\n    }\n  )\n.addAnswer(\"Finally, answer this simple question by typing the number between [1, 2].\",\n    { capture:true },\nasync (ctx, { gotoFlow, endFlow, fallBack }) => {\nreset(ctx, gotoFlow,10000);\nswitch (ctx.body) {\ncase\"1\":\nstop(ctx);\nreturnendFlow(`Nice 1`);\ncase\"2\":\nstop(ctx);\nreturnendFlow(`Ok 2`);\ndefault:\nreturnfallBack(`I only accept *numbers* that are between [1, 2].`);\n      }\n    }\n  );\n\nconstmain=async () => {\nconstadapterFlow=createFlow([questionFlow, idleFlow])\nconstadapterProvider=createProvider(Provider)\nconstadapterDB=newDatabase()\nconst { httpServer } =awaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\nhttpServer(+PORT)\n}\nmain()\n\n```\nCopyCopied!\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/showcases/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/showcases/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/showcases/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "import { EVENTS, addKeyword } from '@builderbot/bot'\nimport { BotContext, TFlow } from '@builderbot/bot/dist/types';\n\n// Object to store timers for each user\nconst timers = {};\n\n// Flow for handling inactivity\nconst idleFlow = addKeyword(EVENTS.ACTION).addAction(\n    async (_, { endFlow }) => {\n        return endFlow(\"Response time has expired\");\n    }\n);\n\n// Function to start the inactivity timer for a user\nconst start = (ctx: BotContext, gotoFlow: (a: TFlow) => Promise<void>, ms: number) => {\n    timers[ctx.from] = setTimeout(() => {\n        console.log(`User timeout: ${ctx.from}`);\n        return gotoFlow(idleFlow);\n    }, ms);\n}\n\n// Function to reset the inactivity timer for a user\nconst reset = (ctx: BotContext, gotoFlow: (a: TFlow) => Promise<void>, ms: number) => {\n    stop(ctx);\n    if (timers[ctx.from]) {\n        console.log(`reset countdown for the user: ${ctx.from}`);\n        clearTimeout(timers[ctx.from]);\n    }\n    start(ctx, gotoFlow, ms);\n}\n\n// Function to stop the inactivity timer for a user\nconst stop = (ctx: BotContext) => {\n    if (timers[ctx.from]) {\n        clearTimeout(timers[ctx.from]);\n    }\n}\n\nexport {\n    start,\n    reset,\n    stop,\n    idleFlow,\n}\n"}, {"language": "language-ts", "code": "import { idleFlow } from './idle-custom'\n\nconst main = async () => {\n\n    const adapterFlow = createFlow([welcomeFlow, registerFlow, idleFlow])\n\n    const adapterProvider = createProvider(Provider)\n    const adapterDB = new Database()\n\n    const { httpServer } = await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n\n    httpServer(+PORT)\n\n}\n"}, {"language": "language-ts", "code": "const questionFlow = addKeyword(\"hello\")\n    .addAction(async (ctx, { gotoFlow }) => {\n        start(ctx, gotoFlow, 10000)\n    })\n"}, {"language": "language-ts", "code": "// ...\n.addAnswer(\n    \"Give me your last name\",\n    { capture: true },\n    async (ctx, { gotoFlow, state }) => {\n        reset(ctx, gotoFlow, 10000);\n        await state.update({ lastName: ctx.body });\n    }\n)\n// ...\n"}, {"language": "language-ts", "code": "// ...\n.addAnswer(\n    \"Thank you!\",\n    null,\n    async (ctx, { gotoFlow, state }) => {\n        stop(ctx);\n    }\n)\n// ...\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, createFlow, addKeyword } from '@bot-whatsapp/bot'\nimport { MemoryDB as Database } from '@bot-whatsapp/bot'\nimport { <PERSON>s<PERSON>rovider as Provider } from '@bot-whatsapp/provider-baileys'\nimport { idleFlow, reset, start, stop, } from './idle-custom'\n\nconst PORT = process.env.PORT ?? 3008\n\nconst questionFlow = addKeyword(\"hello\")\n    .addAction(async (ctx, { gotoFlow }) => start(ctx, gotoFlow, 10000))\n    .addAnswer(\n        [\n            \"This is a test of the Home idle, if you do not respond within 10 seconds I will end the flow.\",\n            \"Give me your name\",\n        ],\n        { capture: true },\n        async (ctx, { gotoFlow, state }) => {\n            reset(ctx, gotoFlow, 10000);\n            await state.update({ name: ctx.body });\n        }\n    )\n    .addAnswer(\n        \"Give me your last name\",\n        { capture: true },\n        async (ctx, { gotoFlow, state }) => {\n            reset(ctx, gotoFlow, 10000);\n            await state.update({ lastName: ctx.body });\n        }\n    )\n    .addAnswer(\"Finally, answer this simple question by typing the number between [1, 2].\",\n        { capture: true },\n        async (ctx, { gotoFlow, endFlow, fallBack }) => {\n            reset(ctx, gotoFlow, 10000);\n            switch (ctx.body) {\n                case \"1\":\n                    stop(ctx);\n                    return endFlow(`Nice 1`);\n                case \"2\":\n                    stop(ctx);\n                    return endFlow(`Ok 2`);\n                default:\n                    return fallBack(`I only accept *numbers* that are between [1, 2].`);\n            }\n        }\n    );\n\n\nconst main = async () => {\n    const adapterFlow = createFlow([questionFlow, idleFlow])\n    const adapterProvider = createProvider(Provider)\n    const adapterDB = new Database()\n\n    const { httpServer } = await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n\n    httpServer(+PORT)\n}\n\nmain()\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/showcases/docker-pm2", "title": "Scheduled Reboots - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n  * [Contribute](https://www.builderbot.app/en/showcases/</contribute>)\n  * [Course](https://www.builderbot.app/en/showcases/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/showcases/</>)\n  * [Documentation](https://www.builderbot.app/en/showcases/</en/showcases/docker-pm2#>)\n  * [Support](https://www.builderbot.app/en/showcases/</en/showcases/docker-pm2#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/showcases/</>)\n    * [Quickstart](https://www.builderbot.app/en/showcases/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/showcases/</concepts>)\n    * [Examples](https://www.builderbot.app/en/showcases/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/showcases/</add-functions>)\n    * [Context](https://www.builderbot.app/en/showcases/</context>)\n    * [Methods](https://www.builderbot.app/en/showcases/</methods>)\n    * [Events](https://www.builderbot.app/en/showcases/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/showcases/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/showcases/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/showcases/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/showcases/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/showcases/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/showcases/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/showcases/</deploy>)\n    * [Railway](https://www.builderbot.app/en/showcases/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/showcases/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/showcases/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/showcases/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/showcases/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/showcases/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/showcases/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/showcases/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/showcases/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/showcases/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/showcases/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/showcases/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/showcases/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/showcases/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/showcases/</contribute>)\n    * [Core](https://www.builderbot.app/en/showcases/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/showcases/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/showcases/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/showcases/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/showcases/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/showcases/</en/showcases/docker-pm2#>)\n\n\n# Scheduled Reboots\n## [Issue](https://www.builderbot.app/en/showcases/</en/showcases/docker-pm2#issue>)\nOccasionally, the bot's programming seems to be unresponsive or the server does not have enough resources.\n## [Possible Solution](https://www.builderbot.app/en/showcases/</en/showcases/docker-pm2#possible-solution>)\nWhen using the free providers we will need to restart the bot to avoid accumulating temporary data that may affect its performance. In that case an easy solution to implement is to schedule restarts, these restarts will not need to scan the QR again.\nDocker and Pm2 One way to do it if you are using docker could be to implement in your docker image Pm2 and with pm2-runtime start your container with a cron pattern.\nIn the following Dockerfile we can see how we start the project with a pattern `0 */12 * * *` that means that it will restart every 12 hours\n### Dockerfile\n```\n# Use the official Node.js image as the base image for building the application.\nFROM node:21-alpine3.18 as builder\n# Enable Corepack and prepare for PNPM installation\nRUN corepack enable && corepack prepare pnpm@latest --activate\nENV PNPM_HOME=/usr/local/bin\n# Set the working directory inside the container\nWORKDIR /app\n# Copy package.json and pnpm-lock.yaml files to the working directory\nCOPY package*.json pnpm-lock.yaml ./\n# Install git for potential dependencies\nRUN apk add --no-cache git\n# Install PM2 globally using PNPM\nRUN pnpm install pm2 -g\n# Copy the application source code into the container\nCOPY . .\n# Install dependencies using PNPM\nRUN pnpm install\n# Create a new stage for deployment\nFROM builder as deploy\n# Copy only necessary files and directories for deployment\nCOPY --from=builder /app/src ./src\nCOPY --from=builder /app/package.json /app/pnpm-lock.yaml ./\n# Install production dependencies using frozen lock file\nRUN pnpm install --frozen-lockfile --production\n# Define the command to start the application using PM2 runtime\nCMD [\"pm2-runtime\", \"start\", \"./src/app.js\", \"--cron\", \"0 */12 * * *\"]\n\n```\nCopyCopied!\nRemember that this is an alternative solution, and it is possible that its implementation could be improved.\n## [Guides](https://www.builderbot.app/en/showcases/</en/showcases/docker-pm2#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/showcases/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/showcases/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/showcases/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/showcases/</plugins>)\n## [Resources](https://www.builderbot.app/en/showcases/</en/showcases/docker-pm2#resources>)\n### [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/showcases/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/showcases/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/showcases/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/showcases/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/showcases/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-docker", "code": "# Use the official Node.js image as the base image for building the application.\nFROM node:21-alpine3.18 as builder\n\n# Enable Corepack and prepare for PNPM installation\nRUN corepack enable && corepack prepare pnpm@latest --activate\nENV PNPM_HOME=/usr/local/bin\n\n# Set the working directory inside the container\nWORKDIR /app\n\n# Copy package.json and pnpm-lock.yaml files to the working directory\nCOPY package*.json pnpm-lock.yaml ./\n\n# Install git for potential dependencies\nRUN apk add --no-cache git\n\n# Install PM2 globally using PNPM\nRUN pnpm install pm2 -g\n\n# Copy the application source code into the container\nCOPY . .\n\n# Install dependencies using PNPM\nRUN pnpm install\n\n# Create a new stage for deployment\nFROM builder as deploy\n\n# Copy only necessary files and directories for deployment\nCOPY --from=builder /app/src ./src\nCOPY --from=builder /app/package.json /app/pnpm-lock.yaml ./\n\n# Install production dependencies using frozen lock file\nRUN pnpm install --frozen-lockfile --production\n\n# Define the command to start the application using PM2 runtime\nCMD [\"pm2-runtime\", \"start\", \"./src/app.js\", \"--cron\", \"0 */12 * * *\"]\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/showcases/event-in-out-messages", "title": "Incoming and outgoing messages - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n  * [Contribute](https://www.builderbot.app/en/showcases/</contribute>)\n  * [Course](https://www.builderbot.app/en/showcases/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/showcases/</>)\n  * [Documentation](https://www.builderbot.app/en/showcases/</en/showcases/event-in-out-messages#>)\n  * [Support](https://www.builderbot.app/en/showcases/</en/showcases/event-in-out-messages#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/showcases/</>)\n    * [Quickstart](https://www.builderbot.app/en/showcases/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/showcases/</concepts>)\n    * [Examples](https://www.builderbot.app/en/showcases/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/showcases/</add-functions>)\n    * [Context](https://www.builderbot.app/en/showcases/</context>)\n    * [Methods](https://www.builderbot.app/en/showcases/</methods>)\n    * [Events](https://www.builderbot.app/en/showcases/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/showcases/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/showcases/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/showcases/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/showcases/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/showcases/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/showcases/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/showcases/</deploy>)\n    * [Railway](https://www.builderbot.app/en/showcases/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/showcases/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/showcases/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/showcases/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/showcases/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/showcases/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/showcases/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/showcases/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/showcases/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/showcases/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/showcases/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/showcases/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/showcases/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/showcases/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/showcases/</contribute>)\n    * [Core](https://www.builderbot.app/en/showcases/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/showcases/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/showcases/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/showcases/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/showcases/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/showcases/</en/showcases/event-in-out-messages#>)\n\n\n# Incoming and outgoing messages\n## [Issue](https://www.builderbot.app/en/showcases/</en/showcases/event-in-out-messages#issue>)\nSometimes we will need to listen to the events of incoming or outgoing messages, this is often necessary when we are connecting our chatbot with third party UI applications.\n## [Possible Solution](https://www.builderbot.app/en/showcases/</en/showcases/event-in-out-messages#possible-solution>)\nIn the example below we show a code which you can execute and observe the messages in the console. The objective is to capture the incoming and outgoing message from the bot.\n### app.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword } from'@builderbot/bot'\nimport { MemoryDB as Database } from'@builderbot/bot'\nimport { BaileysProvider as Provider } from'@builderbot/provider-baileys'\nconstPORT=process.env.PORT??3008\nconstwelcomeFlow=addKeyword('hello!').addAnswer('Welcome!')\nconstmain=async () => {\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(Provider)\nconstadapterDB=newDatabase()\nconstbot=awaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\nbot.httpServer(+PORT)\nadapterProvider.on('message', ({ body, from }) => {\nconsole.log(`Message Payload:`, { body, from })\n  })\nbot.on('send_message', ({ answer, from }) => {\nconsole.log(`Send Message Payload:`, { answer, from })\n  })\n}\nmain()\n\n```\nCopyCopied!\n## [Guides](https://www.builderbot.app/en/showcases/</en/showcases/event-in-out-messages#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/showcases/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/showcases/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/showcases/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/showcases/</plugins>)\n## [Resources](https://www.builderbot.app/en/showcases/</en/showcases/event-in-out-messages#resources>)\n### [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/showcases/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/showcases/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/showcases/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/showcases/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/showcases/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "import { createBot, createProvider, createFlow, addKeyword } from '@builderbot/bot'\nimport { MemoryDB as Database } from '@builderbot/bot'\nimport { BaileysProvider as Provider } from '@builderbot/provider-baileys'\n\nconst PORT = process.env.PORT ?? 3008\n\nconst welcomeFlow = addKeyword('hello!').addAnswer('Welcome!')\n\nconst main = async () => {\n    const adapterFlow = createFlow([welcomeFlow])\n\n    const adapterProvider = createProvider(Provider)\n    const adapterDB = new Database()\n\n    const bot = await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n\n    bot.httpServer(+PORT)\n\n    adapterProvider.on('message', ({ body, from }) => {\n        console.log(`Message Payload:`, { body, from })\n    })\n\n    bot.on('send_message', ({ answer, from }) => {\n        console.log(`Send Message Payload:`, { answer, from })\n    })\n}\n\nmain()\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/showcases/cron-reminder", "title": "Reminder (cron) - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n  * [Contribute](https://www.builderbot.app/en/showcases/</contribute>)\n  * [Course](https://www.builderbot.app/en/showcases/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/showcases/</>)\n  * [Documentation](https://www.builderbot.app/en/showcases/</en/showcases/cron-reminder#>)\n  * [Support](https://www.builderbot.app/en/showcases/</en/showcases/cron-reminder#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/showcases/</>)\n    * [Quickstart](https://www.builderbot.app/en/showcases/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/showcases/</concepts>)\n    * [Examples](https://www.builderbot.app/en/showcases/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/showcases/</add-functions>)\n    * [Context](https://www.builderbot.app/en/showcases/</context>)\n    * [Methods](https://www.builderbot.app/en/showcases/</methods>)\n    * [Events](https://www.builderbot.app/en/showcases/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/showcases/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/showcases/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/showcases/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/showcases/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/showcases/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/showcases/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/showcases/</deploy>)\n    * [Railway](https://www.builderbot.app/en/showcases/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/showcases/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/showcases/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/showcases/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/showcases/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/showcases/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/showcases/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/showcases/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/showcases/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/showcases/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/showcases/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/showcases/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/showcases/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/showcases/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/showcases/</contribute>)\n    * [Core](https://www.builderbot.app/en/showcases/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/showcases/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/showcases/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/showcases/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/showcases/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/showcases/</en/showcases/cron-reminder#>)\n\n\n# Reminder (cron)\n## [Issue](https://www.builderbot.app/en/showcases/</en/showcases/cron-reminder#issue>)\nI need to send individual reminders every so often to users, reminding them to perform a task or complete a purchase.\n## [Possible Solution](https://www.builderbot.app/en/showcases/</en/showcases/cron-reminder#possible-solution>)\nFor this we will need to install some dependencies that will facilitate the work of the scheduled tasks, we will use [node-cron](https://www.builderbot.app/en/showcases/<https:/www.npmjs.com/package/node-cron>) below you will find the instructions on how to install install\n```\npnpminode-cron\npnpmi@types/node-cron-D\n\n```\nCopyCopied!\nWe have seen how we declare a logic that runs every 12 hours `0 */12 * * *` [cron guru](https://www.builderbot.app/en/showcases/<https:/crontab.guru/every-12-hours>) where it makes a request to an api that can also be a query to the database to retrieve which are those user numbers to which we want to send a message.\nIt is recommended that you have a varied list of welcome messages to avoid possible bans.\n### app.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword, utils } from'@builderbot/bot'\nimport { schedule } from'node-cron'\nimport { MemoryDB as Database } from'@builderbot/bot'\nimport { BaileysProvider as Provider } from'@builderbot/provider-baileys'\nconstPORT=process.env.PORT??3008\nconstwelcomeFlow=addKeyword<Provider,Database>(['hi','hello','hola'])\n.addAnswer(`🙌 Hello welcome to this *Chatbot*`)\nconstmain=async () => {\nconstadapterFlow=createFlow([welcomeFlow])\nconstadapterProvider=createProvider(Provider)\nconstadapterDB=newDatabase()\nconst { httpServer } =awaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\nhttpServer(+PORT)\n/**\n   * It is recommended to have an array of messages with which you will \n   * greet or make that first contact with the user to\n   * avoid robotic behavior and possible bans\n   */\nschedule('0 */12 * * *',async () => {\nconsole.log('running a task twelve hours');\nconstusersApi=awaitfetch('http://your.api/users')\nconstusers=awaitusersApi.json()\nconstlistMessages= [`Your message!`,'Ey how are you?','How do you doing?']\nfor (constuserof users) {\nconstrandomMessage= listMessages[Math.floor(Math.random() *listMessages.length)]\nawaitadapterProvider.sendMessage(user.number, randomMessage, {})\nawaitutils.delay(5000)\n    }\n  });\n}\nmain()\n\n```\nCopyCopied!\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/showcases/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/showcases/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/showcases/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-bash", "code": "pnpm i node-cron\npnpm i @types/node-cron -D\n"}, {"language": "language-ts", "code": "import { createBot, createProvider, createFlow, addKeyword, utils } from '@builderbot/bot'\nimport { schedule } from 'node-cron'\nimport { MemoryDB as Database } from '@builderbot/bot'\nimport { BaileysProvider as Provider } from '@builderbot/provider-baileys'\n\nconst PORT = process.env.PORT ?? 3008\n\nconst welcomeFlow = addKeyword<Provider, Database>(['hi', 'hello', 'hola'])\n    .addAnswer(`🙌 Hello welcome to this *Chatbot*`)\n  \nconst main = async () => {\n    const adapterFlow = createFlow([welcomeFlow])\n\n    const adapterProvider = createProvider(Provider)\n    const adapterDB = new Database()\n\n    const { httpServer } = await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n\n    httpServer(+PORT)\n\n    /**\n     * It is recommended to have an array of messages with which you will \n     * greet or make that first contact with the user to\n     * avoid robotic behavior and possible bans\n     */\n\n    schedule('0 */12 * * *', async () => {\n        console.log('running a task twelve hours');\n        const usersApi = await fetch('http://your.api/users')\n        const users = await usersApi.json()\n        const listMessages = [`Your message!`, 'Ey how are you?', 'How do you doing?']\n\n        for (const user of users) {\n            const randomMessage = listMessages[Math.floor(Math.random() * listMessages.length)]\n            await adapterProvider.sendMessage(user.number, randomMessage, {})\n            await utils.delay(5000)\n        }\n    });\n}\n\nmain()\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/showcases/forward-conversation-to-human", "title": "Forward conversation to human - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n  * [Contribute](https://www.builderbot.app/en/showcases/</contribute>)\n  * [Course](https://www.builderbot.app/en/showcases/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/showcases/</>)\n  * [Documentation](https://www.builderbot.app/en/showcases/</en/showcases/forward-conversation-to-human#>)\n  * [Support](https://www.builderbot.app/en/showcases/</en/showcases/forward-conversation-to-human#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/showcases/</>)\n    * [Quickstart](https://www.builderbot.app/en/showcases/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/showcases/</concepts>)\n    * [Examples](https://www.builderbot.app/en/showcases/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/showcases/</add-functions>)\n    * [Context](https://www.builderbot.app/en/showcases/</context>)\n    * [Methods](https://www.builderbot.app/en/showcases/</methods>)\n    * [Events](https://www.builderbot.app/en/showcases/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/showcases/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/showcases/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/showcases/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/showcases/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/showcases/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/showcases/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/showcases/</deploy>)\n    * [Railway](https://www.builderbot.app/en/showcases/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/showcases/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/showcases/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/showcases/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/showcases/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/showcases/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/showcases/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/showcases/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/showcases/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/showcases/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/showcases/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/showcases/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/showcases/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/showcases/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/showcases/</contribute>)\n    * [Core](https://www.builderbot.app/en/showcases/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/showcases/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/showcases/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/showcases/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/showcases/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/showcases/</en/showcases/forward-conversation-to-human#>)\n\n\n# Forward conversation to human\n## [Issue](https://www.builderbot.app/en/showcases/</en/showcases/forward-conversation-to-human#issue>)\nI need to be able to stop the bot to continue the conversation with one person but still allow other people to see it.\n## [Possible Solution](https://www.builderbot.app/en/showcases/</en/showcases/forward-conversation-to-human#possible-solution>)\nIn this example let's imagine that the bot number is `ADMIN_NUMBER` the idea is that if a user writes me the bot will answer this is correct, but I want to pause the bot for that user, what I do is write to my own number from the whatsapp with a phrase `Mute +34000000` assuming that the number of the user I want to pause is `+34000000` and the logic I perform is to clean characters to only get `34000000` and check if it exists in a **blackList** if it is not found I add it in this way the bot stops responding to that user and I can continue talking to the person and when I want to reactivate the bot for this person I write again `Mute +34000000` and if it is found it removes it from the blacklist is basically a switch.\napp.tsutil.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword, utils } from'@builderbot/bot'\nimport { MemoryDB as Database } from'@builderbot/bot'\nimport { BaileysProvider as Provider } from'@builderbot/provider-baileys'\nimport { numberClean } from'./utils'\nconstPORT=process.env.PORT??3008\nconstADMIN_NUMBER=process.env.ADMIN_NUMBER\nconstblackListFlow=addKeyword<Provider,Database>('mute')\n.addAction(async (ctx, { blacklist, flowDynamic }) => {\nif (ctx.from ===ADMIN_NUMBER) {\nconsttoMute=numberClean(ctx.body) //Mute +34000000 message incoming\nconstcheck=blacklist.checkIf(toMute)\nif (!check) {\nblacklist.add(toMute)\nawaitflowDynamic(`❌ ${toMute} muted`)\nreturn\n      }\nblacklist.remove(toMute)\nawaitflowDynamic(`🆗 ${toMute} unmuted`)\nreturn\n    }\n})\nconstfullSamplesFlow=addKeyword<Provider,Database>(['samples',utils.setEvent('SAMPLES')])\n.addAnswer(`💪 I'll send you a lot files...`)\nconstmain=async () => {\nconstadapterFlow=createFlow([fullSamplesFlow, blackListFlow])\nconstadapterProvider=createProvider(Provider)\nconstadapterDB=newDatabase()\nconst { httpServer } =awaitcreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\nhttpServer(+PORT)\n}\nmain()\n\n```\nCopyCopied!\n## [Guides](https://www.builderbot.app/en/showcases/</en/showcases/forward-conversation-to-human#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/showcases/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/showcases/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/showcases/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/showcases/</plugins>)\n## [Resources](https://www.builderbot.app/en/showcases/</en/showcases/forward-conversation-to-human#resources>)\n### [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/showcases/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/showcases/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/showcases/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/showcases/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/showcases/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "import { createBot, createProvider, createFlow, addKeyword, utils } from '@builderbot/bot'\nimport { MemoryDB as Database } from '@builderbot/bot'\nimport { BaileysProvider as Provider } from '@builderbot/provider-baileys'\nimport { numberClean } from './utils'\n\nconst PORT = process.env.PORT ?? 3008\nconst ADMIN_NUMBER = process.env.ADMIN_NUMBER \n\nconst blackListFlow = addKeyword<Provider, Database>('mute')\n    .addAction(async (ctx, { blacklist, flowDynamic }) => {\n        if (ctx.from === ADMIN_NUMBER) {\n            const toMute = numberClean(ctx.body) //Mute +34000000 message incoming\n            const check = blacklist.checkIf(toMute)\n            if (!check) {\n                blacklist.add(toMute)\n                await flowDynamic(`❌ ${toMute} muted`)\n                return\n            }\n            blacklist.remove(toMute)\n            await flowDynamic(`🆗 ${toMute} unmuted`)\n            return\n        }\n})\n\nconst fullSamplesFlow = addKeyword<Provider, Database>(['samples', utils.setEvent('SAMPLES')])\n    .addAnswer(`💪 I'll send you a lot files...`)\n\nconst main = async () => {\n    const adapterFlow = createFlow([fullSamplesFlow, blackListFlow])\n\n    const adapterProvider = createProvider(Provider)\n    const adapterDB = new Database()\n\n    const { httpServer } = await createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n\n    httpServer(+PORT)\n}\n\nmain()\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/showcases/gotoflow-use", "title": "How to Use GotoFlow? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n  * [Contribute](https://www.builderbot.app/en/showcases/</contribute>)\n  * [Course](https://www.builderbot.app/en/showcases/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/showcases/</>)\n  * [Documentation](https://www.builderbot.app/en/showcases/</en/showcases/gotoflow-use#>)\n  * [Support](https://www.builderbot.app/en/showcases/</en/showcases/gotoflow-use#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/showcases/</>)\n    * [Quickstart](https://www.builderbot.app/en/showcases/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/showcases/</concepts>)\n    * [Examples](https://www.builderbot.app/en/showcases/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/showcases/</add-functions>)\n    * [Context](https://www.builderbot.app/en/showcases/</context>)\n    * [Methods](https://www.builderbot.app/en/showcases/</methods>)\n    * [Events](https://www.builderbot.app/en/showcases/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/showcases/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/showcases/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/showcases/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/showcases/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/showcases/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/showcases/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/showcases/</deploy>)\n    * [Railway](https://www.builderbot.app/en/showcases/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/showcases/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/showcases/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/showcases/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/showcases/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/showcases/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/showcases/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/showcases/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/showcases/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/showcases/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/showcases/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/showcases/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/showcases/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/showcases/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/showcases/</contribute>)\n    * [Core](https://www.builderbot.app/en/showcases/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/showcases/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/showcases/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/showcases/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/showcases/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/showcases/</en/showcases/gotoflow-use#>)\n\n\n# How to Use GotoFlow?\n## [Issue](https://www.builderbot.app/en/showcases/</en/showcases/gotoflow-use#issue>)\nI needed to be able to detect users that were already registered in my system so I need to make a get http query to my external system to check if that number was registered or not and depending on that divert it to a convenient flow.\n## [Possible Solution](https://www.builderbot.app/en/showcases/</en/showcases/gotoflow-use#possible-solution>)\nA possible solution is to make use of the gotoFlow to be able to divert the logic depending on the response of the http request to be able to verify if the user is registered in the database or is a new user. registered in the database or is a new user.\napp.tsunregisteredUsersFlow.tsregisteredUsersFlow.ts\n```\nimport { createBot, createProvider, createFlow, addKeyword, MemoryDB, EVENTS } from\"@builderbot/bot\";\nimport { BaileysProvider } from\"@builderbot/provider-baileys\";\nimport registeredUsersFlow from\"./flows/registeredUsersFlow\";\nimport unregisteredUsersFlow from\"./flows/unregisteredUsersFlow\";\nconstwelcomeFlow=addKeyword(EVENTS.WELCOME).addAction(\nasync (_, { gotoFlow, state }) => {\nconstcheckDB=awaitfetch(\"http://my.app.example/checkDB\", {\n   method:\"POST\",\n   body:JSON.stringify({ phoneNumber:state.from }),\n   headers: {\n\"Content-Type\":\"application/json\",\n   },\n  });\nconststatus=awaitcheckDB.json();\nif (status ===undefined) {\nawaitstate.update({ Registration:false });\nreturngotoFlow(unregisteredUsersFlow);\n  }\nif (status ===true) {\nreturngotoFlow(registeredUsersFlow);\n  }\n }\n);\nconstmain=async () => {\nconstprovider=createProvider(BaileysProvider);\nconstdatabase=newMemoryDB();\nconstflow=createFlow([welcomeFlow, registeredUsersFlow, unregisteredUsersFlow]);\nawaitcreateBot({ flow, provider, database });\n};\nmain();\n\n```\nCopyCopied!\n## [Guides](https://www.builderbot.app/en/showcases/</en/showcases/gotoflow-use#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/showcases/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/showcases/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/showcases/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/showcases/</plugins>)\n## [Resources](https://www.builderbot.app/en/showcases/</en/showcases/gotoflow-use#resources>)\n### [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/showcases/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/showcases/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/showcases/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/showcases/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/showcases/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "import { createBot, createProvider, createFlow, addKeyword, MemoryDB, EVENTS } from \"@builderbot/bot\";\nimport { Baileys<PERSON>rovider } from \"@builderbot/provider-baileys\";\nimport registeredUsersFlow from \"./flows/registeredUsersFlow\";\nimport unregisteredUsersFlow from \"./flows/unregisteredUsersFlow\";\n\nconst welcomeFlow = addKeyword(EVENTS.WELCOME).addAction(\n  async (_, { gotoFlow, state }) => {\n    const checkDB = await fetch(\"http://my.app.example/checkDB\", {\n      method: \"POST\",\n      body: JSON.stringify({ phoneNumber: state.from }),\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n    });\n\n    const status = await checkDB.json();\n    if (status === undefined) {\n      await state.update({ Registration: false });\n      return gotoFlow(unregisteredUsersFlow);\n    }\n    if (status === true) {\n      return gotoFlow(registeredUsersFlow);\n    }\n  }\n);\n\nconst main = async () => {\n  const provider = createProvider(BaileysProvider);\n  const database = new MemoryDB();\n  const flow = createFlow([welcomeFlow, registeredUsersFlow, unregisteredUsersFlow]);\n\n  await createBot({ flow, provider, database });\n};\n\nmain();\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/showcases/multiple-messages", "title": "Multiple messages - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/showcases/</>)\n  * [Contribute](https://www.builderbot.app/en/showcases/</contribute>)\n  * [Course](https://www.builderbot.app/en/showcases/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/showcases/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/showcases/</>)\n  * [Documentation](https://www.builderbot.app/en/showcases/</en/showcases/multiple-messages#>)\n  * [Support](https://www.builderbot.app/en/showcases/</en/showcases/multiple-messages#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/showcases/</>)\n    * [Quickstart](https://www.builderbot.app/en/showcases/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/showcases/</concepts>)\n    * [Examples](https://www.builderbot.app/en/showcases/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/showcases/</add-functions>)\n    * [Context](https://www.builderbot.app/en/showcases/</context>)\n    * [Methods](https://www.builderbot.app/en/showcases/</methods>)\n    * [Events](https://www.builderbot.app/en/showcases/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/showcases/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/showcases/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/showcases/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/showcases/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/showcases/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/showcases/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/showcases/</deploy>)\n    * [Railway](https://www.builderbot.app/en/showcases/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/showcases/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/showcases/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/showcases/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/showcases/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/showcases/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/showcases/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/showcases/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/showcases/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/showcases/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/showcases/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/showcases/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/showcases/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/showcases/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/showcases/</contribute>)\n    * [Core](https://www.builderbot.app/en/showcases/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/showcases/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/showcases/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/showcases/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/showcases/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/showcases/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/showcases/</en/showcases/multiple-messages#>)\n\n\n# Multiple messages\n## [Issue](https://www.builderbot.app/en/showcases/</en/showcases/multiple-messages#issue>)\nSometimes, you need the bot to generate responses based on an event such as receiving images from users, but you don't want the bot to throw multiple and redundant responses if the user quickly sends several images.\n## [Possible Solution](https://www.builderbot.app/en/showcases/</en/showcases/multiple-messages#possible-solution>)\nTo ensure that the bot responds only once despite receiving too many calls in a short period of time, a debounce decorator could be implemented around the gotoFlow and endFlow methods.\nIn the example below I will show you how I implemented this solution in my bot to analyze several images and depending on whether they are images of furniture or not, terminate the flow with an error message or send it to another registration flow\nmedia-flow.tsdebounce.ts\n```\nimport path from\"node:path\";\nimport fs from\"node:fs/promises\";\nimport process from\"node:process\";\nimport { addKeyword, EVENTS } from\"@builderbot/bot\";\nimport { BaileysProvider } from\"@builderbot/provider-baileys\";\nimport { UrlToBase64 } from\"@builderbot-plugins/url-to-base64\";\nimport { PROMPT_IMAGE } from\"./prompt\";\nimport { debounce } from\"~/utils/debounce\";\nimport AIClass from\"~/services/OpenAIService\";\nimport { registerFlow } from\"../order/register.flow\";\n\nconstlocalPaths= [];\nletdebouncedEndFlow:(...args:any[]) =>void;\nletdebouncedGoToFlow:(...args:any[]) =>void;\nconstfilePath=path.join(process.cwd(),'src','database','images');\nexportconstmediaFlow=addKeyword<BaileysProvider>(EVENTS.MEDIA)\n.addAction(async(ctx, { provider, queue }) => { \nawaitqueue.enqueue('processImage',async () => {\nconstlocalPath=awaitprovider.saveFile(ctx, { path: filePath });\nlocalPaths.push(localPath);\n },'imageProcessingTask');\nawaitqueue.processQueue('processImage');\nawaitqueue.clearQueue('processImage');\nqueue.clearAndDone('processImage', {fingerIdRef:'imageProcessingTask'});\n})\n.addAction(async (_, { extensions, gotoFlow, endFlow }) => {\nconstai=extensions.ai asAIClass; \nif(!debouncedEndFlow &&!debouncedGoToFlow){\n  debouncedEndFlow =debounce(endFlow,1500);\n  debouncedGoToFlow =debounce(gotoFlow,1500);\n }\nfor(constpathof localPaths) {\nconst { data,mimetype } =UrlToBase64.fromFilePath(path);  \nconstaiResponse=awaitai.readImage(data,PROMPT_IMAGE, mimetype);\nif(aiResponse.includes('NOT_FURNITURE')) {\nfor(constfilePathof localPaths) {\nawaitfs.unlink(filePath);\n   }\nlocalPaths.length=0;\nreturndebouncedEndFlow('Ups! Asegurate de enviar una foto correcta de un mueble');\n  }  \n }\nlocalPaths.length=0;\nreturndebouncedGoToFlow(registerFlow)\n})\n\n```\nCopyCopied!\nRemember that this is an alternative solution, and it is possible that its implementation could be improved.\n## [Guides](https://www.builderbot.app/en/showcases/</en/showcases/multiple-messages#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/showcases/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/showcases/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/showcases/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/showcases/</plugins>)\n## [Resources](https://www.builderbot.app/en/showcases/</en/showcases/multiple-messages#resources>)\n### [Modularize](https://www.builderbot.app/en/showcases/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/showcases/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/showcases/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/showcases/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/showcases/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/showcases/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/showcases/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-ts", "code": "import path from \"node:path\";\nimport fs from \"node:fs/promises\";\nimport process from \"node:process\";\nimport { addKeyword, EVENTS } from \"@builderbot/bot\";\nimport { Bailey<PERSON><PERSON>rovider } from \"@builderbot/provider-baileys\";\nimport { UrlToBase64 } from \"@builderbot-plugins/url-to-base64\";\n\nimport { PROMPT_IMAGE } from \"./prompt\";\nimport { debounce } from \"~/utils/debounce\";\nimport AIClass from \"~/services/OpenAIService\";\nimport { registerFlow } from \"../order/register.flow\";\n\n\nconst localPaths = [];\nlet debouncedEndFlow:(...args: any[]) => void;\nlet debouncedGoToFlow:(...args: any[]) => void;\nconst filePath = path.join(process.cwd(), 'src', 'database', 'images');\n\nexport const mediaFlow = addKeyword<BaileysProvider>(EVENTS.MEDIA)\n.addAction(async(ctx, { provider, queue }) => { \n  await queue.enqueue('processImage', async () => {\n    const localPath = await provider.saveFile(ctx, { path: filePath });\n    localPaths.push(localPath);\n  }, 'imageProcessingTask');\n\n  await queue.processQueue('processImage');\n  await queue.clearQueue('processImage');\n  queue.clearAndDone('processImage', {fingerIdRef: 'imageProcessingTask'});\n})\n.addAction(async (_, { extensions, gotoFlow, endFlow }) => {\n\n  const ai = extensions.ai as AIClass;  \n  if(!debouncedEndFlow && !debouncedGoToFlow){\n    debouncedEndFlow = debounce(endFlow, 1500);\n    debouncedGoToFlow = debounce(gotoFlow, 1500);\n  }\n    \n  for(const path of localPaths) {\n    const { data, mimetype } = UrlToBase64.fromFilePath(path);    \n    const aiResponse = await ai.readImage(data, PROMPT_IMAGE, mimetype);\n\n    if(aiResponse.includes('NOT_FURNITURE')) {\n      for(const filePath of localPaths) {\n        await fs.unlink(filePath);\n      }\n      localPaths.length = 0;\n      return debouncedEndFlow('Ups! Asegurate de enviar una foto correcta de un mueble');\n    }   \n  }\n\n  localPaths.length = 0;\n  return debouncedGoToFlow(registerFlow)\n})\n\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/tutorials/migrate-to-builderbot", "title": "Migrating from bot-whatsapp to builderbot: A Simple Guide - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/tutorials/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/tutorials/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/tutorials/</>)\n  * [Contribute](https://www.builderbot.app/en/tutorials/</contribute>)\n  * [Course](https://www.builderbot.app/en/tutorials/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/tutorials/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/tutorials/</>)\n  * [Documentation](https://www.builderbot.app/en/tutorials/</en/tutorials/migrate-to-builderbot#>)\n  * [Support](https://www.builderbot.app/en/tutorials/</en/tutorials/migrate-to-builderbot#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/tutorials/</>)\n    * [Quickstart](https://www.builderbot.app/en/tutorials/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/tutorials/</concepts>)\n    * [Examples](https://www.builderbot.app/en/tutorials/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/tutorials/</add-functions>)\n    * [Context](https://www.builderbot.app/en/tutorials/</context>)\n    * [Methods](https://www.builderbot.app/en/tutorials/</methods>)\n    * [Events](https://www.builderbot.app/en/tutorials/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/tutorials/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/tutorials/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/tutorials/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/tutorials/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/tutorials/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/tutorials/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/tutorials/</deploy>)\n    * [Railway](https://www.builderbot.app/en/tutorials/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/tutorials/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/tutorials/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/tutorials/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/tutorials/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/tutorials/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/tutorials/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/tutorials/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/tutorials/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/tutorials/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/tutorials/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/tutorials/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/tutorials/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/tutorials/</tutorials/migrate-to-builderbot>)\n      * [Key Differences](https://www.builderbot.app/en/tutorials/</en/tutorials/migrate-to-builderbot#key-differences>)\n      * [Easy Migration Steps](https://www.builderbot.app/en/tutorials/</en/tutorials/migrate-to-builderbot#easy-migration-steps>)\n      * [Code Comparison](https://www.builderbot.app/en/tutorials/</en/tutorials/migrate-to-builderbot#code-comparison>)\n      * [Final Considerations](https://www.builderbot.app/en/tutorials/</en/tutorials/migrate-to-builderbot#final-considerations>)\n    * [API Rest](https://www.builderbot.app/en/tutorials/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/tutorials/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/tutorials/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/tutorials/</contribute>)\n    * [Core](https://www.builderbot.app/en/tutorials/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/tutorials/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/tutorials/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/tutorials/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/tutorials/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/tutorials/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/tutorials/</en/tutorials/migrate-to-builderbot#>)\n\n\n# Migrating from bot-whatsapp to builderbot: A Simple Guide\nbuilderbot is the next evolution of bot-whatsapp, maintaining 99% compatibility while introducing significant improvements. This guide will walk you through the straightforward migration process.\n## [Key Differences](https://www.builderbot.app/en/tutorials/</en/tutorials/migrate-to-builderbot#key-differences>)\n  1. **Name Change** : From bot-whatsapp to builderbot\n  2. **Enhanced Language Support** : Now includes TypeScript in addition to JavaScript\n  3. **Improved Features** : New functionalities while maintaining familiar concepts\n\n\n## [Easy Migration Steps](https://www.builderbot.app/en/tutorials/</en/tutorials/migrate-to-builderbot#easy-migration-steps>)\n### Update Dependencies\nFirst, install the latest builderbot core:\n```\nnpminstall@builderbot/bot@latest\n# or\npnpmadd@builderbot/bot@latest\n\n```\nCopyCopied!\n### Install Your Preferred Provider\nChoose and install the provider you're using:\nbaileysmetatwiliovenomwppconnectwhatsapp-web\n```\npnpminstall@builderbot/provider-baileys@latest\n\n```\nCopyCopied!\n### Update Imports\nModify your imports to use builderbot:\n```\n// Old\nconst { createBot,createProvider,createFlow,addKeyword } =require('@bot-whatsapp/bot')\n// New\nconst { createBot,createProvider,createFlow,addKeyword,MemoryDB } =require('@builderbot/bot')\n\n```\nCopyCopied!\n### Update Provider\nChange the provider import and initialization:\n```\n// Old\nconstWebWhatsappProvider=require('@bot-whatsapp/provider/web-whatsapp')\n// New\nconst { BaileysProvider } =require('@builderbot/bot')\n// When initializing:\nconstadapterProvider=createProvider(BaileysProvider)\nadapterProvider.initHttpServer(3000) // New feature in builderbot\n\n```\nCopyCopied!\n### Update Database\nUpdate your database adapter:\n```\n// Old\nconstMockAdapter=require('@bot-whatsapp/database/mock')\nconstadapterDB=newMockAdapter()\n// New\nconst { MemoryDB } =require('@builderbot/bot')\nconstadapterDB=newMemoryDB()\n\n```\nCopyCopied!\n### Review and Update Flows\nWhile most of your flows will work as-is, consider using new features like `addAction` for more complex logic:\n```\nconstinfoFlow=addKeyword('info')\n.addAction(async (ctx, { flowDynamix }) => {\nawaitflowDynamix(`Welcome ${ctx.name}`)\n  })\n\n```\nCopyCopied!\n## [Code Comparison](https://www.builderbot.app/en/tutorials/</en/tutorials/migrate-to-builderbot#code-comparison>)\nHere's a side-by-side comparison of a basic bot setup in bot-whatsapp and builderbot:\nbot-whatsappbuilderbot\n```\nconst { createBot,createProvider,createFlow,addKeyword } =require('@bot-whatsapp/bot')\nconstBaileysProvider=require('@bot-whatsapp/provider/baileys')\nconstMockAdapter=require('@bot-whatsapp/database/mock')\nconstflowPrincipal=addKeyword(['hola','alo'])\n.addAnswer(['Hola, bienvenido a mi tienda','¿Como puedo ayudarte?'])\n.addAnswer(['Tengo:','Zapatos','Bolsos','etc ...'])\nconstmain=async () => {\nconstadapterDB=newMockAdapter()\nconstadapterFlow=createFlow([flowPrincipal])\nconstadapterProvider=createProvider(BaileysProvider)\ncreateBot({\n    flow: adapterFlow,\n    provider: adapterProvider,\n    database: adapterDB,\n  })\n}\nmain()\n\n```\nCopyCopied!\n## [Final Considerations](https://www.builderbot.app/en/tutorials/</en/tutorials/migrate-to-builderbot#final-considerations>)\n  * Migration should be relatively straightforward due to high compatibility\n  * Take advantage of new builderbot features, especially if you opt to use TypeScript\n  * Maintain your existing development practices and patterns, as they remain valid\n\n\n## [Guides](https://www.builderbot.app/en/tutorials/</en/tutorials/migrate-to-builderbot#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/tutorials/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/tutorials/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/tutorials/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/tutorials/</plugins>)\n## [Resources](https://www.builderbot.app/en/tutorials/</en/tutorials/migrate-to-builderbot#resources>)\n### [Modularize](https://www.builderbot.app/en/tutorials/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/tutorials/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/tutorials/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/tutorials/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/tutorials/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/tutorials/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/tutorials/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-sh", "code": "npm install @builderbot/bot@latest\n# or\npnpm add @builderbot/bot@latest\n"}, {"language": "language-bash", "code": "pnpm install @builderbot/provider-baileys@latest\n"}, {"language": "language-javascript", "code": "// Old\nconst { createBot, createProvider, createFlow, addKeyword } = require('@bot-whatsapp/bot')\n\n// New\nconst { createBot, createProvider, createFlow, addKeyword, MemoryDB } = require('@builderbot/bot')\n"}, {"language": "language-javascript", "code": "// Old\nconst WebWhatsappProvider = require('@bot-whatsapp/provider/web-whatsapp')\n\n// New\nconst { BaileysProvider } = require('@builderbot/bot')\n\n// When initializing:\nconst adapterProvider = createProvider(BaileysProvider)\nadapterProvider.initHttpServer(3000) // New feature in builderbot\n"}, {"language": "language-javascript", "code": "// Old\nconst MockAdapter = require('@bot-whatsapp/database/mock')\nconst adapterDB = new MockAdapter()\n\n// New\nconst { MemoryDB } = require('@builderbot/bot')\nconst adapterDB = new MemoryDB()\n"}, {"language": "language-javascript", "code": "const infoFlow = addKeyword('info')\n    .addAction(async (ctx, { flowDynamix }) => {\n        await flowDynamix(`Welcome ${ctx.name}`)\n    })\n"}, {"language": "language-javascript", "code": "const { createBot, createProvider, createFlow, addKeyword } = require('@bot-whatsapp/bot')\n\nconst BaileysProvider = require('@bot-whatsapp/provider/baileys')\nconst MockAdapter = require('@bot-whatsapp/database/mock')\n\nconst flowPrincipal = addKeyword(['hola', 'alo'])\n    .addAnswer(['Hola, bienvenido a mi tienda', '¿Como puedo ayudarte?'])\n    .addAnswer(['Tengo:', 'Zapatos', 'Bolsos', 'etc ...'])\n\nconst main = async () => {\n    const adapterDB = new MockAdapter()\n    const adapterFlow = createFlow([flowPrincipal])\n    const adapterProvider = createProvider(BaileysProvider)\n    createBot({\n        flow: adapterFlow,\n        provider: adapterProvider,\n        database: adapterDB,\n    })\n}\n\nmain()\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/tutorials/api-use", "title": "How to implement a REST API? - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/tutorials/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/tutorials/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/tutorials/</>)\n  * [Contribute](https://www.builderbot.app/en/tutorials/</contribute>)\n  * [Course](https://www.builderbot.app/en/tutorials/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/tutorials/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/tutorials/</>)\n  * [Documentation](https://www.builderbot.app/en/tutorials/</en/tutorials/api-use#>)\n  * [Support](https://www.builderbot.app/en/tutorials/</en/tutorials/api-use#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/tutorials/</>)\n    * [Quickstart](https://www.builderbot.app/en/tutorials/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/tutorials/</concepts>)\n    * [Examples](https://www.builderbot.app/en/tutorials/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/tutorials/</add-functions>)\n    * [Context](https://www.builderbot.app/en/tutorials/</context>)\n    * [Methods](https://www.builderbot.app/en/tutorials/</methods>)\n    * [Events](https://www.builderbot.app/en/tutorials/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/tutorials/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/tutorials/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/tutorials/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/tutorials/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/tutorials/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/tutorials/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/tutorials/</deploy>)\n    * [Railway](https://www.builderbot.app/en/tutorials/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/tutorials/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/tutorials/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/tutorials/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/tutorials/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/tutorials/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/tutorials/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/tutorials/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/tutorials/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/tutorials/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/tutorials/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/tutorials/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/tutorials/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/tutorials/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/tutorials/</tutorials/api-use>)\n      * [Send Single Message](https://www.builderbot.app/en/tutorials/</en/tutorials/api-use#send-single-message>)\n      * [Send with Media](https://www.builderbot.app/en/tutorials/</en/tutorials/api-use#send-with-media>)\n      * [Block Users](https://www.builderbot.app/en/tutorials/</en/tutorials/api-use#block-users>)\n      * [Trigger Flows](https://www.builderbot.app/en/tutorials/</en/tutorials/api-use#trigger-flows>)\n    * [Gemini](https://www.builderbot.app/en/tutorials/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/tutorials/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/tutorials/</contribute>)\n    * [Core](https://www.builderbot.app/en/tutorials/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/tutorials/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/tutorials/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/tutorials/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/tutorials/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/tutorials/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/tutorials/</en/tutorials/api-use#>)\n\n\n# How to implement a REST API?\nIn many occasions we need to send a message via HTTP request is very useful to send reminders or order confirmations. In this case each provider implements a `handleCtx` function which allows you to inject the bot instantiation and access its functions from a controller.\n## [Send Single Message](https://www.builderbot.app/en/tutorials/</en/tutorials/api-use#send-single-message>)\n### Request\nPOST\n/v1/messages\n```\ncurlhttps://api.example.chat/v1/messages \\\n-dnumber=\"34000000\" \\\n-dmessage=\"Hello!\"\n\n```\nCopyCopied!\nIn the exercise below you can get an example of how it works. It is interesting to understand that internally the library already implements a very light version of HTTP similar to express but called [polka](https://www.builderbot.app/en/tutorials/<https:/github.com/lukeed/polka/tree/main>).\n### app.ts\n```\nimport'dotenv/config'\nimport { createBot, MemoryDB, createProvider } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\nimport flow from'./flows';\nconstPORT=process.env.PORT??3001\nconstmain=async () => {\nconstprovider=createProvider(BaileysProvider)\nconst { handleCtx,httpServer } =awaitcreateBot({\n    database:newMemoryDB(),\n    provider,\n    flow,\n  })\nhttpServer(+PORT)\nprovider.server.post('/v1/messages',handleCtx(async (bot, req, res) => {\nconst { number,message } =req.body\nawaitbot.sendMessage(number, message, {})\nreturnres.end('send')\n  }))\n}\nmain()\n\n```\nCopyCopied!\n## [Send with Media](https://www.builderbot.app/en/tutorials/</en/tutorials/api-use#send-with-media>)\n### Request\nMedia LinkMedia Local\nPOST\n/v1/messages\n```\ncurlhttps://api.example.chat/v1/messages \\\n-dnumber=\"34000000\" \\\n-dmessage=\"Ey! send media\"\n\n```\nCopyCopied!\nIn the following example you will understand how to send files, images or videos from an api when the file is in a public URL or also if you want to upload it through a request.\napp-media.tsapp-media.storage.ts\n```\nimport'dotenv/config'\nimport { createBot, MemoryDB, createProvider } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\nimport flow from'./flows';\nconstPORT=process.env.PORT??3001\nconstmain=async () => {\nconstprovider=createProvider(BaileysProvider)\nconst { handleCtx,httpServer } =awaitcreateBot({\n    database:newMemoryDB(),\n    provider,\n    flow,\n  })\nhttpServer(+PORT)\nprovider.server.post('/v1/messages',handleCtx(async (bot, req, res) => {\nconst { number,message,media } =req.body\nawaitbot.sendMessage(number, message, { media }) // https://i.imgur.com/0HpzsEm.png\nreturnres.end('send')\n  }))\n}\nmain()\n\n```\nCopyCopied!\n## [Block Users](https://www.builderbot.app/en/tutorials/</en/tutorials/api-use#block-users>)\n### Request\nPOST\n/v1/blacklist\n```\ncurlhttps://api.example.chat/v1/blacklist \\\n-dnumber=\"34000000\" \\\n-dintent=\"'add' or 'remove'\"\n\n```\nCopyCopied!\nIn the following example you will understand how to add numbers to a blacklist to prevent the bot from answering those numbers, very useful when we want to talk to a person without the bot answering.\n### blacklist-api.ts\n```\nimport { createBot, MemoryDB, createProvider } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\nimport flow from'./flows';\nconstPORT=process.env.PORT??3001\nconstmain=async () => {\nconstprovider=createProvider(BaileysProvider)\nconst { handleCtx,httpServer } =awaitcreateBot({\n    database:newMemoryDB(),\n    provider,\n    flow,\n  })\nhttpServer(+PORT)\nprovider.server.post('/v1/blacklist',handleCtx(async (bot, req, res) => {\nconst { number,intent } =req.body\nif (intent ==='remove') bot.blacklist.remove(`340000000`)\nif (intent ==='add') bot.blacklist.add(`340000000`)\nres.writeHead(200, { 'Content-Type':'application/json' })\nreturnres.end(JSON.stringify({ status:'ok', number, intent }))\n  }))\n}\nmain()\n\n```\nCopyCopied!\n## [Trigger Flows](https://www.builderbot.app/en/tutorials/</en/tutorials/api-use#trigger-flows>)\n### Request\nPOST\n/v1/register\n```\ncurlhttps://api.example.chat/v1/register \\\n-dnumber=\"34000000\" \\\n-dname=\"Joe\"\n\n```\nCopyCopied!\nIn the following example you will understand how to start a flow to a given number from an API request, it is a new experimental function that may undergo changes in the future.\n### trigger-flow-api.ts\n```\nimport { createBot, MemoryDB, createProvider } from'@builderbot/bot'\nimport { BaileysProvider } from'@builderbot/provider-baileys'\nconstPORT=process.env.PORT??3001\nconstregisterFlow=addKeyword(utils.setEvent('EVENT_REGISTER'))\n.addAnswer(`What is your name?`, { capture:true },async (ctx, { state }) => {\nawaitstate.update({ name:ctx.body })\n  })\n.addAnswer('What is your age?', { capture:true },async (ctx, { state }) => {\nawaitstate.update({ age:ctx.body })\n  })\n.addAction(async (_, { flowDynamic, state }) => {\nawaitflowDynamic(`${state.get('name')}, thanks for your information!: Your age: ${state.get('age')}`)\n  })\nconstmain=async () => {\nconstprovider=createProvider(BaileysProvider)\nconst { handleCtx,httpServer } =awaitcreateBot({\n    database:newMemoryDB(),\n    provider,\n    flow:createFlow([registerFlow]),\n  })\nhttpServer(+PORT)\nprovider.server.post('/v1/register',handleCtx(async (bot, req, res) => {\nconst { number,name } =req.body\nawaitbot.dispatch('EVENT_REGISTER', { from: number, name })\nreturnres.end('trigger')\n  }))\n}\nmain()\n\n```\nCopyCopied!\n## [Guides](https://www.builderbot.app/en/tutorials/</en/tutorials/api-use#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/tutorials/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/tutorials/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/tutorials/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/tutorials/</plugins>)\n## [Resources](https://www.builderbot.app/en/tutorials/</en/tutorials/api-use#resources>)\n### [Modularize](https://www.builderbot.app/en/tutorials/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/tutorials/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/tutorials/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/tutorials/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/tutorials/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/tutorials/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/tutorials/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-bash", "code": "curl https://api.example.chat/v1/messages \\\n  -d number=\"34000000\" \\\n  -d message=\"Hello!\"\n"}, {"language": "language-ts", "code": "import 'dotenv/config'\nimport { createBot, MemoryDB, createProvider } from '@builderbot/bot'\nimport { BaileysProvider } from '@builderbot/provider-baileys'\nimport flow from './flows';\n\nconst PORT = process.env.PORT ?? 3001\n\nconst main = async () => {\n    const provider = createProvider(BaileysProvider)\n\n    const { handleCtx, httpServer } = await createBot({\n        database: new MemoryDB(),\n        provider,\n        flow,\n    })\n\n    httpServer(+PORT)\n\n    provider.server.post('/v1/messages', handleCtx(async (bot, req, res) => {\n        const { number, message } = req.body\n        await bot.sendMessage(number, message, {})\n        return res.end('send')\n    }))\n}\n\nmain()\n"}, {"language": "language-bash", "code": "curl https://api.example.chat/v1/messages \\\n  -d number=\"34000000\" \\\n  -d message=\"Ey! send media\"\n"}, {"language": "language-ts", "code": "import 'dotenv/config'\nimport { createBot, MemoryDB, createProvider } from '@builderbot/bot'\nimport { BaileysProvider } from '@builderbot/provider-baileys'\nimport flow from './flows';\n\nconst PORT = process.env.PORT ?? 3001\n\nconst main = async () => {\n    const provider = createProvider(BaileysProvider)\n\n    const { handleCtx, httpServer } = await createBot({\n        database: new MemoryDB(),\n        provider,\n        flow,\n    })\n\n    httpServer(+PORT)\n\n    provider.server.post('/v1/messages', handleCtx(async (bot, req, res) => {\n        const { number, message, media } = req.body\n        await bot.sendMessage(number, message, { media }) // https://i.imgur.com/0HpzsEm.png\n        return res.end('send')\n    }))\n}\n\nmain()\n"}, {"language": "language-bash", "code": "curl https://api.example.chat/v1/blacklist \\\n  -d number=\"34000000\" \\\n  -d intent=\"'add' or 'remove'\"\n"}, {"language": "language-ts", "code": "import { createBot, MemoryDB, createProvider } from '@builderbot/bot'\nimport { <PERSON>s<PERSON><PERSON>ider } from '@builderbot/provider-baileys'\nimport flow from './flows';\n\nconst PORT = process.env.PORT ?? 3001\n\nconst main = async () => {\n    const provider = createProvider(BaileysProvider)\n\n    const { handleCtx, httpServer } = await createBot({\n        database: new MemoryDB(),\n        provider,\n        flow,\n    })\n\n    httpServer(+PORT)\n\n    provider.server.post('/v1/blacklist', handleCtx(async (bot, req, res) => {\n        const { number, intent } = req.body\n        if (intent === 'remove') bot.blacklist.remove(`340000000`)\n        if (intent === 'add') bot.blacklist.add(`340000000`)\n\n        res.writeHead(200, { 'Content-Type': 'application/json' })\n        return res.end(JSON.stringify({ status: 'ok', number, intent }))\n    }))\n}\n\nmain()\n"}, {"language": "language-bash", "code": "curl https://api.example.chat/v1/register \\\n  -d number=\"34000000\" \\\n  -d name =\"<PERSON>\"\n"}, {"language": "language-ts", "code": "import { createBot, MemoryDB, createProvider } from '@builderbot/bot'\nimport { Baileys<PERSON>rovider } from '@builderbot/provider-baileys'\n\nconst PORT = process.env.PORT ?? 3001\n\nconst registerFlow = addKeyword(utils.setEvent('EVENT_REGISTER'))\n    .addAnswer(`What is your name?`, { capture: true }, async (ctx, { state }) => {\n        await state.update({ name: ctx.body })\n    })\n    .addAnswer('What is your age?', { capture: true }, async (ctx, { state }) => {\n        await state.update({ age: ctx.body })\n    })\n    .addAction(async (_, { flowDynamic, state }) => {\n        await flowDynamic(`${state.get('name')}, thanks for your information!: Your age: ${state.get('age')}`)\n    })\n\nconst main = async () => {\n    const provider = createProvider(BaileysProvider)\n\n    const { handleCtx, httpServer } = await createBot({\n        database: new MemoryDB(),\n        provider,\n        flow: createFlow([registerFlow]),\n    })\n\n    httpServer(+PORT)\n\n    provider.server.post('/v1/register', handleCtx(async (bot, req, res) => {\n        const { number, name } = req.body\n        await bot.dispatch('EVENT_REGISTER', { from: number, name })\n        return res.end('trigger')\n    }))\n}\n\nmain()\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/tutorials/chatbot-with-gemini", "title": "Chatbot with Context - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/tutorials/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/tutorials/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/tutorials/</>)\n  * [Contribute](https://www.builderbot.app/en/tutorials/</contribute>)\n  * [Course](https://www.builderbot.app/en/tutorials/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/tutorials/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/tutorials/</>)\n  * [Documentation](https://www.builderbot.app/en/tutorials/</en/tutorials/chatbot-with-gemini#>)\n  * [Support](https://www.builderbot.app/en/tutorials/</en/tutorials/chatbot-with-gemini#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/tutorials/</>)\n    * [Quickstart](https://www.builderbot.app/en/tutorials/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/tutorials/</concepts>)\n    * [Examples](https://www.builderbot.app/en/tutorials/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/tutorials/</add-functions>)\n    * [Context](https://www.builderbot.app/en/tutorials/</context>)\n    * [Methods](https://www.builderbot.app/en/tutorials/</methods>)\n    * [Events](https://www.builderbot.app/en/tutorials/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/tutorials/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/tutorials/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/tutorials/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/tutorials/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/tutorials/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/tutorials/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/tutorials/</deploy>)\n    * [Railway](https://www.builderbot.app/en/tutorials/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/tutorials/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/tutorials/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/tutorials/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/tutorials/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/tutorials/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/tutorials/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/tutorials/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/tutorials/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/tutorials/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/tutorials/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/tutorials/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/tutorials/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/tutorials/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/tutorials/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/tutorials/</tutorials/chatbot-with-gemini>)\n      * [Install Gemini](https://www.builderbot.app/en/tutorials/</en/tutorials/chatbot-with-gemini#install-gemini>)\n      * [Context Example](https://www.builderbot.app/en/tutorials/</en/tutorials/chatbot-with-gemini#context-example>)\n      * [Vision Example](https://www.builderbot.app/en/tutorials/</en/tutorials/chatbot-with-gemini#vision-example>)\n    * [Langchain](https://www.builderbot.app/en/tutorials/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/tutorials/</contribute>)\n    * [Core](https://www.builderbot.app/en/tutorials/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/tutorials/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/tutorials/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/tutorials/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/tutorials/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/tutorials/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/tutorials/</en/tutorials/chatbot-with-gemini#>)\n\n\n# Chatbot with Context\nThe gemini model is a generous and valuable resource to use if it comes to creating a product with AI that has both vision and text generation and without spending a penny is what we want.\nTo start remember to install the gemini plugin and have your [Google Api key](https://www.builderbot.app/en/tutorials/<https:/aistudio.google.com/app/apikey>)\n## [Install Gemini](https://www.builderbot.app/en/tutorials/</en/tutorials/chatbot-with-gemini#install-gemini>)\nWe started the installation of a package that will help you to implement the Gemin power in builderbot.\n```\npnpminstall@builderbot-plugins/gemini-layer\n\n```\nCopyCopied!\n[More info about it](https://www.builderbot.app/en/tutorials/<https:/github.com/codigoencasa/bot-plugins/tree/main/packages/gemini>)\n## [Context Example](https://www.builderbot.app/en/tutorials/</en/tutorials/chatbot-with-gemini#context-example>)\nSometimes we don't want to place so many keywords or answers in validations of our flows, we want some freedom when it comes to responding, that's why we have the gemini plugin to respond based on a context\nthe context indicates between key value, the different scenarios in which the user may be when interacting with the assistant\n**context** : It is the object that will contain all the necessary properties you need.\nlayers/context.layer.tsflows/context.flow.tsapp.ts\n```\nimport { geminiLayer } from\"@builderbot-plugins/gemini-layer\"\nexportdefaultasync (...bot:any) =>awaitgeminiLayer({\n  context: {\n    name:'your name',\n    email:'your email',\n    summary:'your summary'\n// and more properties\n  }\n}, bot)\n\n```\nCopyCopied!\n## [Vision Example](https://www.builderbot.app/en/tutorials/</en/tutorials/chatbot-with-gemini#vision-example>)\nField| Description  \n---|---  \n`vision`| true  \n`visionPrompt`| What you want the model to see  \n`image_path`| The route where the image will be downloaded to be analyzed later  \n`cb`| A callback that receives the same interface as addAction and which returns an answer in the state  \nlayers/image.layer.tsflows/image.flow.tsapp.ts\n```\nimport { geminiLayer } from\"@builderbot-plugins/gemini-layer\"\nconstvisionPrompt='Give me a brief summary of what you see in the picture'\nexportdefaultasync (...bot:any) =>awaitgeminiLayer({\n  vision:true,\n  visionPrompt,\n  image_path:'./',\ncb:async (_, { state, gotoFlow }) => {\nconst { answer } =state.getMyState()\nconsole.log('answer about image', answer)\nif (answer ==='anything else you want to validate') {\nreturngotoFlow('any flow you want to go')\n    }\n  }\n}, bot)\n\n```\nCopyCopied!\n## [Guides](https://www.builderbot.app/en/tutorials/</en/tutorials/chatbot-with-gemini#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/tutorials/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/tutorials/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/tutorials/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/tutorials/</plugins>)\n## [Resources](https://www.builderbot.app/en/tutorials/</en/tutorials/chatbot-with-gemini#resources>)\n### [Modularize](https://www.builderbot.app/en/tutorials/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/tutorials/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/tutorials/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/tutorials/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/tutorials/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/tutorials/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/tutorials/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-bash", "code": "pnpm install @builderbot-plugins/gemini-layer\n"}, {"language": "language-ts", "code": "import { geminiLayer } from \"@builderbot-plugins/gemini-layer\"\n\nexport default async (...bot: any) => await geminiLayer({\n    context: {\n        name: 'your name',\n        email: 'your email',\n        summary: 'your summary'\n        // and more properties\n    }\n}, bot)\n"}, {"language": "language-ts", "code": "import { geminiLayer } from \"@builderbot-plugins/gemini-layer\"\n\nconst visionPrompt = 'Give me a brief summary of what you see in the picture'\n\nexport default async (...bot: any) => await geminiLayer({\n    vision: true,\n    visionPrompt,\n    image_path: './',\n    cb: async (_, { state, gotoFlow }) => {\n        const { answer } = state.getMyState()\n        console.log('answer about image', answer)\n\n        if (answer === 'anything else you want to validate') {\n            return gotoFlow('any flow you want to go')\n        }\n    }\n}, bot)\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/tutorials/langchain", "title": "Langchain - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/tutorials/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/tutorials/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/tutorials/</>)\n  * [Contribute](https://www.builderbot.app/en/tutorials/</contribute>)\n  * [Course](https://www.builderbot.app/en/tutorials/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/tutorials/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/tutorials/</>)\n  * [Documentation](https://www.builderbot.app/en/tutorials/</en/tutorials/langchain#>)\n  * [Support](https://www.builderbot.app/en/tutorials/</en/tutorials/langchain#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/tutorials/</>)\n    * [Quickstart](https://www.builderbot.app/en/tutorials/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/tutorials/</concepts>)\n    * [Examples](https://www.builderbot.app/en/tutorials/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/tutorials/</add-functions>)\n    * [Context](https://www.builderbot.app/en/tutorials/</context>)\n    * [Methods](https://www.builderbot.app/en/tutorials/</methods>)\n    * [Events](https://www.builderbot.app/en/tutorials/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/tutorials/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/tutorials/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/tutorials/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/tutorials/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/tutorials/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/tutorials/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/tutorials/</deploy>)\n    * [Railway](https://www.builderbot.app/en/tutorials/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/tutorials/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/tutorials/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/tutorials/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/tutorials/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/tutorials/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/tutorials/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/tutorials/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/tutorials/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/tutorials/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/tutorials/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/tutorials/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/tutorials/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/tutorials/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/tutorials/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/tutorials/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/tutorials/</tutorials/langchain>)\n      * [Install](https://www.builderbot.app/en/tutorials/</en/tutorials/langchain#install>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/tutorials/</contribute>)\n    * [Core](https://www.builderbot.app/en/tutorials/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/tutorials/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/tutorials/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/tutorials/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/tutorials/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/tutorials/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/tutorials/</en/tutorials/langchain#>)\n\n\n# Langchain\nWhen we want something more personality and intelligence in our assistants, the first thing we think about is Openai, what if I tell you that there is a simple way to get the most out of your LLM?\n## [Install](https://www.builderbot.app/en/tutorials/</en/tutorials/langchain#install>)\nlet's look at a simple but very valuable trick. to be able to know the user's intention, we have tried it before with DialogFlow but what a headache, let's go for something easier\n```\npnpmi@langchain/openai@langchain/corezod\n\n```\nCopyCopied!\nai/catch-intention.tsapp.ts\n```\nimport { z } from\"zod\";\nimport { ChatOpenAI, ChatPromptTemplate } from\"@langchain/openai\";\nexportconstopenAI=newChatOpenAI({\n  modelName:'gpt-4',\n  openAIApiKey:'YOUR_API_KEY_HERE',\n});\nconstSYSTEM_STRUCT=`just only history based: \n{history}\nAnswer the users question as best as possible.`;\nexportconstPROMPT_STRUCT=ChatPromptTemplate.fromMessages([\n  [\"system\",SYSTEM_STRUCT],\n  [\"human\",\"{question}\"]\n]);\nconstcatchIntention=z.object(\n  {\n    intention:z.enum(['UNKNOWN','SALES','GREETING','CLOSURE'])\n.describe('Categorize the following conversation and decide what the intention is')\n  }\n).describe('Given the following products, you should structure it in the best way, do not alter or edit anything');\nconstllmWithToolsCatchIntention=openAI.withStructuredOutput(catchIntention, {\n  name:\"CatchIntention\",\n});\nexportconstgetIntention=async (text:string):Promise<string> => {\ntry {\nconst { intention } =awaitPROMPT_STRUCT.pipe(llmWithToolsCatchIntention).invoke({\n      question: text,\n      history:awaithistory.getHistory(state)\n    });\nreturnPromise.resolve(String(intention).toLocaleLowerCase());\n  } catch (errorIntention) {\nreturnPromise.resolve('unknown');\n  }\n};\n\n```\nCopyCopied!\nThat way you can validate the intentions of your end customer and set up your own purchase flow as easy as that\n## [Guides](https://www.builderbot.app/en/tutorials/</en/tutorials/langchain#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/tutorials/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/tutorials/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/tutorials/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/tutorials/</plugins>)\n## [Resources](https://www.builderbot.app/en/tutorials/</en/tutorials/langchain#resources>)\n### [Modularize](https://www.builderbot.app/en/tutorials/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/tutorials/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/tutorials/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/tutorials/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/tutorials/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/tutorials/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/tutorials/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-bash", "code": "pnpm i @langchain/openai @langchain/core zod\n"}, {"language": "language-ts", "code": "import { z } from \"zod\";\nimport { ChatOpenAI, ChatPromptTemplate } from \"@langchain/openai\";\n\nexport const openAI = new ChatOpenAI({\n    modelName: 'gpt-4',\n    openAIApiKey: 'YOUR_API_KEY_HERE',\n});\n\nconst SYSTEM_STRUCT = `just only history based: \n{history}\n\nAnswer the users question as best as possible.`;\n\nexport const PROMPT_STRUCT = ChatPromptTemplate.fromMessages([\n    [\"system\", SYSTEM_STRUCT],\n    [\"human\", \"{question}\"]\n]);\n\nconst catchIntention = z.object(\n    {\n        intention: z.enum(['UNKNOWN', 'SALES', 'GREETING', 'CLOSURE'])\n            .describe('Categorize the following conversation and decide what the intention is')\n    }\n).describe('Given the following products, you should structure it in the best way, do not alter or edit anything');\n\nconst llmWithToolsCatchIntention = openAI.withStructuredOutput(catchIntention, {\n    name: \"CatchIntention\",\n});\n\nexport const getIntention = async (text: string): Promise<string> => {\n    try {\n        const { intention } = await PROMPT_STRUCT.pipe(llmWithToolsCatchIntention).invoke({\n            question: text,\n            history: await history.getHistory(state)\n        });\n\n        return Promise.resolve(String(intention).toLocaleLowerCase());\n    } catch (errorIntention) {\n        return Promise.resolve('unknown');\n    }\n};\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/contribute", "title": "Contribute - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/contribute#>)\n  * [Support](https://www.builderbot.app/en/</en/contribute#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n      * [Quick View](https://www.builderbot.app/en/</en/contribute#quick-view>)\n      * [Why Contribute?](https://www.builderbot.app/en/</en/contribute#why-contribute>)\n      * [How to Contribute](https://www.builderbot.app/en/</en/contribute#how-to-contribute>)\n      * [GitHub Workflow](https://www.builderbot.app/en/</en/contribute#git-hub-workflow>)\n      * [Writing MDX](https://www.builderbot.app/en/</en/contribute#writing-mdx>)\n      * [VSCode](https://www.builderbot.app/en/</en/contribute#vs-code>)\n      * [Extensions](https://www.builderbot.app/en/</en/contribute#extensions>)\n      * [Review Process](https://www.builderbot.app/en/</en/contribute#review-process>)\n      * [File Structure](https://www.builderbot.app/en/</en/contribute#file-structure>)\n      * [Required Fields](https://www.builderbot.app/en/</en/contribute#required-fields>)\n      * [Code Blocks](https://www.builderbot.app/en/</en/contribute#code-blocks>)\n      * [Language and Filename](https://www.builderbot.app/en/</en/contribute#language-and-filename>)\n      * [Grouped code blocks](https://www.builderbot.app/en/</en/contribute#grouped-code-blocks>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/contribute#>)\n\n\n# Contribute\nWelcome to the **BuilderBot** Contribution Guide We're glad to have you here.\nThis page provides instructions on how to edit BuilderBot documentation. Our goal is to ensure that everyone in the community feels empowered to contribute and improve our documentation.\n## [Quick View](https://www.builderbot.app/en/</en/contribute#quick-view>)\n  * Make a fork of the [project](https://www.builderbot.app/en/<https:/github.com/codigoencasa/documentation/fork>)\n  * Clone the project `git clone https://github.com/USERNAME/documentation`\n  * Install dependencies `npm install`\n  * Make your changes\n  * Send your contributions (PullRequest)\n\n\n## [Why Contribute?](https://www.builderbot.app/en/</en/contribute#why-contribute>)\nOpen source work never ends, and neither does documentation. Contributing to the documentation is a great way for beginners to get involved in open source and for experienced developers to clarify more complex issues while sharing their knowledge with the community.\nBy contributing to BuilderBot documentation, you help us create a more robust learning resource for all developers. If you've found a typo, a confusing section, or noticed that a particular topic is missing, your contributions are welcome and appreciated.\n## [How to Contribute](https://www.builderbot.app/en/</en/contribute#how-to-contribute>)\nThe content of the documentation is located in the [BuilderBot repository](https://www.builderbot.app/en/<https:/github.com/codigoencasa/documentation>). To contribute, you can edit the files directly on GitHub or clone the repository and edit the files locally.\n## [GitHub Workflow](https://www.builderbot.app/en/</en/contribute#git-hub-workflow>)\nIf you're new to GitHub, we recommend you read the GitHub Open Source Guide to learn how to fork a repository, create a branch, and send a pull request.\nThe code in the underlying documents lives in a private codebase that syncs with the public BuilderBot repository. This means that you cannot preview the docs locally. However, you will see your changes in builderbot.app after merging a pull request.\n## [Writing MDX](https://www.builderbot.app/en/</en/contribute#writing-mdx>)\nThe docs are written in [MDX](https://www.builderbot.app/en/<https:/mdxjs.com/>), a markdown format that supports JSX syntax. This allows us to embed React components in the docs. See the [GitHub Markdown Guide](https://www.builderbot.app/en/<https:/docs.github.com/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax>) for a quick overview of markdown syntax.\n## [VSCode](https://www.builderbot.app/en/</en/contribute#vs-code>)\n### Previewing Changes Locally\nVSCode has a built-in markdown previewer that you can use to see your edits locally. To enable the previewer for MDX files, you'll need to add a configuration option to your user settings.\nOpen the command palette (`⌘ + ⇧ + P` on Mac or `Ctrl + Shift + P` on Windows) and search from `Preferences: Open User Settings (JSON)`.\nThen, add the following line to your `settings.json` file:\n```\n{\n\"files.associations\": {\n\"*.mdx\":\"markdown\"\n }\n}\n\n```\nCopyCopied!\nNext, open the command palette again, and search for `Markdown: Preview File` or `Markdown: Open Preview to the Side`. This will open a preview window where you can see your formatted changes.\n## [Extensions](https://www.builderbot.app/en/</en/contribute#extensions>)\nWe also recommend the following extensions for VSCode users:\n  * [MDX](https://www.builderbot.app/en/<https:/marketplace.visualstudio.com/items?itemName=unifiedjs.vscode-mdx>): Intellisense and syntax highlighting for MDX.\n  * [Grammarly](https://www.builderbot.app/en/<https:/marketplace.visualstudio.com/items?itemName=znck.grammarly>): Grammar and spell checker.\n  * [Prettier](https://www.builderbot.app/en/<https:/marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode>): Format MDX files on save.\n\n\n## [Review Process](https://www.builderbot.app/en/</en/contribute#review-process>)\nOnce you have submitted your contribution, a **Core Team** member will review your changes, provide feedback and merge the pull request when ready.\nPlease let us know if you have any questions or need further assistance in the comments of your PR. Thank you for contributing to the BuilderBot docs and for being part of our community.\n## [File Structure](https://www.builderbot.app/en/</en/contribute#file-structure>)\nDocuments use file system routing. Each folder and file within `/pages`[](https://www.builderbot.app/en/<https:/github.com/codigoencasa/documentation/tree/master/src/pages>) represents a path segment. These segments are used to generate URL paths, navigation and breadcrumbs.\n```\nen\n├── showcases\n│  └── api-use.mdx\n└── ...\n\n```\nCopyCopied!\nEach folder prefix `en`, `es`, `pt` represents the language in which the content is represented.\n```\nen\n├── showcases\n│  └── api-use.mdx\n└── ...\nes\n├── showcases\n│  └── api-use.mdx\n└── ...\npt\n├── showcases\n│  └── api-use.mdx\n└── ...\n\n```\nCopyCopied!\n## [Required Fields](https://www.builderbot.app/en/</en/contribute#required-fields>)\nThe following fields are **required** :\nField| Description  \n---|---  \n`description`| The page's description, used in the `<meta name=\"description\">` tag for SEO.  \n`title`| The page's `<h1>` title, used for SEO and OG Images.  \n```\nexportconstdescription='In this guide, we will talk ...'\n# Community\n\n```\nCopyCopied!\n## [Code Blocks](https://www.builderbot.app/en/</en/contribute#code-blocks>)\nThe code blocks must contain a minimal working example that can be copied and pasted. This means that the code must be able to run without any additional configuration.\nFor example if we want to print TS or JS code\n### example.ts\n```\nconstflow=addKeyword('hello')\n.addAnswer(`What is your name?`, { capture:true },async (ctx, { state }) => {\nawaitstate.update({ name:ctx.body })\n  })\n.addAction(async (ctx, { state, flowDynamic }) => {\nconstname=state.get('name')\nawaitflowDynamic(`Your name is: ${name}`)\n  })\n}\n\n```\nCopyCopied!\nAlways run examples locally before committing them. This will ensure that the code is up-to-date and working.\n## [Language and Filename](https://www.builderbot.app/en/</en/contribute#language-and-filename>)\nCode blocks should have a header that includes the language and the `filename`. Add a `filename` prop to render a special Terminal icon that helps orientate users where to input the command. For example:\n```\n```ts {{ title: 'example.ts' }}\nconstflow=addKeyword('hello')\n.addAnswer(`What is your name?`, { capture:true },async (ctx, { state }) => {\nawaitstate.update({ name:ctx.body })\n  })\n.addAction(async (ctx, { state, flowDynamic }) => {\nconstname=state.get('name')\nawaitflowDynamic(`Your name is: ${name}`)\n  })\n}\n```\n\n```\nCopyCopied!\nMost examples in the docs are written in `tsx` and `jsx`, and a few in `bash`. However, you can use any supported language, here's the [full list](https://www.builderbot.app/en/<https:/github.com/shikijs/shiki/blob/main/docs/languages.md#all-languages>).\nWhen writing JavaScript code blocks, we use the following language and extension combinations.\nLanguage| Extension  \n---|---  \nJavaScript files| ```js| .js  \nTypeScript files| ```ts| .ts  \n## [Grouped code blocks](https://www.builderbot.app/en/</en/contribute#grouped-code-blocks>)\nSometimes we will need to represent a group of blocks of code grouped together even with different file names and in multiple languages we can do it in the following way\napp.tsprovider/index.tsdatabase/index.tsflow/index.tsflow/welcome.flow.tsservices/ai.ts\n```\nimport { createBot } from'@builderbot/bot';\nimport { flow } from\"./flow\";\nimport { database } from\"./database\";\nimport { provider } from\"./provider\";\nimport { ai } from\"./services/ai\";\nconstmain=async () => {\nawaitcreateBot({\n     flow,\n     provider,\n     database,\n   },\n     extensions: {\n     ai // Dependency AI \n   })\nprovider.initHttpServer(3000)\n}\nmain()\n\n```\nCopyCopied!\nThe template already provides internally a `<CodeGroup>` component that has the ability to interpret code blocks.\n```\n<CodeGroup>\n```ts {{ title: 'app.ts' }}\nimport { createBot } from'@builderbot/bot';\nimport { flow } from\"./flow\";\nimport { database } from\"./database\";\nimport { provider } from\"./provider\";\nimport { ai } from\"./services/ai\";\nconstmain=async () => {\nawaitcreateBot({\n     flow,\n     provider,\n     database,\n   },\n     extensions: {\n     ai // Dependency AI \n   })\nprovider.initHttpServer(3000)\n}\nmain()\n```\n```ts {{ title: 'provider/index.ts' }}\nimport { createProvider } from'@builderbot/bot';\nimport { BaileysProvider } from'@builderbot/provider-baileys';\nexportconstprovider=createProvider(BaileysProvider)\n```\n```ts {{ title: 'database/index.ts' }}\nexportconstdatabase=newMemoryDB()\n```\n```ts {{ title: 'flow/index.ts' }}\nimport { createFlow } from'@builderbot/bot';\nimport { flowWelcome } from\"./welcome.flow\";\nimport { byeFlow } from\"./bye.flow\";\nimport { mediaFlow } from\"./media.flow\";\n// other flows....\nexportconstflow=createFlow([flowWelcome, byeFlow, mediaFlow])\n```\n```ts {{ title: 'flow/welcome.flow.ts' }}\nimport { addKeyword, EVENTS } from'@builderbot/bot';\nexportconstflowWelcome=addKeyword(EVENTS.WELCOME)\n.addAction(async (ctx, {flowDynamic, extensions})=> {\nconst { ai } = extensions\nconsttalkWithGPT=ai.chat(ctx.body) // Dependency AI from app.ts\nawaitflowDynamic(talkWithGPT)\n })\n```\n```ts {{ title: 'services/ai.ts' }}\n// ....\nexportconstai=newAiService(process.env.OPEN_AI_KEY);\n```\n</CodeGroup>\n\n```\nCopyCopied!\n## [Guides](https://www.builderbot.app/en/</en/contribute#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/</plugins>)\n## [Resources](https://www.builderbot.app/en/</en/contribute#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [{"language": "language-json", "code": "{\n  \"files.associations\": {\n    \"*.mdx\": \"markdown\"\n  }\n}\n"}, {"language": "language-txt", "code": "en\n├── showcases\n│   └── api-use.mdx\n└── ...\n"}, {"language": "language-txt", "code": "en\n├── showcases\n│   └── api-use.mdx\n└── ...\nes\n├── showcases\n│   └── api-use.mdx\n└── ...\npt\n├── showcases\n│   └── api-use.mdx\n└── ...\n"}, {"language": "language-mdx", "code": "export const description = 'In this guide, we will talk ...'\n\n# Community\n"}, {"language": "language-ts", "code": "const flow = addKeyword('hello')\n    .addAnswer(`What is your name?`, { capture: true }, async (ctx, { state }) => {\n        await state.update({ name: ctx.body })\n    })\n    .addAction(async (ctx, { state, flowDynamic }) => {\n        const name = state.get('name')\n        await flowDynamic(`Your name is: ${name}`)\n    })\n}\n"}, {"language": "language-mdx", "code": "```ts {{ title: 'example.ts' }}\nconst flow = addKeyword('hello')\n    .addAnswer(`What is your name?`, { capture: true }, async (ctx, { state }) => {\n        await state.update({ name: ctx.body })\n    })\n    .addAction(async (ctx, { state, flowDynamic }) => {\n        const name = state.get('name')\n        await flowDynamic(`Your name is: ${name}`)\n    })\n}\n```\n"}, {"language": "language-ts", "code": "  import { createBot } from '@builderbot/bot';\n  import { flow } from \"./flow\";\n  import { database } from \"./database\";\n  import { provider } from \"./provider\";\n  import { ai } from \"./services/ai\";\n\n  const main = async () => {\n  await createBot({\n          flow,\n          provider,\n          database,\n      },\n          extensions: {\n          ai // Dependency AI \n      })\n\n  provider.initHttpServer(3000)\n}\nmain()\n"}, {"language": "language-mdx", "code": "<CodeGroup>\n```ts {{ title: 'app.ts' }}\n  import { createBot } from '@builderbot/bot';\n  import { flow } from \"./flow\";\n  import { database } from \"./database\";\n  import { provider } from \"./provider\";\n  import { ai } from \"./services/ai\";\n\n  const main = async () => {\n  await createBot({\n          flow,\n          provider,\n          database,\n      },\n          extensions: {\n          ai // Dependency AI \n      })\n\n  provider.initHttpServer(3000)\n}\nmain()\n```\n```ts {{ title: 'provider/index.ts' }}\n  import { createProvider } from '@builderbot/bot';\n  import { BaileysProvider } from '@builderbot/provider-baileys';\n\n  export const provider = createProvider(BaileysProvider)\n```\n```ts {{ title: 'database/index.ts' }}\n  export const database = new MemoryDB()\n```\n```ts {{ title: 'flow/index.ts' }}\n  import { createFlow } from '@builderbot/bot';\n  import { flowWelcome } from \"./welcome.flow\";\n  import { byeFlow } from \"./bye.flow\";\n  import { mediaFlow } from \"./media.flow\";\n  // other flows....\n\n  export const flow =  createFlow([flowWelcome, byeFlow, mediaFlow])\n```\n```ts {{ title: 'flow/welcome.flow.ts' }}\n  import { addKeyword, EVENTS } from '@builderbot/bot';\n\n  export const flowWelcome = addKeyword(EVENTS.WELCOME)\n  .addAction(async (ctx, {flowDynamic, extensions})=> {\n    const { ai } = extensions\n    const talkWithGPT = ai.chat(ctx.body) // Dependency AI from app.ts\n    await flowDynamic(talkWithGPT)\n  })\n```\n```ts {{ title: 'services/ai.ts' }}\n  // ....\n  export const ai = new AiService(process.env.OPEN_AI_KEY);\n```\n</CodeGroup>\n"}], "sections": []}, {"url": "https://www.builderbot.app/en/contribute/core", "title": "Core - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/contribute/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/contribute/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/contribute/</>)\n  * [Contribute](https://www.builderbot.app/en/contribute/</contribute>)\n  * [Course](https://www.builderbot.app/en/contribute/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/contribute/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/contribute/</>)\n  * [Documentation](https://www.builderbot.app/en/contribute/</en/contribute/core#>)\n  * [Support](https://www.builderbot.app/en/contribute/</en/contribute/core#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/contribute/</>)\n    * [Quickstart](https://www.builderbot.app/en/contribute/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/contribute/</concepts>)\n    * [Examples](https://www.builderbot.app/en/contribute/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/contribute/</add-functions>)\n    * [Context](https://www.builderbot.app/en/contribute/</context>)\n    * [Methods](https://www.builderbot.app/en/contribute/</methods>)\n    * [Events](https://www.builderbot.app/en/contribute/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/contribute/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/contribute/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/contribute/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/contribute/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/contribute/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/contribute/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/contribute/</deploy>)\n    * [Railway](https://www.builderbot.app/en/contribute/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/contribute/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/contribute/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/contribute/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/contribute/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/contribute/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/contribute/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/contribute/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/contribute/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/contribute/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/contribute/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/contribute/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/contribute/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/contribute/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/contribute/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/contribute/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/contribute/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/contribute/</contribute>)\n    * [Core](https://www.builderbot.app/en/contribute/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/contribute/</resources>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/contribute/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/contribute/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/contribute/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/contribute/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/contribute/</en/contribute/core#>)\n\n\n# Core\n<https://www.youtube.com/watch?v=PzuE18-j9sY>\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/contribute/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/contribute/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/contribute/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [], "sections": []}, {"url": "https://www.builderbot.app/en/resources", "title": "BuilderBot Brand Guidelines and Assets - BuilderBot.app Chatbot for Whatsapp, Telegram and more", "content": "[🚀 ¡Nuevo! builderbot cloud para No-code ¡Pruébalo gratis!](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n``Ctrl ``K``\n[![Logo](https://www.builderbot.app/_next/static/media/logo-v2.5d15651a.png)BuilderBot](https://www.builderbot.app/en/</>)\n  * [Contribute](https://www.builderbot.app/en/</contribute>)\n  * [Course](https://www.builderbot.app/en/<https:/app.codigoencasa.com/courses/curso-chatbot-whatsapp?refCode=LEIFER>)\n\n\n[Get started](https://www.builderbot.app/en/<https:/builderbot.cloud>)\n  * [API](https://www.builderbot.app/en/</>)\n  * [Documentation](https://www.builderbot.app/en/</en/resources#>)\n  * [Support](https://www.builderbot.app/en/</en/resources#>)\n  * ## Start here\n    * [Introduction](https://www.builderbot.app/en/</>)\n    * [Quickstart](https://www.builderbot.app/en/</quickstart>)\n    * [Concepts](https://www.builderbot.app/en/</concepts>)\n    * [Examples](https://www.builderbot.app/en/</uses-cases>)\n  * ## Basics\n    * [Functions](https://www.builderbot.app/en/</add-functions>)\n    * [Context](https://www.builderbot.app/en/</context>)\n    * [Methods](https://www.builderbot.app/en/</methods>)\n    * [Events](https://www.builderbot.app/en/</events>)\n  * ## Built-in\n    * [Databases](https://www.builderbot.app/en/</databases>)\n  * ## Providers\n    * [Meta](https://www.builderbot.app/en/</providers/meta>)\n    * [Twilio](https://www.builderbot.app/en/</providers/twilio>)\n    * [Baileys](https://www.builderbot.app/en/</providers/baileys>)\n    * [Venom](https://www.builderbot.app/en/</providers#venom>)\n    * [WPPConnect](https://www.builderbot.app/en/</providers#wpp-connect>)\n  * ## Deploy\n    * [Resume](https://www.builderbot.app/en/</deploy>)\n    * [Railway](https://www.builderbot.app/en/</deploy/railway>)\n    * [Docker](https://www.builderbot.app/en/</deploy/docker>)\n    * [VPS](https://www.builderbot.app/en/</deploy/vps>)\n  * ## Recipes\n    * [Queue limit](https://www.builderbot.app/en/</showcases/queue-limit>)\n    * [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\n    * [Fast Entries](https://www.builderbot.app/en/</showcases/fast-entires>)\n    * [Idle](https://www.builderbot.app/en/</showcases/idle-optional>)\n    * [Scheduled Reboots](https://www.builderbot.app/en/</showcases/docker-pm2>)\n    * [In-Out messages](https://www.builderbot.app/en/</showcases/event-in-out-messages>)\n    * [Reminder](https://www.builderbot.app/en/</showcases/cron-reminder>)\n    * [Forward to human](https://www.builderbot.app/en/</showcases/forward-conversation-to-human>)\n    * [GotoFlow Use](https://www.builderbot.app/en/</showcases/gotoflow-use>)\n    * [Multiple messages](https://www.builderbot.app/en/</showcases/multiple-messages>)\n  * ## Tutorials\n    * [Migrate from bot-whatsapp](https://www.builderbot.app/en/</tutorials/migrate-to-builderbot>)\n    * [API Rest](https://www.builderbot.app/en/</tutorials/api-use>)\n    * [Gemini](https://www.builderbot.app/en/</tutorials/chatbot-with-gemini>)\n    * [Langchain](https://www.builderbot.app/en/</tutorials/langchain>)\n  * ## Community Contribute\n    * [Documentation](https://www.builderbot.app/en/</contribute>)\n    * [Core](https://www.builderbot.app/en/</contribute/core>)\n    * [Brand and Logos](https://www.builderbot.app/en/</resources>)\n      * [Logo Pack](https://www.builderbot.app/en/</en/resources#logo-pack>)\n      * [Brand Assets](https://www.builderbot.app/en/</en/resources#brand-assets>)\n  * ## Plugins\n    * [Telegram](https://www.builderbot.app/en/</plugins/telegram>)\n    * [Shopify](https://www.builderbot.app/en/</plugins/shopify>)\n    * [Agents](https://www.builderbot.app/en/</plugins/agents>)\n    * [Langchain](https://www.builderbot.app/en/</plugins/langchain>)\n  * [Sign in](https://www.builderbot.app/en/</en/resources#>)\n\n\n# BuilderBot Brand Guidelines and Assets\nAvoid representing the BuilderBot brand in a way that:\n  * Implies partnership, sponsorship or endorsement.\n  * Makes the BuilderBot brand the most distinctive or prominent feature.\n  * Puts the BuilderBot brand in a negative context as part of a script or storyline. You must comply with our Terms of Use and Community Guidelines.\n\n\n### Keep the word BuilderBot consistent\nAvoid representing the BuilderBot brand in a way that:\n  * Keep the letters \"B\" in BuilderBot capitalized and in the same font size and style as the content surrounding it.\n  * If you offer an app, website or a product or service that uses the BuilderBot APIs or is otherwise compatible with or related to BuilderBot, you may only use BuilderBot to say that your app is \"for BuilderBot\" or that the name of your campaign is \"on BuilderBot\" in a descriptive manner.\n  * Don't modify, abbreviate or translate the word BuilderBot to a different language or by using non-English characters, or use any of our logos to replace it.\n  * Don't combine \"Builder\" or \"Bot\" with your own brand.\n  * Don't combine any part of the BuilderBot brand with a company name, other trademarks, or generic terms.\n\n\n### Distance BuilderBot from other platforms\n  * BuilderBot may be mentioned in a television commercial with its parent company or other affiliated companies.\n  * Don't mention other similar platforms in the same spot as BuilderBot, unless it's a general \"Find us on...\" call to action.\n  * If you create a hashtag that uses the word Builder or Bot, it shouldn't be used on other platforms and you shouldn't try to acquire or enforce rights over that hashtag.\n\n\n## [Logo Pack](https://www.builderbot.app/en/</en/resources#logo-pack>)\nI have read and accept the applicable guidelines and other terms for use.\n  * [Download BuilderBot Horizontal Logo (PNG)](https://www.builderbot.app/en/</assets/brand/logo-horizontal.png>)\n  * [Download BuilderBot Logo (SVG)](https://www.builderbot.app/en/</assets/brand/logo.svg>)\n  * [Download BuilderBot Logo (PNG)](https://www.builderbot.app/en/</assets/brand/logo-alone.png>)\n  * [Download BuilderBot Black Logo (PNG)](https://www.builderbot.app/en/</assets/brand/logo-black.png>)\n  * [Download BuilderBot White Logo (PNG)](https://www.builderbot.app/en/</assets/brand/logo-blanco.png>)\n  * [Download BuilderBot White Line Logo (PNG)](https://www.builderbot.app/en/</assets/brand/logo-blanco-line.png>)\n  * [Download BuilderBot Full Logo (PNG)](https://www.builderbot.app/en/</assets/brand/logo-full.png>)\n  * [Download BuilderBot Full Line Logo (PNG)](https://www.builderbot.app/en/</assets/brand/logo-full-line.png>)\n  * [Download BuilderBot Transparent Line Logo (PNG)](https://www.builderbot.app/en/</assets/brand/logo-transparent-line.png>)\n  * [Download BuilderBot Mini Logo (PNG)](https://www.builderbot.app/en/</assets/brand/mini-logo.png>)\n  * [Download BuilderBot Thumbnail Vector (PNG)](https://www.builderbot.app/en/</assets/brand/thumbnail-vector-30.png>)\n\n\n## [Brand Assets](https://www.builderbot.app/en/</en/resources#brand-assets>)\nHere's a color guide for inspiration that you can use\nTeal\n#20E3B2\nCopy\nDark Blue\n#0F172A\nCopy\nLight Gray\n#F1F5F9\nCopy\nDark Gray\n#64748B\nCopy\nWhite\n#FFFFFF\nCopy\n## [Guides](https://www.builderbot.app/en/</en/resources#guides>)\n### My first chatbot\nLearn how build your first chatbot in few minutes\n[Read more](https://www.builderbot.app/en/</uses-cases#my-first-bot>)\n### Concepts\nUnderstand the essential concepts for building bots\n[Read more](https://www.builderbot.app/en/</concepts>)\n### Add Functions\nThe key to learning how to write flows is add-functions.\n[Read more](https://www.builderbot.app/en/</add-functions>)\n### Plugins\nUnlimitate and start implementing the community plugins.\n[Read more](https://www.builderbot.app/en/</plugins>)\n## [Resources](https://www.builderbot.app/en/</en/resources#resources>)\n### [Modularize](https://www.builderbot.app/en/</showcases/modularize>)\nLearn how to modularise flows so that you can have a more maintainable bot.\n### [Send Message](https://www.builderbot.app/en/</tutorials/api-use>)\nHow to send a message via HTTP to start conversations, you can send multimedia as well.\n### [Dockerizer](https://www.builderbot.app/en/</deploy/docker>)\nA good practice is to dockerise your bots to make them more maintainable and effective.\n### [Events](https://www.builderbot.app/en/</events>)\nLearning about events will make us more fluent when creating chatbots.\nWas this page helpful?\nYesNo\n© Copyright 2025. All rights reserved.\n[Follow us on Twitter](https://www.builderbot.app/en/<https:/x.com/@LeiferMendez>)[Follow us on GitHub](https://www.builderbot.app/en/<https:/github.com/codigoencasa/bot-whatsapp>)[Join our Discord server](https://www.builderbot.app/en/<https:/link.codigoencasa.com/DISCORD>)\n", "code_blocks": [], "sections": []}]