import json
import os
import sys
import argparse
from typing import List, Dict, Any, Optional
from rich.console import Console
from rich.markdown import Markdown

# Intenta importar las bibliotecas necesarias para el embedding
try:
    from sentence_transformers import SentenceTransformer
    EMBEDDING_AVAILABLE = True
except ImportError:
    EMBEDDING_AVAILABLE = False

class KnowledgeBase:
    """
    Clase para cargar y consultar la base de conocimiento.
    """
    
    def __init__(self, knowledge_base_path: str):
        """
        Inicializa la base de conocimiento.
        
        Args:
            knowledge_base_path: Ruta al archivo JSON de la base de conocimiento
        """
        self.knowledge_base_path = knowledge_base_path
        self.knowledge_base = self._load_knowledge_base()
        self.embedding_model = None
        
        # Inicializar el modelo de embedding si está disponible
        if EMBEDDING_AVAILABLE:
            try:
                self.embedding_model = SentenceTransformer('paraphrase-MiniLM-L6-v2')
                print("Modelo de embedding cargado correctamente.")
            except Exception as e:
                print(f"Error al cargar el modelo de embedding: {str(e)}")
                self.embedding_model = None
    
    def _load_knowledge_base(self) -> List[Dict[str, Any]]:
        """Carga la base de conocimiento desde el archivo JSON."""
        try:
            with open(self.knowledge_base_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error al cargar la base de conocimiento: {str(e)}")
            return []
    
    def search(self, query: str, num_results: int = 3) -> List[Dict[str, Any]]:
        """
        Busca en la base de conocimiento utilizando palabras clave.
        
        Args:
            query: Consulta de búsqueda
            num_results: Número de resultados a devolver
            
        Returns:
            Lista de chunks relevantes
        """
        # Normalizar la consulta
        query_terms = [term.lower() for term in query.split() if len(term) > 2]
        results = []
        
        # Si no hay términos válidos, devolver lista vacía
        if not query_terms:
            return []
        
        # Buscar en cada página y chunk
        for page in self.knowledge_base:
            # Primero buscar en los metadatos de la página
            page_title_lower = page.get('title', '').lower()
            page_relevance = sum(2 for term in query_terms if term in page_title_lower)
            
            # Buscar en el contenido de la página
            content = page.get('content', '').lower()
            content_score = sum(1 for term in query_terms if term in content)
            
            # Puntuación total para la página
            total_score = page_relevance + content_score
            
            if total_score > 0:
                results.append({
                    'page': page,
                    'score': total_score,
                    'title': page['title'],
                    'url': page['url'],
                    'content': page['content']
                })
        
        # Ordenar por puntuación
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:num_results]

class AgentCoder:
    """
    Agente codificador que utiliza la base de conocimiento para generar código.
    """
    
    def __init__(self, knowledge_base_path: str):
        """
        Inicializa el agente codificador.
        
        Args:
            knowledge_base_path: Ruta al archivo JSON de la base de conocimiento
        """
        self.console = Console()
        self.knowledge_base = KnowledgeBase(knowledge_base_path)
    
    def generate_project_structure(self, project_name: str, project_description: str) -> Dict[str, Any]:
        """
        Genera la estructura de un proyecto basado en la descripción.
        
        Args:
            project_name: Nombre del proyecto
            project_description: Descripción del proyecto
            
        Returns:
            Estructura del proyecto
        """
        # Buscar información relevante en la base de conocimiento
        results = self.knowledge_base.search(f"create {project_description} project structure")
        
        # Estructura básica del proyecto
        project_structure = {
            "name": project_name,
            "description": project_description,
            "files": []
        }
        
        # Analizar los resultados para determinar la estructura del proyecto
        if results:
            # Aquí se implementaría la lógica para analizar los resultados y generar la estructura
            # Por ahora, usaremos una estructura básica basada en la documentación de BuilderBot
            project_structure["files"] = [
                {
                    "path": "app.ts",
                    "content": self._generate_app_ts(project_name)
                },
                {
                    "path": "provider/index.ts",
                    "content": self._generate_provider_index_ts()
                },
                {
                    "path": "database/index.ts",
                    "content": self._generate_database_index_ts()
                },
                {
                    "path": "flow/index.ts",
                    "content": self._generate_flow_index_ts()
                },
                {
                    "path": "flow/welcome.flow.ts",
                    "content": self._generate_welcome_flow_ts(project_description)
                }
            ]
        
        return project_structure
    
    def _generate_app_ts(self, project_name: str) -> str:
        """Genera el contenido del archivo app.ts"""
        return f"""
import {{ createBot }} from '@builderbot/bot';
import {{ flow }} from "./flow";
import {{ database }} from "./database";
import {{ provider }} from "./provider";

/**
 * {project_name} - Main application file
 */
const main = async () => {{
    await createBot({{
        flow,
        provider,
        database,
    }})
    
    provider.initHttpServer(3000)
}}

main()
"""
    
    def _generate_provider_index_ts(self) -> str:
        """Genera el contenido del archivo provider/index.ts"""
        return """
import { createProvider } from '@builderbot/bot';
import { BaileysProvider } from '@builderbot/provider-baileys';

export const provider = createProvider(BaileysProvider)
"""
    
    def _generate_database_index_ts(self) -> str:
        """Genera el contenido del archivo database/index.ts"""
        return """
import { MemoryDB } from '@builderbot/bot';

export const database = new MemoryDB()
"""
    
    def _generate_flow_index_ts(self) -> str:
        """Genera el contenido del archivo flow/index.ts"""
        return """
import { createFlow } from '@builderbot/bot';
import { flowWelcome } from "./welcome.flow";

export const flow = createFlow([
    flowWelcome,
    // Add more flows here
])
"""
    
    def _generate_welcome_flow_ts(self, description: str) -> str:
        """Genera el contenido del archivo flow/welcome.flow.ts"""
        return f"""
import {{ addKeyword, EVENTS }} from '@builderbot/bot';

/**
 * Welcome flow - {description}
 */
export const flowWelcome = addKeyword(EVENTS.WELCOME)
    .addAnswer('👋 ¡Hola! Bienvenido a nuestro chatbot.')
    .addAnswer('¿En qué puedo ayudarte hoy?')
    .addAction(async (ctx, {{ flowDynamic }}) => {{
        await flowDynamic('Estoy aquí para asistirte con {description}')
    }})
"""
    
    def create_project(self, project_name: str, project_description: str, output_dir: str) -> None:
        """
        Crea un proyecto basado en la descripción.
        
        Args:
            project_name: Nombre del proyecto
            project_description: Descripción del proyecto
            output_dir: Directorio de salida
        """
        self.console.print(f"[bold green]Generando proyecto: {project_name}[/bold green]")
        self.console.print(f"[cyan]Descripción: {project_description}[/cyan]")
        
        # Generar estructura del proyecto
        project_structure = self.generate_project_structure(project_name, project_description)
        
        # Crear directorio del proyecto
        project_dir = os.path.join(output_dir, project_name)
        os.makedirs(project_dir, exist_ok=True)
        
        # Crear archivos
        for file_info in project_structure["files"]:
            file_path = os.path.join(project_dir, file_info["path"])
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(file_info["content"].strip())
            
            self.console.print(f"[green]Creado archivo: {file_info['path']}[/green]")
        
        # Crear README.md
        readme_path = os.path.join(project_dir, "README.md")
        with open(readme_path, "w", encoding="utf-8") as f:
            f.write(f"""# {project_name}

{project_description}

## Instalación

```bash
npm install
```

## Ejecución

```bash
npm start
```

## Estructura del proyecto

- `app.ts`: Archivo principal de la aplicación
- `provider/`: Configuración del proveedor de mensajería
- `database/`: Configuración de la base de datos
- `flow/`: Flujos de conversación del chatbot
""")
        
        self.console.print(f"[green]Creado archivo: README.md[/green]")
        
        # Crear package.json
        package_json_path = os.path.join(project_dir, "package.json")
        with open(package_json_path, "w", encoding="utf-8") as f:
            f.write(f"""{{
  "name": "{project_name.lower().replace(' ', '-')}",
  "version": "1.0.0",
  "description": "{project_description}",
  "main": "app.js",
  "scripts": {{
    "start": "ts-node app.ts",
    "build": "tsc",
    "dev": "nodemon app.ts"
  }},
  "dependencies": {{
    "@builderbot/bot": "^1.0.0",
    "@builderbot/provider-baileys": "^1.0.0"
  }},
  "devDependencies": {{
    "nodemon": "^2.0.20",
    "ts-node": "^10.9.1",
    "typescript": "^4.9.4"
  }}
}}""")
        
        self.console.print(f"[green]Creado archivo: package.json[/green]")
        
        # Crear tsconfig.json
        tsconfig_path = os.path.join(project_dir, "tsconfig.json")
        with open(tsconfig_path, "w", encoding="utf-8") as f:
            f.write("""
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "outDir": "./dist",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  }
}
""")
        
        self.console.print(f"[green]Creado archivo: tsconfig.json[/green]")
        
        self.console.print(f"\n[bold green]¡Proyecto {project_name} creado con éxito en {project_dir}![/bold green]")
        self.console.print("[yellow]Para ejecutar el proyecto:[/yellow]")
        self.console.print("1. Navega al directorio del proyecto:")
        self.console.print(f"   [cyan]cd {project_dir}[/cyan]")
        self.console.print("2. Instala las dependencias:")
        self.console.print("   [cyan]npm install[/cyan]")
        self.console.print("3. Ejecuta el proyecto:")
        self.console.print("   [cyan]npm start[/cyan]")

def main():
    parser = argparse.ArgumentParser(description='Agente codificador basado en base de conocimiento')
    parser.add_argument('--kb', required=True, help='Ruta al archivo JSON de la base de conocimiento')
    parser.add_argument('--project-name', required=True, help='Nombre del proyecto a generar')
    parser.add_argument('--description', required=True, help='Descripción del proyecto')
    parser.add_argument('--output-dir', default='./projects', help='Directorio de salida para el proyecto')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.kb):
        print(f"Error: No se encontró el archivo de base de conocimiento en {args.kb}")
        sys.exit(1)
    
    # Crear directorio de salida si no existe
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Crear agente codificador
    agent = AgentCoder(args.kb)
    
    # Crear proyecto
    agent.create_project(args.project_name, args.description, args.output_dir)

if __name__ == "__main__":
    main()
